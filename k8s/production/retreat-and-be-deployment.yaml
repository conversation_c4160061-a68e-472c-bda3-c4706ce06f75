# Retreat And Be Production Deployment - Sprint 18
# Date: 10 Juin 2025
# Objectif: Déploiement production haute disponibilité

apiVersion: apps/v1
kind: Deployment
metadata:
  name: retreat-and-be-backend
  namespace: production
  labels:
    app: retreat-and-be-backend
    version: v1.0.0
    environment: production
    component: api
  annotations:
    deployment.kubernetes.io/revision: "1"
    description: "Backend API principal pour Retreat And Be"
spec:
  replicas: 5
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 1
  selector:
    matchLabels:
      app: retreat-and-be-backend
      environment: production
  template:
    metadata:
      labels:
        app: retreat-and-be-backend
        version: v1.0.0
        environment: production
        component: api
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: retreat-and-be-backend
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        runAsGroup: 1001
        fsGroup: 1001
      containers:
      - name: backend
        image: retreatandbe.azurecr.io/backend:v1.0.0
        imagePullPolicy: Always
        ports:
        - name: http
          containerPort: 3000
          protocol: TCP
        - name: metrics
          containerPort: 9090
          protocol: TCP
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "3000"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-credentials
              key: url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: jwt-secret
              key: secret
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: redis-credentials
              key: url
        - name: CORS_ORIGINS
          value: "https://retreatandbe.com,https://app.retreatandbe.com"
        - name: LOG_LEVEL
          value: "info"
        - name: METRICS_ENABLED
          value: "true"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: http
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
        volumeMounts:
        - name: uploads
          mountPath: /app/uploads
        - name: logs
          mountPath: /app/logs
        - name: config
          mountPath: /app/config
          readOnly: true
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
      volumes:
      - name: uploads
        persistentVolumeClaim:
          claimName: uploads-pvc
      - name: logs
        emptyDir: {}
      - name: config
        configMap:
          name: backend-config
      imagePullSecrets:
      - name: acr-secret
      nodeSelector:
        kubernetes.io/arch: amd64
        node-type: worker
      tolerations:
      - key: "node-type"
        operator: "Equal"
        value: "worker"
        effect: "NoSchedule"
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - retreat-and-be-backend
              topologyKey: kubernetes.io/hostname
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: kubernetes.io/arch
                operator: In
                values:
                - amd64
      terminationGracePeriodSeconds: 30

---
apiVersion: v1
kind: Service
metadata:
  name: retreat-and-be-backend-service
  namespace: production
  labels:
    app: retreat-and-be-backend
    environment: production
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "http"
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 80
    targetPort: http
    protocol: TCP
  - name: metrics
    port: 9090
    targetPort: metrics
    protocol: TCP
  selector:
    app: retreat-and-be-backend
    environment: production
  sessionAffinity: None

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: retreat-and-be-backend
  namespace: production
  labels:
    app: retreat-and-be-backend
    environment: production
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::ACCOUNT:role/retreat-and-be-backend-role

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: backend-config
  namespace: production
  labels:
    app: retreat-and-be-backend
    environment: production
data:
  app.json: |
    {
      "name": "Retreat And Be Backend",
      "version": "1.0.0",
      "environment": "production",
      "features": {
        "mobile_excellence": true,
        "performance_optimization": true,
        "security_enhanced": true,
        "monitoring": true
      },
      "limits": {
        "max_upload_size": "50MB",
        "rate_limit": "1000/hour",
        "concurrent_connections": 1000
      }
    }
  nginx.conf: |
    upstream backend {
        least_conn;
        server 127.0.0.1:3000 max_fails=3 fail_timeout=30s;
    }
    
    server {
        listen 80;
        server_name _;
        
        location /health {
            access_log off;
            proxy_pass http://backend;
        }
        
        location / {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: uploads-pvc
  namespace: production
  labels:
    app: retreat-and-be-backend
    environment: production
spec:
  accessModes:
  - ReadWriteMany
  storageClassName: fast-ssd
  resources:
    requests:
      storage: 100Gi

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: retreat-and-be-backend-netpol
  namespace: production
spec:
  podSelector:
    matchLabels:
      app: retreat-and-be-backend
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    - podSelector:
        matchLabels:
          app: prometheus
  - ports:
    - protocol: TCP
      port: 3000
    - protocol: TCP
      port: 9090
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: database
  - to:
    - namespaceSelector:
        matchLabels:
          name: redis
  - ports:
    - protocol: TCP
      port: 5432  # PostgreSQL
    - protocol: TCP
      port: 6379  # Redis
    - protocol: TCP
      port: 443   # HTTPS
    - protocol: TCP
      port: 53    # DNS
    - protocol: UDP
      port: 53    # DNS

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: retreat-and-be-backend-pdb
  namespace: production
spec:
  minAvailable: 3
  selector:
    matchLabels:
      app: retreat-and-be-backend
      environment: production
