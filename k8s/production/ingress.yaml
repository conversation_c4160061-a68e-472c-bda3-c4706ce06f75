# Production Ingress Configuration - Sprint 18
# Date: 10 Juin 2025
# Objectif: Load balancing et SSL termination production

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: retreat-and-be-ingress
  namespace: production
  labels:
    app: retreat-and-be
    environment: production
  annotations:
    # NGINX Ingress Controller
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    
    # SSL/TLS Configuration
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-protocols: "TLSv1.2 TLSv1.3"
    nginx.ingress.kubernetes.io/ssl-ciphers: "ECDHE-RSA-AES128-GCM-SHA256,ECDHE-RSA-AES256-GCM-SHA384"
    
    # Performance Optimizations
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-buffering: "on"
    nginx.ingress.kubernetes.io/proxy-buffer-size: "8k"
    
    # Rate Limiting
    nginx.ingress.kubernetes.io/rate-limit: "1000"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    nginx.ingress.kubernetes.io/rate-limit-connections: "100"
    
    # Security Headers
    nginx.ingress.kubernetes.io/configuration-snippet: |
      add_header X-Frame-Options "SAMEORIGIN" always;
      add_header X-Content-Type-Options "nosniff" always;
      add_header X-XSS-Protection "1; mode=block" always;
      add_header Referrer-Policy "strict-origin-when-cross-origin" always;
      add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; media-src 'self'; object-src 'none'; child-src 'self'; frame-ancestors 'self'; form-action 'self'; base-uri 'self';" always;
      add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    
    # Compression
    nginx.ingress.kubernetes.io/enable-compression: "true"
    nginx.ingress.kubernetes.io/compression-level: "6"
    nginx.ingress.kubernetes.io/compression-types: "text/plain,text/css,application/json,application/javascript,text/xml,application/xml,application/xml+rss,text/javascript"
    
    # Caching
    nginx.ingress.kubernetes.io/server-snippet: |
      location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Cache-Status "HIT";
      }
      
      location ~* \.(html|htm)$ {
        expires 1h;
        add_header Cache-Control "public, must-revalidate";
      }
    
    # Load Balancing
    nginx.ingress.kubernetes.io/upstream-hash-by: "$remote_addr"
    nginx.ingress.kubernetes.io/load-balance: "least_conn"
    
    # Monitoring
    nginx.ingress.kubernetes.io/enable-access-log: "true"
    nginx.ingress.kubernetes.io/access-log-path: "/var/log/nginx/access.log"
    nginx.ingress.kubernetes.io/error-log-path: "/var/log/nginx/error.log"

spec:
  tls:
  - hosts:
    - retreatandbe.com
    - www.retreatandbe.com
    - api.retreatandbe.com
    - app.retreatandbe.com
    secretName: retreatandbe-tls
  
  rules:
  # Main Website
  - host: retreatandbe.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: retreat-and-be-frontend-service
            port:
              number: 80
  
  # WWW Redirect
  - host: www.retreatandbe.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: retreat-and-be-frontend-service
            port:
              number: 80
  
  # API Backend
  - host: api.retreatandbe.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: retreat-and-be-backend-service
            port:
              number: 80
      - path: /health
        pathType: Exact
        backend:
          service:
            name: retreat-and-be-backend-service
            port:
              number: 80
      - path: /metrics
        pathType: Exact
        backend:
          service:
            name: retreat-and-be-backend-service
            port:
              number: 9090
  
  # Application Frontend
  - host: app.retreatandbe.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: retreat-and-be-app-service
            port:
              number: 80

---
# Frontend Service
apiVersion: v1
kind: Service
metadata:
  name: retreat-and-be-frontend-service
  namespace: production
  labels:
    app: retreat-and-be-frontend
    environment: production
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 80
    targetPort: 80
    protocol: TCP
  selector:
    app: retreat-and-be-frontend
    environment: production

---
# App Service
apiVersion: v1
kind: Service
metadata:
  name: retreat-and-be-app-service
  namespace: production
  labels:
    app: retreat-and-be-app
    environment: production
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 80
    targetPort: 3000
    protocol: TCP
  selector:
    app: retreat-and-be-app
    environment: production

---
# Certificate for SSL
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: retreatandbe-cert
  namespace: production
spec:
  secretName: retreatandbe-tls
  issuerRef:
    name: letsencrypt-prod
    kind: ClusterIssuer
  dnsNames:
  - retreatandbe.com
  - www.retreatandbe.com
  - api.retreatandbe.com
  - app.retreatandbe.com

---
# Ingress for Monitoring (Grafana, Prometheus)
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: monitoring-ingress
  namespace: monitoring
  labels:
    app: monitoring
    environment: production
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: monitoring-auth
    nginx.ingress.kubernetes.io/auth-realm: "Monitoring Access Required"
    nginx.ingress.kubernetes.io/whitelist-source-range: "10.0.0.0/8,**********/12,***********/16"
spec:
  tls:
  - hosts:
    - monitoring.retreatandbe.com
    - grafana.retreatandbe.com
    - prometheus.retreatandbe.com
    secretName: monitoring-tls
  
  rules:
  # Grafana
  - host: grafana.retreatandbe.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: grafana-service
            port:
              number: 3000
  
  # Prometheus
  - host: prometheus.retreatandbe.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: prometheus-service
            port:
              number: 9090
  
  # General Monitoring Dashboard
  - host: monitoring.retreatandbe.com
    http:
      paths:
      - path: /grafana
        pathType: Prefix
        backend:
          service:
            name: grafana-service
            port:
              number: 3000
      - path: /prometheus
        pathType: Prefix
        backend:
          service:
            name: prometheus-service
            port:
              number: 9090

---
# Basic Auth Secret for Monitoring
apiVersion: v1
kind: Secret
metadata:
  name: monitoring-auth
  namespace: monitoring
type: Opaque
data:
  auth: YWRtaW46JGFwcjEkSDY1dnVhNzAkLnRiTXhPbGRBVXNkdWkuRzFLVS8u  # admin:monitoring123
