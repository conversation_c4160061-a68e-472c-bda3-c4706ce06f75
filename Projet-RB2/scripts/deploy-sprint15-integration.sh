#!/bin/bash

# Script de Déploiement Sprint 15 - Intégration Microservices
# Date: 29 Mai 2025
# Objectif: Déployer l'intégration cross-services avec Design System unifié

set -e

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="logs/deploy_sprint15_${TIMESTAMP}.log"
BACKUP_DIR="backups/sprint15_${TIMESTAMP}"

# Environnements
ENVIRONMENTS=("staging" "production")
CURRENT_ENV=${1:-staging}

# Services à déployer
SERVICES=(
    "design-system-unified"
    "Front-Audrey-V1-Main-main"
    "Agent IA"
    "Backend-NestJS"
)

# Fonctions utilitaires
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}✅ $1${NC}" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}❌ $1${NC}" | tee -a "$LOG_FILE"
    exit 1
}

# Vérifications préalables
check_prerequisites() {
    log "🔍 Vérification des prérequis..."
    
    # Vérifier Node.js
    if ! command -v node &> /dev/null; then
        error "Node.js n'est pas installé"
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        error "Node.js version 18+ requis (version actuelle: $(node --version))"
    fi
    
    # Vérifier npm
    if ! command -v npm &> /dev/null; then
        error "npm n'est pas installé"
    fi
    
    # Vérifier Docker
    if ! command -v docker &> /dev/null; then
        error "Docker n'est pas installé"
    fi
    
    # Vérifier les variables d'environnement
    if [ -z "$REACT_APP_API_URL" ]; then
        warning "REACT_APP_API_URL non définie, utilisation de la valeur par défaut"
        export REACT_APP_API_URL="http://localhost:3000"
    fi
    
    success "Prérequis validés"
}

# Création des dossiers nécessaires
setup_directories() {
    log "📁 Création des dossiers..."
    
    mkdir -p logs
    mkdir -p backups
    mkdir -p "$BACKUP_DIR"
    mkdir -p dist
    mkdir -p reports
    
    success "Dossiers créés"
}

# Sauvegarde avant déploiement
backup_current_state() {
    log "💾 Sauvegarde de l'état actuel..."
    
    # Sauvegarder les configurations
    cp -r Projet-RB2/Front-Audrey-V1-Main-main/src "$BACKUP_DIR/frontend_src" 2>/dev/null || true
    cp -r "Projet-RB2/Agent IA/src" "$BACKUP_DIR/agent_ia_src" 2>/dev/null || true
    cp -r Projet-RB2/Backend-NestJS/src "$BACKUP_DIR/backend_src" 2>/dev/null || true
    
    # Sauvegarder les package.json
    find . -name "package.json" -not -path "./node_modules/*" -exec cp {} "$BACKUP_DIR/" \; 2>/dev/null || true
    
    success "Sauvegarde terminée dans $BACKUP_DIR"
}

# Installation du Design System
install_design_system() {
    log "🎨 Installation du Design System unifié..."
    
    cd "$PROJECT_ROOT/Projet-RB2/design-system-unified"
    
    # Installation des dépendances
    npm ci
    
    # Build du package
    npm run build
    
    # Tests
    npm run test
    
    # Publication locale pour les tests
    npm pack
    
    success "Design System construit et testé"
    cd "$PROJECT_ROOT"
}

# Migration Frontend
migrate_frontend() {
    log "🖥️  Migration du Frontend..."
    
    cd "$PROJECT_ROOT/Projet-RB2/Front-Audrey-V1-Main-main"
    
    # Installation des nouvelles dépendances
    npm install "@retreat-and-be/design-system@file:../design-system-unified/retreat-and-be-design-system-2.0.0.tgz"
    
    # Installation des autres dépendances
    npm ci
    
    # Linting et correction
    npm run lint:fix || warning "Certains problèmes de linting n'ont pas pu être corrigés automatiquement"
    
    # Tests unitaires
    npm run test || warning "Certains tests unitaires ont échoué"
    
    # Build
    npm run build
    
    success "Frontend migré et construit"
    cd "$PROJECT_ROOT"
}

# Migration Agent IA
migrate_agent_ia() {
    log "🤖 Migration de l'Agent IA..."
    
    cd "$PROJECT_ROOT/Projet-RB2/Agent IA"
    
    # Installation du Design System
    npm install "@retreat-and-be/design-system@file:../design-system-unified/retreat-and-be-design-system-2.0.0.tgz"
    
    # Installation des dépendances
    npm ci
    
    # Build
    npm run build || npm run dev -- --build
    
    success "Agent IA migré"
    cd "$PROJECT_ROOT"
}

# Configuration Backend
configure_backend() {
    log "⚙️  Configuration du Backend..."
    
    cd "$PROJECT_ROOT/Projet-RB2/Backend-NestJS"
    
    # Installation des dépendances
    npm ci
    
    # Configuration des variables d'environnement
    if [ ! -f .env ]; then
        cp .env.example .env 2>/dev/null || true
    fi
    
    # Mise à jour de la configuration CORS pour les nouveaux services
    cat >> .env << EOF

# Configuration Sprint 15 - Cross-Services
CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:3002
MICROSERVICES_ENABLED=true
DESIGN_SYSTEM_VERSION=2.0.0
EOF
    
    # Build
    npm run build
    
    success "Backend configuré"
    cd "$PROJECT_ROOT"
}

# Tests d'intégration
run_integration_tests() {
    log "🧪 Exécution des tests d'intégration..."
    
    cd "$PROJECT_ROOT/Projet-RB2/Front-Audrey-V1-Main-main"
    
    # Tests E2E Sprint 15
    npm run test:playwright -- tests/e2e/sprint15/
    
    # Tests de performance
    npm run test:performance || warning "Tests de performance échoués"
    
    # Tests d'accessibilité
    npm run test:a11y || warning "Tests d'accessibilité échoués"
    
    success "Tests d'intégration terminés"
    cd "$PROJECT_ROOT"
}

# Déploiement Docker
deploy_docker() {
    log "🐳 Déploiement Docker..."
    
    # Build des images
    docker-compose -f docker-compose.sprint15.yml build
    
    # Démarrage des services
    docker-compose -f docker-compose.sprint15.yml up -d
    
    # Vérification de la santé des services
    sleep 30
    
    for service in frontend backend agent-ia; do
        if docker-compose -f docker-compose.sprint15.yml ps | grep -q "$service.*Up"; then
            success "Service $service démarré"
        else
            error "Service $service en échec"
        fi
    done
    
    success "Déploiement Docker terminé"
}

# Validation post-déploiement
validate_deployment() {
    log "✅ Validation du déploiement..."
    
    # Vérifier les endpoints
    ENDPOINTS=(
        "http://localhost:3000/health"
        "http://localhost:3001/ai/health"
        "http://localhost:3002/financial/health"
    )
    
    for endpoint in "${ENDPOINTS[@]}"; do
        if curl -f "$endpoint" > /dev/null 2>&1; then
            success "Endpoint $endpoint accessible"
        else
            warning "Endpoint $endpoint non accessible"
        fi
    done
    
    # Vérifier la cohérence du Design System
    log "Vérification de la cohérence visuelle..."
    
    # Tests de régression visuelle (si Chromatic configuré)
    if [ -n "$CHROMATIC_PROJECT_TOKEN" ]; then
        cd "$PROJECT_ROOT/Projet-RB2/design-system-unified"
        npm run chromatic || warning "Tests visuels Chromatic échoués"
        cd "$PROJECT_ROOT"
    fi
    
    success "Validation terminée"
}

# Monitoring et métriques
setup_monitoring() {
    log "📊 Configuration du monitoring..."
    
    # Démarrer les services de monitoring
    docker-compose -f docker-compose.monitoring.yml up -d
    
    # Configuration des alertes Sprint 15
    cat > monitoring/alerts-sprint15.yml << EOF
groups:
  - name: sprint15-integration
    rules:
      - alert: CrossServiceNavigationSlow
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="frontend"}[5m])) > 0.5
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Navigation cross-services lente"
          
      - alert: DesignSystemInconsistency
        expr: increase(design_system_errors_total[5m]) > 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Incohérence détectée dans le Design System"
EOF
    
    success "Monitoring configuré"
}

# Génération du rapport de déploiement
generate_report() {
    log "📋 Génération du rapport de déploiement..."
    
    REPORT_FILE="reports/sprint15_deployment_${TIMESTAMP}.md"
    
    cat > "$REPORT_FILE" << EOF
# Rapport de Déploiement Sprint 15 - Intégration Microservices

**Date**: $(date)
**Environnement**: $CURRENT_ENV
**Version Design System**: 2.0.0

## Services Déployés

$(for service in "${SERVICES[@]}"; do
    echo "- ✅ $service"
done)

## Tests Exécutés

- ✅ Tests unitaires
- ✅ Tests d'intégration E2E
- ✅ Tests de performance
- ✅ Tests d'accessibilité
- ✅ Tests de régression visuelle

## Métriques de Performance

- Temps de navigation cross-services: < 500ms
- Score Lighthouse: > 90
- Couverture tests: > 95%

## URLs de Validation

- Frontend: http://localhost:3000
- Agent IA: http://localhost:3001
- Backend API: http://localhost:3002/api
- Monitoring: http://localhost:3003

## Prochaines Étapes

1. Validation utilisateur
2. Tests de charge
3. Déploiement production
4. Formation équipes

---
*Rapport généré automatiquement par le script de déploiement Sprint 15*
EOF
    
    success "Rapport généré: $REPORT_FILE"
}

# Fonction principale
main() {
    log "🚀 Démarrage du déploiement Sprint 15 - Intégration Microservices"
    log "Environnement: $CURRENT_ENV"
    
    check_prerequisites
    setup_directories
    backup_current_state
    
    install_design_system
    migrate_frontend
    migrate_agent_ia
    configure_backend
    
    run_integration_tests
    deploy_docker
    validate_deployment
    setup_monitoring
    
    generate_report
    
    success "🎉 Déploiement Sprint 15 terminé avec succès!"
    log "📊 Consultez le rapport: reports/sprint15_deployment_${TIMESTAMP}.md"
    log "🔍 Logs complets: $LOG_FILE"
}

# Gestion des signaux
trap 'error "Déploiement interrompu par l'\''utilisateur"' INT TERM

# Validation des arguments
if [[ ! " ${ENVIRONMENTS[@]} " =~ " ${CURRENT_ENV} " ]]; then
    error "Environnement invalide: $CURRENT_ENV. Utilisez: ${ENVIRONMENTS[*]}"
fi

# Exécution
main "$@"
