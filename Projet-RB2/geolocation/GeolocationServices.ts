/**
 * Geolocation Services - Sprint 17 Jour 2
 * Services de géolocalisation avancés avec contexte et recommandations
 */

interface GeolocationConfig {
  enableHighAccuracy: boolean;
  timeout: number;
  maximumAge: number;
  enableBackground: boolean;
  enableGeofencing: boolean;
  enableContextualServices: boolean;
  apiKey?: string;
  debugMode: boolean;
}

interface LocationData {
  latitude: number;
  longitude: number;
  accuracy: number;
  altitude?: number;
  altitudeAccuracy?: number;
  heading?: number;
  speed?: number;
  timestamp: number;
}

interface GeofenceRegion {
  id: string;
  name: string;
  center: { lat: number; lng: number };
  radius: number; // meters
  type: 'retreat_center' | 'city' | 'region' | 'custom';
  metadata: Record<string, any>;
  enabled: boolean;
}

interface ContextualRecommendation {
  id: string;
  type: 'retreat' | 'activity' | 'restaurant' | 'transport' | 'weather';
  title: string;
  description: string;
  distance: number; // meters
  rating: number;
  price?: number;
  imageUrl?: string;
  actionUrl: string;
  relevanceScore: number;
}

interface LocationContext {
  location: LocationData;
  address: PlaceDetails;
  weather: WeatherData;
  nearbyPlaces: PlaceDetails[];
  recommendations: ContextualRecommendation[];
  geofences: GeofenceRegion[];
  timezone: string;
  country: string;
  region: string;
}

interface PlaceDetails {
  placeId: string;
  name: string;
  address: string;
  types: string[];
  rating?: number;
  priceLevel?: number;
  photos?: string[];
  openingHours?: string[];
  website?: string;
  phoneNumber?: string;
  distance?: number;
}

interface WeatherData {
  temperature: number;
  humidity: number;
  description: string;
  icon: string;
  windSpeed: number;
  visibility: number;
  uvIndex: number;
  recommendation: string;
}

export class GeolocationServices {
  private config: GeolocationConfig;
  private currentLocation?: LocationData;
  private watchId?: number;
  private geofences: Map<string, GeofenceRegion> = new Map();
  private locationHistory: LocationData[] = [];
  private contextCache: Map<string, LocationContext> = new Map();
  private backgroundSync?: ServiceWorkerRegistration;

  constructor(config: Partial<GeolocationConfig> = {}) {
    this.config = {
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 300000, // 5 minutes
      enableBackground: true,
      enableGeofencing: true,
      enableContextualServices: true,
      debugMode: false,
      ...config
    };

    this.initialize();
  }

  /**
   * Initialisation des services de géolocalisation
   */
  private async initialize() {
    this.debug('Initializing Geolocation Services...');

    // Vérifier le support de la géolocalisation
    if (!this.isGeolocationSupported()) {
      console.warn('Geolocation not supported');
      return;
    }

    // Demander les permissions
    const permission = await this.requestPermissions();
    if (permission !== 'granted') {
      console.warn('Geolocation permission denied');
      return;
    }

    // Obtenir la position actuelle
    await this.getCurrentLocation();

    // Configurer les geofences par défaut
    this.setupDefaultGeofences();

    // Démarrer le suivi de position
    if (this.config.enableBackground) {
      this.startLocationTracking();
    }

    // Configurer la synchronisation en arrière-plan
    if (this.config.enableBackground && 'serviceWorker' in navigator) {
      this.setupBackgroundSync();
    }

    this.debug('Geolocation Services initialized successfully');
  }

  /**
   * Vérifier le support de la géolocalisation
   */
  private isGeolocationSupported(): boolean {
    return 'geolocation' in navigator;
  }

  /**
   * Demander les permissions de géolocalisation
   */
  private async requestPermissions(): Promise<PermissionState> {
    try {
      if ('permissions' in navigator) {
        const permission = await navigator.permissions.query({ name: 'geolocation' });
        return permission.state;
      }
      
      // Fallback: essayer d'obtenir la position pour tester la permission
      await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject, {
          timeout: 1000,
          maximumAge: Infinity
        });
      });
      
      return 'granted';
    } catch (error) {
      return 'denied';
    }
  }

  /**
   * Obtenir la position actuelle
   */
  public async getCurrentLocation(): Promise<LocationData | null> {
    return new Promise((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const locationData: LocationData = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            altitude: position.coords.altitude || undefined,
            altitudeAccuracy: position.coords.altitudeAccuracy || undefined,
            heading: position.coords.heading || undefined,
            speed: position.coords.speed || undefined,
            timestamp: position.timestamp
          };

          this.currentLocation = locationData;
          this.addToLocationHistory(locationData);
          
          this.debug('Current location obtained:', locationData);
          resolve(locationData);
        },
        (error) => {
          console.error('Failed to get current location:', error);
          reject(error);
        },
        {
          enableHighAccuracy: this.config.enableHighAccuracy,
          timeout: this.config.timeout,
          maximumAge: this.config.maximumAge
        }
      );
    });
  }

  /**
   * Démarrer le suivi de position
   */
  public startLocationTracking(): void {
    if (this.watchId) {
      this.stopLocationTracking();
    }

    this.watchId = navigator.geolocation.watchPosition(
      (position) => {
        const locationData: LocationData = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
          altitude: position.coords.altitude || undefined,
          altitudeAccuracy: position.coords.altitudeAccuracy || undefined,
          heading: position.coords.heading || undefined,
          speed: position.coords.speed || undefined,
          timestamp: position.timestamp
        };

        this.handleLocationUpdate(locationData);
      },
      (error) => {
        console.error('Location tracking error:', error);
      },
      {
        enableHighAccuracy: this.config.enableHighAccuracy,
        timeout: this.config.timeout,
        maximumAge: this.config.maximumAge
      }
    );

    this.debug('Location tracking started');
  }

  /**
   * Arrêter le suivi de position
   */
  public stopLocationTracking(): void {
    if (this.watchId) {
      navigator.geolocation.clearWatch(this.watchId);
      this.watchId = undefined;
      this.debug('Location tracking stopped');
    }
  }

  /**
   * Gérer les mises à jour de position
   */
  private async handleLocationUpdate(location: LocationData): Promise<void> {
    this.currentLocation = location;
    this.addToLocationHistory(location);

    // Vérifier les geofences
    if (this.config.enableGeofencing) {
      await this.checkGeofences(location);
    }

    // Mettre à jour le contexte
    if (this.config.enableContextualServices) {
      await this.updateLocationContext(location);
    }

    this.debug('Location updated:', location);
  }

  /**
   * Ajouter à l'historique des positions
   */
  private addToLocationHistory(location: LocationData): void {
    this.locationHistory.push(location);
    
    // Garder seulement les 100 dernières positions
    if (this.locationHistory.length > 100) {
      this.locationHistory = this.locationHistory.slice(-100);
    }
  }

  /**
   * Obtenir le contexte de localisation complet
   */
  public async getLocationContext(location?: LocationData): Promise<LocationContext | null> {
    const targetLocation = location || this.currentLocation;
    if (!targetLocation) {
      return null;
    }

    const cacheKey = `${targetLocation.latitude.toFixed(4)}_${targetLocation.longitude.toFixed(4)}`;
    
    // Vérifier le cache
    if (this.contextCache.has(cacheKey)) {
      const cached = this.contextCache.get(cacheKey)!;
      // Retourner le cache s'il a moins de 30 minutes
      if (Date.now() - cached.location.timestamp < 30 * 60 * 1000) {
        return cached;
      }
    }

    try {
      // Obtenir les détails de l'adresse
      const address = await this.reverseGeocode(targetLocation);
      
      // Obtenir la météo
      const weather = await this.getWeatherData(targetLocation);
      
      // Obtenir les lieux à proximité
      const nearbyPlaces = await this.getNearbyPlaces(targetLocation);
      
      // Générer des recommandations
      const recommendations = await this.generateRecommendations(targetLocation, nearbyPlaces);
      
      // Obtenir les geofences actives
      const activeGeofences = this.getActiveGeofences(targetLocation);

      const context: LocationContext = {
        location: targetLocation,
        address,
        weather,
        nearbyPlaces,
        recommendations,
        geofences: activeGeofences,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        country: address.address.split(',').pop()?.trim() || 'Unknown',
        region: address.address.split(',').slice(-2, -1)[0]?.trim() || 'Unknown'
      };

      // Mettre en cache
      this.contextCache.set(cacheKey, context);

      return context;

    } catch (error) {
      console.error('Failed to get location context:', error);
      return null;
    }
  }

  /**
   * Géocodage inverse
   */
  private async reverseGeocode(location: LocationData): Promise<PlaceDetails> {
    // Simulation - en production, utiliser une API comme Google Maps
    return {
      placeId: 'mock_place_id',
      name: 'Centre Ville',
      address: '123 Rue de la Paix, 75001 Paris, France',
      types: ['locality', 'political']
    };
  }

  /**
   * Obtenir les données météo
   */
  private async getWeatherData(location: LocationData): Promise<WeatherData> {
    // Simulation - en production, utiliser une API météo
    return {
      temperature: 22,
      humidity: 65,
      description: 'Ensoleillé',
      icon: 'sunny',
      windSpeed: 10,
      visibility: 10,
      uvIndex: 6,
      recommendation: 'Parfait pour une séance de yoga en extérieur !'
    };
  }

  /**
   * Obtenir les lieux à proximité
   */
  private async getNearbyPlaces(location: LocationData): Promise<PlaceDetails[]> {
    // Simulation - en production, utiliser Google Places API
    return [
      {
        placeId: 'retreat_center_1',
        name: 'Centre de Retraite Zen',
        address: '456 Avenue de la Sérénité',
        types: ['spa', 'health'],
        rating: 4.8,
        distance: 500,
        website: 'https://zen-retreat.com'
      },
      {
        placeId: 'yoga_studio_1',
        name: 'Studio Yoga Harmony',
        address: '789 Rue de la Méditation',
        types: ['gym', 'health'],
        rating: 4.6,
        distance: 300,
        website: 'https://yoga-harmony.com'
      }
    ];
  }

  /**
   * Générer des recommandations contextuelles
   */
  private async generateRecommendations(
    location: LocationData,
    nearbyPlaces: PlaceDetails[]
  ): Promise<ContextualRecommendation[]> {
    const recommendations: ContextualRecommendation[] = [];

    // Recommandations basées sur les lieux à proximité
    for (const place of nearbyPlaces) {
      if (place.types.includes('spa') || place.types.includes('health')) {
        recommendations.push({
          id: `rec_${place.placeId}`,
          type: 'retreat',
          title: `Découvrez ${place.name}`,
          description: `Centre de bien-être à ${place.distance}m de vous`,
          distance: place.distance || 0,
          rating: place.rating || 0,
          actionUrl: place.website || '#',
          relevanceScore: this.calculateRelevanceScore(place, location)
        });
      }
    }

    // Recommandations basées sur la météo
    const weather = await this.getWeatherData(location);
    if (weather.temperature > 20 && weather.description.includes('soleil')) {
      recommendations.push({
        id: 'weather_outdoor',
        type: 'activity',
        title: 'Yoga en plein air',
        description: `Profitez du beau temps (${weather.temperature}°C) pour une séance en extérieur`,
        distance: 0,
        rating: 5,
        actionUrl: '/activities/outdoor-yoga',
        relevanceScore: 0.9
      });
    }

    // Trier par score de pertinence
    return recommendations.sort((a, b) => b.relevanceScore - a.relevanceScore);
  }

  /**
   * Calculer le score de pertinence
   */
  private calculateRelevanceScore(place: PlaceDetails, location: LocationData): number {
    let score = 0;

    // Score basé sur la distance (plus proche = meilleur)
    const distance = place.distance || 1000;
    score += Math.max(0, 1 - distance / 2000); // 0-1 basé sur 2km max

    // Score basé sur la note
    if (place.rating) {
      score += place.rating / 5; // 0-1 basé sur 5 étoiles max
    }

    // Score basé sur le type
    if (place.types.includes('spa') || place.types.includes('health')) {
      score += 0.3;
    }

    return Math.min(1, score);
  }

  /**
   * Créer une geofence
   */
  public createGeofence(geofence: GeofenceRegion): void {
    this.geofences.set(geofence.id, geofence);
    this.debug(`Geofence created: ${geofence.name}`);
  }

  /**
   * Vérifier les geofences
   */
  private async checkGeofences(location: LocationData): Promise<void> {
    for (const [id, geofence] of this.geofences) {
      if (!geofence.enabled) continue;

      const distance = this.calculateDistance(
        location.latitude,
        location.longitude,
        geofence.center.lat,
        geofence.center.lng
      );

      const isInside = distance <= geofence.radius;
      const wasInside = this.wasInGeofence(id);

      if (isInside && !wasInside) {
        // Entrée dans la geofence
        await this.handleGeofenceEntry(geofence, location);
      } else if (!isInside && wasInside) {
        // Sortie de la geofence
        await this.handleGeofenceExit(geofence, location);
      }
    }
  }

  /**
   * Gérer l'entrée dans une geofence
   */
  private async handleGeofenceEntry(geofence: GeofenceRegion, location: LocationData): Promise<void> {
    this.debug(`Entered geofence: ${geofence.name}`);

    // Déclencher une notification
    if ('serviceWorker' in navigator) {
      const registration = await navigator.serviceWorker.ready;
      registration.showNotification(`Bienvenue à ${geofence.name}`, {
        body: 'Découvrez les activités disponibles dans cette zone',
        icon: '/icons/location.png',
        tag: `geofence_${geofence.id}`,
        data: {
          geofenceId: geofence.id,
          location
        }
      });
    }

    // Envoyer un événement
    this.dispatchLocationEvent('geofence_enter', { geofence, location });
  }

  /**
   * Gérer la sortie d'une geofence
   */
  private async handleGeofenceExit(geofence: GeofenceRegion, location: LocationData): Promise<void> {
    this.debug(`Exited geofence: ${geofence.name}`);

    // Envoyer un événement
    this.dispatchLocationEvent('geofence_exit', { geofence, location });
  }

  /**
   * Vérifier si l'utilisateur était dans une geofence
   */
  private wasInGeofence(geofenceId: string): boolean {
    // Implémentation simplifiée - en production, vérifier l'état précédent
    return false;
  }

  /**
   * Obtenir les geofences actives
   */
  private getActiveGeofences(location: LocationData): GeofenceRegion[] {
    const activeGeofences: GeofenceRegion[] = [];

    for (const geofence of this.geofences.values()) {
      if (!geofence.enabled) continue;

      const distance = this.calculateDistance(
        location.latitude,
        location.longitude,
        geofence.center.lat,
        geofence.center.lng
      );

      if (distance <= geofence.radius) {
        activeGeofences.push(geofence);
      }
    }

    return activeGeofences;
  }

  /**
   * Calculer la distance entre deux points
   */
  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371e3; // Rayon de la Terre en mètres
    const φ1 = lat1 * Math.PI / 180;
    const φ2 = lat2 * Math.PI / 180;
    const Δφ = (lat2 - lat1) * Math.PI / 180;
    const Δλ = (lon2 - lon1) * Math.PI / 180;

    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c;
  }

  /**
   * Configurer les geofences par défaut
   */
  private setupDefaultGeofences(): void {
    const defaultGeofences: GeofenceRegion[] = [
      {
        id: 'paris_center',
        name: 'Centre de Paris',
        center: { lat: 48.8566, lng: 2.3522 },
        radius: 5000, // 5km
        type: 'city',
        metadata: {
          description: 'Centre historique de Paris',
          activities: ['yoga', 'meditation', 'wellness']
        },
        enabled: true
      },
      {
        id: 'retreat_center_main',
        name: 'Centre de Retraite Principal',
        center: { lat: 48.8606, lng: 2.3376 },
        radius: 1000, // 1km
        type: 'retreat_center',
        metadata: {
          description: 'Notre centre de retraite principal',
          services: ['yoga', 'spa', 'meditation', 'nutrition']
        },
        enabled: true
      }
    ];

    defaultGeofences.forEach(geofence => {
      this.geofences.set(geofence.id, geofence);
    });
  }

  /**
   * Configurer la synchronisation en arrière-plan
   */
  private async setupBackgroundSync(): Promise<void> {
    try {
      this.backgroundSync = await navigator.serviceWorker.ready;
      
      // Enregistrer la synchronisation de géolocalisation
      await this.backgroundSync.sync.register('geolocation-sync');
      
      this.debug('Background sync configured');
    } catch (error) {
      console.error('Failed to setup background sync:', error);
    }
  }

  /**
   * Mettre à jour le contexte de localisation
   */
  private async updateLocationContext(location: LocationData): Promise<void> {
    try {
      const context = await this.getLocationContext(location);
      if (context) {
        this.dispatchLocationEvent('context_updated', { context });
      }
    } catch (error) {
      console.error('Failed to update location context:', error);
    }
  }

  /**
   * Dispatcher un événement de localisation
   */
  private dispatchLocationEvent(type: string, data: any): void {
    const event = new CustomEvent(`geolocation_${type}`, {
      detail: data
    });
    window.dispatchEvent(event);
  }

  /**
   * API publique
   */
  public async getNearbyRetreats(radius: number = 10000): Promise<ContextualRecommendation[]> {
    if (!this.currentLocation) {
      await this.getCurrentLocation();
    }

    if (!this.currentLocation) {
      return [];
    }

    const context = await this.getLocationContext(this.currentLocation);
    return context?.recommendations.filter(rec => 
      rec.type === 'retreat' && rec.distance <= radius
    ) || [];
  }

  public getLocationHistory(): LocationData[] {
    return [...this.locationHistory];
  }

  public getCurrentLocationData(): LocationData | null {
    return this.currentLocation || null;
  }

  private debug(message: string, data?: any): void {
    if (this.config.debugMode) {
      console.log(`[Geolocation] ${message}`, data);
    }
  }

  /**
   * Nettoyage
   */
  public destroy(): void {
    this.stopLocationTracking();
    this.geofences.clear();
    this.locationHistory = [];
    this.contextCache.clear();
  }
}

export default GeolocationServices;
