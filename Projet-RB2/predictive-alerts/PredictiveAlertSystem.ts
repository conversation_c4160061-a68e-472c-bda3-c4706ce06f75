/**
 * Predictive Alert System - Sprint 16 Jour 2
 * Système d'alertes prédictives basé sur ML pour détection d'anomalies
 */

import * as tf from '@tensorflow/tfjs';

export interface AlertRule {
  id: string;
  name: string;
  description: string;
  type: 'threshold' | 'anomaly' | 'trend' | 'pattern' | 'ml_prediction';
  severity: 'low' | 'medium' | 'high' | 'critical';
  metrics: string[];
  conditions: AlertCondition[];
  actions: AlertAction[];
  enabled: boolean;
  cooldownPeriod: number; // minutes
  tags: string[];
}

export interface AlertCondition {
  metric: string;
  operator: 'gt' | 'lt' | 'eq' | 'ne' | 'between' | 'outside' | 'anomaly' | 'trend';
  value?: number | [number, number];
  timeWindow: number; // minutes
  aggregation: 'avg' | 'sum' | 'min' | 'max' | 'count' | 'rate';
  threshold?: number;
  sensitivity?: number; // pour détection d'anomalies
}

export interface AlertAction {
  type: 'email' | 'slack' | 'webhook' | 'sms' | 'auto_remediation';
  target: string;
  template?: string;
  priority: number;
  retryPolicy?: {
    maxRetries: number;
    backoffMultiplier: number;
  };
}

export interface Alert {
  id: string;
  ruleId: string;
  ruleName: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'open' | 'acknowledged' | 'resolved' | 'suppressed';
  triggeredAt: Date;
  resolvedAt?: Date;
  acknowledgedAt?: Date;
  acknowledgedBy?: string;
  message: string;
  details: AlertDetails;
  actions: AlertActionResult[];
  tags: string[];
}

export interface AlertDetails {
  metric: string;
  currentValue: number;
  expectedValue?: number;
  threshold?: number;
  deviation?: number;
  confidence?: number;
  context: Record<string, any>;
  relatedMetrics: Array<{
    name: string;
    value: number;
    correlation: number;
  }>;
  predictions?: {
    nextHour: number;
    next24Hours: number;
    trend: 'increasing' | 'decreasing' | 'stable';
    confidence: number;
  };
}

export interface AlertActionResult {
  actionType: string;
  target: string;
  status: 'pending' | 'sent' | 'failed' | 'retrying';
  sentAt?: Date;
  error?: string;
  retryCount: number;
}

export interface AnomalyDetectionModel {
  name: string;
  type: 'isolation_forest' | 'autoencoder' | 'lstm' | 'statistical';
  model?: tf.LayersModel;
  parameters: Record<string, any>;
  trainingData: number[][];
  lastTrained: Date;
  accuracy: number;
}

export interface MetricTimeSeries {
  metric: string;
  timestamp: Date;
  value: number;
  tags: Record<string, string>;
}

export class PredictiveAlertSystem {
  private alertRules: Map<string, AlertRule> = new Map();
  private activeAlerts: Map<string, Alert> = new Map();
  private anomalyModels: Map<string, AnomalyDetectionModel> = new Map();
  private metricHistory: Map<string, MetricTimeSeries[]> = new Map();
  private monitoringInterval?: NodeJS.Timeout;
  private config: any;

  constructor(config: any = {}) {
    this.config = {
      monitoringInterval: 60000, // 1 minute
      anomalyThreshold: 0.95,
      maxHistorySize: 10000,
      enableMLPredictions: true,
      enableAutoRemediation: true,
      enableCorrelationAnalysis: true,
      debugMode: false,
      ...config
    };

    this.initialize();
  }

  /**
   * Initialisation du système d'alertes
   */
  private async initialize() {
    this.debug('Initializing Predictive Alert System...');

    // Charger les règles d'alerte existantes
    await this.loadAlertRules();

    // Initialiser les modèles d'anomalie
    await this.initializeAnomalyModels();

    // Démarrer le monitoring
    this.startMonitoring();

    // Configurer l'entraînement automatique des modèles
    this.setupAutoTraining();

    this.debug('Predictive Alert System initialized successfully');
  }

  /**
   * Créer une nouvelle règle d'alerte
   */
  public createAlertRule(rule: AlertRule): void {
    this.validateAlertRule(rule);
    this.alertRules.set(rule.id, rule);
    
    this.debug(`Alert rule created: ${rule.name}`);
  }

  /**
   * Ingestion de métriques
   */
  public ingestMetric(metric: MetricTimeSeries): void {
    const metricKey = this.getMetricKey(metric);
    
    if (!this.metricHistory.has(metricKey)) {
      this.metricHistory.set(metricKey, []);
    }

    const history = this.metricHistory.get(metricKey)!;
    history.push(metric);

    // Limiter la taille de l'historique
    if (history.length > this.config.maxHistorySize) {
      history.shift();
    }

    // Évaluer les règles d'alerte en temps réel
    this.evaluateAlertRules(metric);
  }

  /**
   * Évaluation des règles d'alerte
   */
  private async evaluateAlertRules(metric: MetricTimeSeries): Promise<void> {
    const metricKey = this.getMetricKey(metric);

    for (const [ruleId, rule] of this.alertRules) {
      if (!rule.enabled || !rule.metrics.includes(metric.metric)) {
        continue;
      }

      // Vérifier le cooldown
      if (this.isInCooldown(ruleId)) {
        continue;
      }

      try {
        const shouldAlert = await this.evaluateRule(rule, metric, metricKey);
        
        if (shouldAlert) {
          await this.triggerAlert(rule, metric, metricKey);
        }
      } catch (error) {
        this.debug(`Error evaluating rule ${rule.name}:`, error);
      }
    }
  }

  /**
   * Évaluation d'une règle spécifique
   */
  private async evaluateRule(
    rule: AlertRule,
    metric: MetricTimeSeries,
    metricKey: string
  ): Promise<boolean> {
    const history = this.metricHistory.get(metricKey) || [];

    for (const condition of rule.conditions) {
      const result = await this.evaluateCondition(condition, metric, history);
      
      if (!result) {
        return false; // Toutes les conditions doivent être vraies
      }
    }

    return true;
  }

  /**
   * Évaluation d'une condition
   */
  private async evaluateCondition(
    condition: AlertCondition,
    metric: MetricTimeSeries,
    history: MetricTimeSeries[]
  ): Promise<boolean> {
    const timeWindow = condition.timeWindow * 60 * 1000; // Convert to ms
    const cutoffTime = new Date(Date.now() - timeWindow);
    
    // Filtrer les données dans la fenêtre temporelle
    const windowData = history.filter(m => m.timestamp >= cutoffTime);
    
    if (windowData.length === 0) {
      return false;
    }

    // Calculer la valeur agrégée
    const aggregatedValue = this.aggregateValues(
      windowData.map(m => m.value),
      condition.aggregation
    );

    switch (condition.operator) {
      case 'gt':
        return aggregatedValue > (condition.value as number);
      
      case 'lt':
        return aggregatedValue < (condition.value as number);
      
      case 'eq':
        return Math.abs(aggregatedValue - (condition.value as number)) < 0.001;
      
      case 'ne':
        return Math.abs(aggregatedValue - (condition.value as number)) >= 0.001;
      
      case 'between':
        const [min, max] = condition.value as [number, number];
        return aggregatedValue >= min && aggregatedValue <= max;
      
      case 'outside':
        const [minOut, maxOut] = condition.value as [number, number];
        return aggregatedValue < minOut || aggregatedValue > maxOut;
      
      case 'anomaly':
        return await this.detectAnomaly(condition.metric, aggregatedValue, condition.sensitivity);
      
      case 'trend':
        return this.detectTrend(windowData, condition.threshold);
      
      default:
        return false;
    }
  }

  /**
   * Détection d'anomalie ML
   */
  private async detectAnomaly(
    metricName: string,
    value: number,
    sensitivity: number = 0.95
  ): Promise<boolean> {
    const model = this.anomalyModels.get(metricName);
    
    if (!model || !model.model) {
      // Fallback vers détection statistique
      return this.detectStatisticalAnomaly(metricName, value, sensitivity);
    }

    try {
      // Préparer les données d'entrée
      const inputTensor = tf.tensor2d([[value]]);
      
      // Prédiction du modèle
      const prediction = model.model.predict(inputTensor) as tf.Tensor;
      const anomalyScore = await prediction.data();
      
      inputTensor.dispose();
      prediction.dispose();

      return anomalyScore[0] > sensitivity;
      
    } catch (error) {
      this.debug(`ML anomaly detection failed for ${metricName}:`, error);
      return this.detectStatisticalAnomaly(metricName, value, sensitivity);
    }
  }

  /**
   * Détection d'anomalie statistique (fallback)
   */
  private detectStatisticalAnomaly(
    metricName: string,
    value: number,
    sensitivity: number
  ): boolean {
    const history = this.metricHistory.get(metricName) || [];
    
    if (history.length < 30) {
      return false; // Pas assez de données
    }

    const values = history.slice(-100).map(m => m.value); // Dernières 100 valeurs
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const variance = values.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / values.length;
    const stdDev = Math.sqrt(variance);

    // Z-score
    const zScore = Math.abs((value - mean) / stdDev);
    
    // Seuil basé sur la sensibilité (ex: 2.5 sigma pour 95% de confiance)
    const threshold = this.getZScoreThreshold(sensitivity);
    
    return zScore > threshold;
  }

  /**
   * Détection de tendance
   */
  private detectTrend(data: MetricTimeSeries[], threshold: number = 0.1): boolean {
    if (data.length < 5) {
      return false;
    }

    const values = data.map(m => m.value);
    const n = values.length;
    
    // Régression linéaire simple
    const sumX = (n * (n - 1)) / 2;
    const sumY = values.reduce((a, b) => a + b, 0);
    const sumXY = values.reduce((sum, y, i) => sum + i * y, 0);
    const sumX2 = (n * (n - 1) * (2 * n - 1)) / 6;
    
    const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    
    // Normaliser par la moyenne pour obtenir un pourcentage de changement
    const avgValue = sumY / n;
    const normalizedSlope = Math.abs(slope / avgValue);
    
    return normalizedSlope > threshold;
  }

  /**
   * Déclencher une alerte
   */
  private async triggerAlert(
    rule: AlertRule,
    metric: MetricTimeSeries,
    metricKey: string
  ): Promise<void> {
    const alertId = this.generateAlertId();
    
    // Analyser les corrélations avec d'autres métriques
    const relatedMetrics = this.config.enableCorrelationAnalysis
      ? await this.analyzeCorrelations(metric)
      : [];

    // Générer des prédictions
    const predictions = this.config.enableMLPredictions
      ? await this.generatePredictions(metricKey)
      : undefined;

    const alert: Alert = {
      id: alertId,
      ruleId: rule.id,
      ruleName: rule.name,
      severity: rule.severity,
      status: 'open',
      triggeredAt: new Date(),
      message: this.generateAlertMessage(rule, metric),
      details: {
        metric: metric.metric,
        currentValue: metric.value,
        context: { ...metric.tags },
        relatedMetrics,
        predictions
      },
      actions: [],
      tags: rule.tags
    };

    this.activeAlerts.set(alertId, alert);

    // Exécuter les actions
    await this.executeAlertActions(alert, rule.actions);

    this.debug(`Alert triggered: ${rule.name} - ${alert.message}`);
  }

  /**
   * Exécuter les actions d'alerte
   */
  private async executeAlertActions(alert: Alert, actions: AlertAction[]): Promise<void> {
    for (const action of actions.sort((a, b) => a.priority - b.priority)) {
      const actionResult: AlertActionResult = {
        actionType: action.type,
        target: action.target,
        status: 'pending',
        retryCount: 0
      };

      alert.actions.push(actionResult);

      try {
        await this.executeAction(action, alert);
        actionResult.status = 'sent';
        actionResult.sentAt = new Date();
      } catch (error) {
        actionResult.status = 'failed';
        actionResult.error = error.message;
        
        // Retry logic
        if (action.retryPolicy && actionResult.retryCount < action.retryPolicy.maxRetries) {
          setTimeout(() => {
            this.retryAction(action, alert, actionResult);
          }, this.calculateBackoffDelay(actionResult.retryCount, action.retryPolicy.backoffMultiplier));
        }
      }
    }
  }

  /**
   * Exécuter une action spécifique
   */
  private async executeAction(action: AlertAction, alert: Alert): Promise<void> {
    switch (action.type) {
      case 'email':
        await this.sendEmailAlert(action.target, alert, action.template);
        break;
      
      case 'slack':
        await this.sendSlackAlert(action.target, alert, action.template);
        break;
      
      case 'webhook':
        await this.sendWebhookAlert(action.target, alert);
        break;
      
      case 'sms':
        await this.sendSMSAlert(action.target, alert);
        break;
      
      case 'auto_remediation':
        if (this.config.enableAutoRemediation) {
          await this.executeAutoRemediation(action.target, alert);
        }
        break;
      
      default:
        throw new Error(`Unknown action type: ${action.type}`);
    }
  }

  /**
   * Analyser les corrélations
   */
  private async analyzeCorrelations(metric: MetricTimeSeries): Promise<Array<{
    name: string;
    value: number;
    correlation: number;
  }>> {
    const correlations: Array<{ name: string; value: number; correlation: number }> = [];
    
    // Analyser les corrélations avec d'autres métriques récentes
    for (const [otherMetricKey, otherHistory] of this.metricHistory) {
      if (otherMetricKey === this.getMetricKey(metric)) {
        continue;
      }

      const correlation = this.calculateCorrelation(
        this.metricHistory.get(this.getMetricKey(metric)) || [],
        otherHistory
      );

      if (Math.abs(correlation) > 0.5) { // Corrélation significative
        const latestValue = otherHistory[otherHistory.length - 1]?.value || 0;
        correlations.push({
          name: otherMetricKey,
          value: latestValue,
          correlation
        });
      }
    }

    return correlations.sort((a, b) => Math.abs(b.correlation) - Math.abs(a.correlation));
  }

  /**
   * Générer des prédictions
   */
  private async generatePredictions(metricKey: string): Promise<{
    nextHour: number;
    next24Hours: number;
    trend: 'increasing' | 'decreasing' | 'stable';
    confidence: number;
  } | undefined> {
    const history = this.metricHistory.get(metricKey);
    
    if (!history || history.length < 10) {
      return undefined;
    }

    // Prédiction simple basée sur la tendance
    const recentValues = history.slice(-10).map(m => m.value);
    const trend = this.calculateTrend(recentValues);
    const lastValue = recentValues[recentValues.length - 1];

    return {
      nextHour: lastValue + trend,
      next24Hours: lastValue + trend * 24,
      trend: trend > 0.01 ? 'increasing' : trend < -0.01 ? 'decreasing' : 'stable',
      confidence: 0.7 // Confiance basique
    };
  }

  /**
   * Utilitaires
   */
  private getMetricKey(metric: MetricTimeSeries): string {
    const tagString = Object.entries(metric.tags)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([k, v]) => `${k}:${v}`)
      .join(',');
    
    return `${metric.metric}${tagString ? `{${tagString}}` : ''}`;
  }

  private aggregateValues(values: number[], aggregation: string): number {
    switch (aggregation) {
      case 'avg':
        return values.reduce((a, b) => a + b, 0) / values.length;
      case 'sum':
        return values.reduce((a, b) => a + b, 0);
      case 'min':
        return Math.min(...values);
      case 'max':
        return Math.max(...values);
      case 'count':
        return values.length;
      case 'rate':
        return values.length > 1 ? (values[values.length - 1] - values[0]) / (values.length - 1) : 0;
      default:
        return values[values.length - 1] || 0;
    }
  }

  private getZScoreThreshold(sensitivity: number): number {
    // Conversion approximative de la sensibilité en seuil Z-score
    const thresholds: Record<number, number> = {
      0.90: 1.645,
      0.95: 1.96,
      0.99: 2.576,
      0.999: 3.291
    };

    return thresholds[sensitivity] || 2.0;
  }

  private calculateCorrelation(series1: MetricTimeSeries[], series2: MetricTimeSeries[]): number {
    // Implémentation simplifiée du coefficient de corrélation de Pearson
    if (series1.length !== series2.length || series1.length < 2) {
      return 0;
    }

    const values1 = series1.map(m => m.value);
    const values2 = series2.map(m => m.value);

    const mean1 = values1.reduce((a, b) => a + b, 0) / values1.length;
    const mean2 = values2.reduce((a, b) => a + b, 0) / values2.length;

    let numerator = 0;
    let sum1 = 0;
    let sum2 = 0;

    for (let i = 0; i < values1.length; i++) {
      const diff1 = values1[i] - mean1;
      const diff2 = values2[i] - mean2;
      numerator += diff1 * diff2;
      sum1 += diff1 * diff1;
      sum2 += diff2 * diff2;
    }

    const denominator = Math.sqrt(sum1 * sum2);
    return denominator === 0 ? 0 : numerator / denominator;
  }

  private calculateTrend(values: number[]): number {
    if (values.length < 2) return 0;
    
    const n = values.length;
    const sumX = (n * (n - 1)) / 2;
    const sumY = values.reduce((a, b) => a + b, 0);
    const sumXY = values.reduce((sum, y, i) => sum + i * y, 0);
    const sumX2 = (n * (n - 1) * (2 * n - 1)) / 6;
    
    return (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
  }

  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateAlertMessage(rule: AlertRule, metric: MetricTimeSeries): string {
    return `${rule.name}: ${metric.metric} = ${metric.value}`;
  }

  private debug(message: string, data?: any) {
    if (this.config.debugMode) {
      console.log(`[Predictive Alerts] ${message}`, data);
    }
  }

  // Méthodes stub pour les fonctionnalités avancées
  private validateAlertRule(rule: AlertRule): void { /* Implementation */ }
  private async loadAlertRules(): Promise<void> { /* Implementation */ }
  private async initializeAnomalyModels(): Promise<void> { /* Implementation */ }
  private startMonitoring(): void { /* Implementation */ }
  private setupAutoTraining(): void { /* Implementation */ }
  private isInCooldown(ruleId: string): boolean { return false; }
  private async sendEmailAlert(target: string, alert: Alert, template?: string): Promise<void> { /* Implementation */ }
  private async sendSlackAlert(target: string, alert: Alert, template?: string): Promise<void> { /* Implementation */ }
  private async sendWebhookAlert(target: string, alert: Alert): Promise<void> { /* Implementation */ }
  private async sendSMSAlert(target: string, alert: Alert): Promise<void> { /* Implementation */ }
  private async executeAutoRemediation(target: string, alert: Alert): Promise<void> { /* Implementation */ }
  private async retryAction(action: AlertAction, alert: Alert, actionResult: AlertActionResult): Promise<void> { /* Implementation */ }
  private calculateBackoffDelay(retryCount: number, multiplier: number): number { return 1000 * Math.pow(multiplier, retryCount); }

  /**
   * API publique pour gestion des alertes
   */
  public getActiveAlerts(): Alert[] {
    return Array.from(this.activeAlerts.values());
  }

  public acknowledgeAlert(alertId: string, userId: string): void {
    const alert = this.activeAlerts.get(alertId);
    if (alert) {
      alert.status = 'acknowledged';
      alert.acknowledgedAt = new Date();
      alert.acknowledgedBy = userId;
    }
  }

  public resolveAlert(alertId: string): void {
    const alert = this.activeAlerts.get(alertId);
    if (alert) {
      alert.status = 'resolved';
      alert.resolvedAt = new Date();
    }
  }

  /**
   * Nettoyage
   */
  public destroy() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }
    
    this.anomalyModels.forEach(model => {
      if (model.model) {
        model.model.dispose();
      }
    });
    
    this.alertRules.clear();
    this.activeAlerts.clear();
    this.anomalyModels.clear();
    this.metricHistory.clear();
  }
}

export default PredictiveAlertSystem;
