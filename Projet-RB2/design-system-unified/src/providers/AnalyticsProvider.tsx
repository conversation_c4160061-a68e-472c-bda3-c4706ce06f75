/**
 * Analytics Provider - Sprint 16
 * Provider pour intégration analytics cross-services avec le Design System
 */

import React, { createContext, useContext, useEffect, useRef, ReactNode } from 'react';
import AnalyticsSDK, { AnalyticsConfig, PredictionResponse } from '../../../analytics-sdk/src/AnalyticsSDK';

interface AnalyticsContextValue {
  analytics: AnalyticsSDK | null;
  track: (eventName: string, properties?: Record<string, any>) => void;
  page: (pageName?: string, properties?: Record<string, any>) => void;
  identify: (userId: string, traits?: Record<string, any>) => void;
  conversion: (conversionType: string, value?: number, properties?: Record<string, any>) => void;
  error: (error: Error, context?: Record<string, any>) => void;
  requestPredictions: () => Promise<PredictionResponse | null>;
  isInitialized: boolean;
}

interface AnalyticsProviderProps {
  config: AnalyticsConfig;
  children: ReactNode;
  enableAutoComponentTracking?: boolean;
  enablePerformanceMonitoring?: boolean;
  enableUserJourneyTracking?: boolean;
}

const AnalyticsContext = createContext<AnalyticsContextValue | null>(null);

export const AnalyticsProvider: React.FC<AnalyticsProviderProps> = ({
  config,
  children,
  enableAutoComponentTracking = true,
  enablePerformanceMonitoring = true,
  enableUserJourneyTracking = true
}) => {
  const analyticsRef = useRef<AnalyticsSDK | null>(null);
  const [isInitialized, setIsInitialized] = React.useState(false);

  useEffect(() => {
    // Initialiser le SDK Analytics
    const initializeAnalytics = async () => {
      try {
        analyticsRef.current = new AnalyticsSDK(config);
        setIsInitialized(true);

        // Tracking automatique des composants si activé
        if (enableAutoComponentTracking) {
          setupComponentTracking();
        }

        // Monitoring de performance si activé
        if (enablePerformanceMonitoring) {
          setupPerformanceMonitoring();
        }

        // Tracking du parcours utilisateur si activé
        if (enableUserJourneyTracking) {
          setupUserJourneyTracking();
        }

        console.log('Analytics Provider initialized successfully');
      } catch (error) {
        console.error('Failed to initialize Analytics Provider:', error);
      }
    };

    initializeAnalytics();

    // Cleanup
    return () => {
      if (analyticsRef.current) {
        analyticsRef.current.destroy();
      }
    };
  }, [config, enableAutoComponentTracking, enablePerformanceMonitoring, enableUserJourneyTracking]);

  /**
   * Configuration du tracking automatique des composants
   */
  const setupComponentTracking = () => {
    // Observer pour les composants du Design System
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as HTMLElement;
            
            // Tracking des composants avec data-analytics
            if (element.dataset.analytics) {
              track('component_rendered', {
                componentType: element.dataset.analytics,
                componentId: element.id,
                timestamp: Date.now()
              });
            }

            // Tracking des modales
            if (element.getAttribute('role') === 'dialog') {
              track('modal_opened', {
                modalId: element.id,
                modalClass: element.className
              });
            }
          }
        });
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  };

  /**
   * Configuration du monitoring de performance
   */
  const setupPerformanceMonitoring = () => {
    // Monitoring des re-renders React
    if (typeof window !== 'undefined' && (window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__) {
      const hook = (window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__;
      
      hook.onCommitFiberRoot = (id: any, root: any) => {
        const renderTime = performance.now();
        track('react_render', {
          rootId: id,
          renderTime,
          fiberCount: root.current?.child ? 1 : 0
        });
      };
    }

    // Monitoring des interactions utilisateur
    const interactionObserver = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.entryType === 'event') {
          track('interaction_performance', {
            eventType: (entry as any).name,
            duration: entry.duration,
            startTime: entry.startTime
          });
        }
      });
    });

    try {
      interactionObserver.observe({ entryTypes: ['event'] });
    } catch (e) {
      // Event timing API not supported
    }
  };

  /**
   * Configuration du tracking du parcours utilisateur
   */
  const setupUserJourneyTracking = () => {
    let userJourney: string[] = [];
    let sessionStartTime = Date.now();

    // Tracking des changements de page
    const trackPageChange = () => {
      const currentPage = window.location.pathname;
      userJourney.push(currentPage);

      track('user_journey_step', {
        step: userJourney.length,
        page: currentPage,
        journey: userJourney.slice(-5), // Garder les 5 dernières pages
        timeFromStart: Date.now() - sessionStartTime
      });

      // Analyser les patterns de navigation
      if (userJourney.length >= 3) {
        const pattern = userJourney.slice(-3).join(' -> ');
        track('navigation_pattern', {
          pattern,
          frequency: userJourney.filter(page => page === currentPage).length
        });
      }
    };

    // Observer les changements d'URL
    let currentUrl = window.location.href;
    const urlObserver = new MutationObserver(() => {
      if (window.location.href !== currentUrl) {
        currentUrl = window.location.href;
        trackPageChange();
      }
    });

    urlObserver.observe(document.body, {
      childList: true,
      subtree: true
    });

    // Tracking initial
    trackPageChange();
  };

  /**
   * Fonctions de tracking
   */
  const track = (eventName: string, properties: Record<string, any> = {}) => {
    if (analyticsRef.current) {
      // Ajouter contexte Design System
      const enhancedProperties = {
        ...properties,
        designSystemVersion: '2.0.0',
        service: config.service,
        timestamp: Date.now()
      };

      analyticsRef.current.track(eventName, enhancedProperties);
    }
  };

  const page = (pageName?: string, properties: Record<string, any> = {}) => {
    if (analyticsRef.current) {
      analyticsRef.current.page(pageName, {
        ...properties,
        designSystemVersion: '2.0.0',
        service: config.service
      });
    }
  };

  const identify = (userId: string, traits: Record<string, any> = {}) => {
    if (analyticsRef.current) {
      analyticsRef.current.identify(userId, traits);
    }
  };

  const conversion = (conversionType: string, value?: number, properties: Record<string, any> = {}) => {
    if (analyticsRef.current) {
      analyticsRef.current.conversion(conversionType, value, {
        ...properties,
        service: config.service
      });
    }
  };

  const error = (error: Error, context: Record<string, any> = {}) => {
    if (analyticsRef.current) {
      analyticsRef.current.error(error, {
        ...context,
        designSystemVersion: '2.0.0',
        service: config.service
      });
    }
  };

  const requestPredictions = async (): Promise<PredictionResponse | null> => {
    if (analyticsRef.current) {
      return await analyticsRef.current.requestPredictions();
    }
    return null;
  };

  const contextValue: AnalyticsContextValue = {
    analytics: analyticsRef.current,
    track,
    page,
    identify,
    conversion,
    error,
    requestPredictions,
    isInitialized
  };

  return (
    <AnalyticsContext.Provider value={contextValue}>
      {children}
    </AnalyticsContext.Provider>
  );
};

/**
 * Hook pour utiliser les analytics
 */
export const useAnalytics = (): AnalyticsContextValue => {
  const context = useContext(AnalyticsContext);
  
  if (!context) {
    throw new Error('useAnalytics must be used within an AnalyticsProvider');
  }
  
  return context;
};

/**
 * HOC pour tracking automatique des composants
 */
export function withAnalytics<T extends object>(
  Component: React.ComponentType<T>,
  componentName: string,
  trackingOptions: {
    trackRender?: boolean;
    trackProps?: boolean;
    trackInteractions?: boolean;
  } = {}
) {
  const {
    trackRender = true,
    trackProps = false,
    trackInteractions = true
  } = trackingOptions;

  const WrappedComponent = React.forwardRef<any, T>((props, ref) => {
    const { track } = useAnalytics();
    const renderCount = useRef(0);

    useEffect(() => {
      if (trackRender) {
        renderCount.current++;
        track('component_render', {
          componentName,
          renderCount: renderCount.current,
          props: trackProps ? props : undefined
        });
      }
    });

    const enhancedProps = trackInteractions ? {
      ...props,
      'data-analytics': componentName,
      onClick: (event: any) => {
        track('component_click', {
          componentName,
          elementType: event.target.tagName,
          timestamp: Date.now()
        });
        
        // Appeler l'onClick original s'il existe
        if ((props as any).onClick) {
          (props as any).onClick(event);
        }
      }
    } : props;

    return <Component ref={ref} {...enhancedProps as T} />;
  });

  WrappedComponent.displayName = `withAnalytics(${componentName})`;
  
  return WrappedComponent;
}

/**
 * Hook pour tracking des interactions utilisateur
 */
export const useInteractionTracking = (componentName: string) => {
  const { track } = useAnalytics();

  const trackClick = (elementType: string, additionalData: Record<string, any> = {}) => {
    track('interaction_click', {
      componentName,
      elementType,
      ...additionalData,
      timestamp: Date.now()
    });
  };

  const trackHover = (duration: number, additionalData: Record<string, any> = {}) => {
    track('interaction_hover', {
      componentName,
      duration,
      ...additionalData,
      timestamp: Date.now()
    });
  };

  const trackFocus = (additionalData: Record<string, any> = {}) => {
    track('interaction_focus', {
      componentName,
      ...additionalData,
      timestamp: Date.now()
    });
  };

  return {
    trackClick,
    trackHover,
    trackFocus
  };
};

/**
 * Hook pour tracking des conversions
 */
export const useConversionTracking = () => {
  const { conversion, track } = useAnalytics();

  const trackConversion = (type: string, value?: number, additionalData: Record<string, any> = {}) => {
    conversion(type, value, additionalData);
  };

  const trackFunnelStep = (step: string, stepNumber: number, additionalData: Record<string, any> = {}) => {
    track('funnel_step', {
      step,
      stepNumber,
      ...additionalData,
      timestamp: Date.now()
    });
  };

  const trackAbandon = (step: string, reason?: string, additionalData: Record<string, any> = {}) => {
    track('funnel_abandon', {
      step,
      reason,
      ...additionalData,
      timestamp: Date.now()
    });
  };

  return {
    trackConversion,
    trackFunnelStep,
    trackAbandon
  };
};

/**
 * Hook pour prédictions IA
 */
export const usePredictiveAnalytics = () => {
  const { requestPredictions, track } = useAnalytics();
  const [predictions, setPredictions] = React.useState<PredictionResponse | null>(null);
  const [isLoading, setIsLoading] = React.useState(false);

  const getPredictions = async () => {
    setIsLoading(true);
    try {
      const result = await requestPredictions();
      setPredictions(result);
      
      if (result) {
        track('predictions_used', {
          conversionProbability: result.conversionProbability,
          churnRisk: result.churnRisk,
          recommendedActionsCount: result.recommendedActions.length
        });
      }
    } catch (error) {
      console.error('Failed to get predictions:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return {
    predictions,
    isLoading,
    getPredictions
  };
};

export default AnalyticsProvider;
