/**
 * Store de Navigation Cross-Services - Sprint 15
 * Gestion centralisée de l'état de navigation entre microservices
 */

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { subscribeWithSelector } from 'zustand/middleware';

export interface NavigationState {
  currentService: string;
  previousService?: string;
  breadcrumbs: BreadcrumbItem[];
  isTransitioning: boolean;
  transitionDirection: 'forward' | 'backward' | null;
}

export interface BreadcrumbItem {
  id: string;
  label: string;
  href: string;
  service: string;
  isActive?: boolean;
}

export interface NavigationHistory {
  service: string;
  path: string;
  timestamp: number;
  title?: string;
}

export interface NavigationActions {
  // Navigation entre services
  navigateToService: (serviceId: string, path?: string) => void;
  setCurrentService: (serviceId: string) => void;
  
  // Gestion des breadcrumbs
  setBreadcrumbs: (breadcrumbs: BreadcrumbItem[]) => void;
  addBreadcrumb: (breadcrumb: BreadcrumbItem) => void;
  removeBreadcrumb: (id: string) => void;
  clearBreadcrumbs: () => void;
  
  // Gestion des transitions
  startTransition: (direction: 'forward' | 'backward') => void;
  endTransition: () => void;
  
  // Historique
  addToHistory: (entry: NavigationHistory) => void;
  getHistory: () => NavigationHistory[];
  clearHistory: () => void;
  
  // Utilitaires
  canGoBack: () => boolean;
  goBack: () => void;
  reset: () => void;
}

export type NavigationStore = NavigationState & NavigationActions & {
  history: NavigationHistory[];
};

const initialState: NavigationState = {
  currentService: 'main',
  previousService: undefined,
  breadcrumbs: [],
  isTransitioning: false,
  transitionDirection: null,
};

export const useNavigationStore = create<NavigationStore>()(
  subscribeWithSelector(
    persist(
      (set, get) => ({
        ...initialState,
        history: [],

        // Navigation entre services
        navigateToService: (serviceId: string, path = '/') => {
          const currentState = get();
          
          // Éviter la navigation vers le même service
          if (currentState.currentService === serviceId) {
            return;
          }

          // Démarrer la transition
          set({ isTransitioning: true, transitionDirection: 'forward' });

          // Ajouter à l'historique
          const historyEntry: NavigationHistory = {
            service: serviceId,
            path,
            timestamp: Date.now(),
            title: `Navigation vers ${serviceId}`
          };

          // Mettre à jour l'état
          set({
            previousService: currentState.currentService,
            currentService: serviceId,
            history: [...currentState.history, historyEntry].slice(-50) // Garder les 50 dernières entrées
          });

          // Simuler la navigation (à adapter selon le router utilisé)
          if (typeof window !== 'undefined') {
            const targetUrl = `/${serviceId}${path}`;
            window.history.pushState({ service: serviceId, path }, '', targetUrl);
          }

          // Terminer la transition après un délai
          setTimeout(() => {
            set({ isTransitioning: false, transitionDirection: null });
          }, 300);
        },

        setCurrentService: (serviceId: string) => {
          set({ currentService: serviceId });
        },

        // Gestion des breadcrumbs
        setBreadcrumbs: (breadcrumbs: BreadcrumbItem[]) => {
          set({ breadcrumbs });
        },

        addBreadcrumb: (breadcrumb: BreadcrumbItem) => {
          const currentBreadcrumbs = get().breadcrumbs;
          
          // Éviter les doublons
          const exists = currentBreadcrumbs.some(b => b.id === breadcrumb.id);
          if (exists) {
            return;
          }

          set({
            breadcrumbs: [...currentBreadcrumbs, breadcrumb]
          });
        },

        removeBreadcrumb: (id: string) => {
          const currentBreadcrumbs = get().breadcrumbs;
          set({
            breadcrumbs: currentBreadcrumbs.filter(b => b.id !== id)
          });
        },

        clearBreadcrumbs: () => {
          set({ breadcrumbs: [] });
        },

        // Gestion des transitions
        startTransition: (direction: 'forward' | 'backward') => {
          set({ isTransitioning: true, transitionDirection: direction });
        },

        endTransition: () => {
          set({ isTransitioning: false, transitionDirection: null });
        },

        // Historique
        addToHistory: (entry: NavigationHistory) => {
          const currentHistory = get().history;
          set({
            history: [...currentHistory, entry].slice(-50)
          });
        },

        getHistory: () => {
          return get().history;
        },

        clearHistory: () => {
          set({ history: [] });
        },

        // Utilitaires
        canGoBack: () => {
          const history = get().history;
          return history.length > 1;
        },

        goBack: () => {
          const state = get();
          
          if (!state.canGoBack()) {
            return;
          }

          const history = state.history;
          const previousEntry = history[history.length - 2];

          if (previousEntry) {
            state.startTransition('backward');
            
            set({
              currentService: previousEntry.service,
              previousService: state.currentService,
              history: history.slice(0, -1)
            });

            // Navigation browser
            if (typeof window !== 'undefined') {
              window.history.back();
            }

            setTimeout(() => {
              state.endTransition();
            }, 300);
          }
        },

        reset: () => {
          set({
            ...initialState,
            history: []
          });
        }
      }),
      {
        name: 'retreat-and-be-navigation',
        storage: createJSONStorage(() => sessionStorage),
        partialize: (state) => ({
          currentService: state.currentService,
          history: state.history.slice(-10), // Persister seulement les 10 dernières entrées
        }),
      }
    )
  )
);

// Hooks utilitaires
export const useCurrentService = () => useNavigationStore(state => state.currentService);
export const useBreadcrumbs = () => useNavigationStore(state => state.breadcrumbs);
export const useNavigationHistory = () => useNavigationStore(state => state.history);
export const useIsTransitioning = () => useNavigationStore(state => state.isTransitioning);

// Sélecteurs
export const navigationSelectors = {
  getCurrentService: (state: NavigationStore) => state.currentService,
  getPreviousService: (state: NavigationStore) => state.previousService,
  getBreadcrumbs: (state: NavigationStore) => state.breadcrumbs,
  getIsTransitioning: (state: NavigationStore) => state.isTransitioning,
  getCanGoBack: (state: NavigationStore) => state.canGoBack(),
  getLastVisitedService: (state: NavigationStore) => {
    const history = state.history;
    return history.length > 1 ? history[history.length - 2].service : null;
  }
};

// Middleware pour écouter les changements de service
export const subscribeToServiceChanges = (callback: (service: string) => void) => {
  return useNavigationStore.subscribe(
    (state) => state.currentService,
    callback
  );
};

// Utilitaires pour l'intégration avec React Router
export const createServiceRoute = (serviceId: string, basePath = '') => {
  return `${basePath}/${serviceId}/*`;
};

export const getServiceFromPath = (pathname: string): string => {
  const segments = pathname.split('/').filter(Boolean);
  return segments[0] || 'main';
};

// Configuration des services par défaut
export const defaultServiceConfig = {
  main: {
    id: 'main',
    name: 'Accueil',
    basePath: '/',
    defaultPath: '/'
  },
  ai: {
    id: 'ai',
    name: 'Assistant IA',
    basePath: '/ai',
    defaultPath: '/ai/chat'
  },
  financial: {
    id: 'financial',
    name: 'Finances',
    basePath: '/financial',
    defaultPath: '/financial/dashboard'
  },
  social: {
    id: 'social',
    name: 'Social',
    basePath: '/social',
    defaultPath: '/social/feed'
  }
};

// Hook pour l'initialisation de la navigation
export const useNavigationInit = (initialService?: string) => {
  const { setCurrentService, addToHistory } = useNavigationStore();

  React.useEffect(() => {
    if (initialService) {
      setCurrentService(initialService);
      addToHistory({
        service: initialService,
        path: window.location.pathname,
        timestamp: Date.now(),
        title: `Initialisation ${initialService}`
      });
    }
  }, [initialService, setCurrentService, addToHistory]);
};

// Hook pour la synchronisation avec React Router
export const useRouterSync = () => {
  const { setCurrentService, addToHistory } = useNavigationStore();

  React.useEffect(() => {
    const handlePopState = (event: PopStateEvent) => {
      const service = getServiceFromPath(window.location.pathname);
      setCurrentService(service);
      
      if (event.state?.service) {
        addToHistory({
          service: event.state.service,
          path: event.state.path || window.location.pathname,
          timestamp: Date.now(),
          title: `Navigation browser vers ${event.state.service}`
        });
      }
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, [setCurrentService, addToHistory]);
};
