/**
 * @retreat-and-be/design-system
 * Design System Unifié - Sprint 15
 * 
 * Point d'entrée principal pour tous les composants,
 * hooks, utilitaires et tokens du Design System
 */

// === TOKENS DE DESIGN ===
export { theme, type Theme } from './tokens/theme';
export { colors, type ColorPalette } from './tokens/colors';
export { typography, type Typography } from './tokens/typography';
export { spacing, type Spacing } from './tokens/spacing';
export { breakpoints, type Breakpoints } from './tokens/breakpoints';
export { shadows, type Shadows } from './tokens/shadows';
export { animations, type Animations } from './tokens/animations';

// === COMPOSANTS DE BASE ===
export { Button, type ButtonProps } from './components/Button';
export { Input, type InputProps } from './components/Input';
export { TextArea, type TextAreaProps } from './components/TextArea';
export { Label, type LabelProps } from './components/Label';
export { Badge, type BadgeProps } from './components/Badge';
export { Avatar, type AvatarProps } from './components/Avatar';
export { Spinner, type SpinnerProps } from './components/Spinner';
export { Skeleton, type SkeletonProps } from './components/Skeleton';

// === COMPOSANTS DE LAYOUT ===
export { Container, type ContainerProps } from './components/Container';
export { Grid, type GridProps } from './components/Grid';
export { Stack, type StackProps } from './components/Stack';
export { Divider, type DividerProps } from './components/Divider';
export { Card, type CardProps } from './components/Card';
export { Paper, type PaperProps } from './components/Paper';

// === COMPOSANTS DE NAVIGATION ===
export { Header, type HeaderProps } from './components/Header';
export { Navigation, type NavigationProps } from './components/Navigation';
export { Breadcrumb, type BreadcrumbProps } from './components/Breadcrumb';
export { Tabs, type TabsProps } from './components/Tabs';
export { Pagination, type PaginationProps } from './components/Pagination';
export { Sidebar, type SidebarProps } from './components/Sidebar';

// === COMPOSANTS DE FORMULAIRE ===
export { FormField, type FormFieldProps } from './components/FormField';
export { Select, type SelectProps } from './components/Select';
export { Checkbox, type CheckboxProps } from './components/Checkbox';
export { Radio, type RadioProps } from './components/Radio';
export { Switch, type SwitchProps } from './components/Switch';
export { Slider, type SliderProps } from './components/Slider';
export { DatePicker, type DatePickerProps } from './components/DatePicker';
export { FileUpload, type FileUploadProps } from './components/FileUpload';

// === COMPOSANTS DE FEEDBACK ===
export { Alert, type AlertProps } from './components/Alert';
export { Toast, type ToastProps } from './components/Toast';
export { Modal, type ModalProps } from './components/Modal';
export { Tooltip, type TooltipProps } from './components/Tooltip';
export { Popover, type PopoverProps } from './components/Popover';
export { Progress, type ProgressProps } from './components/Progress';
export { Loading, type LoadingProps } from './components/Loading';

// === COMPOSANTS DE DONNÉES ===
export { Table, type TableProps } from './components/Table';
export { DataGrid, type DataGridProps } from './components/DataGrid';
export { Chart, type ChartProps } from './components/Chart';
export { EmptyState, type EmptyStateProps } from './components/EmptyState';
export { SearchInput, type SearchInputProps } from './components/SearchInput';

// === COMPOSANTS SPÉCIALISÉS ===
export { ChatWindow, type ChatWindowProps } from './components/ChatWindow';
export { MessageBubble, type MessageBubbleProps } from './components/MessageBubble';
export { UserProfile, type UserProfileProps } from './components/UserProfile';
export { RetreatCard, type RetreatCardProps } from './components/RetreatCard';
export { PaymentForm, type PaymentFormProps } from './components/PaymentForm';
export { RatingStars, type RatingStarsProps } from './components/RatingStars';

// === HOOKS ===
export { useTheme } from './hooks/useTheme';
export { useBreakpoint } from './hooks/useBreakpoint';
export { useLocalStorage } from './hooks/useLocalStorage';
export { useSessionStorage } from './hooks/useSessionStorage';
export { useModal } from './hooks/useModal';
export { useToast } from './hooks/useToast';
export { useForm } from './hooks/useForm';
export { useDebounce } from './hooks/useDebounce';
export { useMediaQuery } from './hooks/useMediaQuery';
export { useClickOutside } from './hooks/useClickOutside';
export { useKeyboard } from './hooks/useKeyboard';

// === PROVIDERS ===
export { ThemeProvider, type ThemeProviderProps } from './providers/ThemeProvider';
export { ToastProvider, type ToastProviderProps } from './providers/ToastProvider';
export { ModalProvider, type ModalProviderProps } from './providers/ModalProvider';
export { DesignSystemProvider, type DesignSystemProviderProps } from './providers/DesignSystemProvider';

// === UTILITAIRES ===
export { cn } from './utils/cn';
export { formatCurrency } from './utils/formatCurrency';
export { formatDate } from './utils/formatDate';
export { generateId } from './utils/generateId';
export { debounce } from './utils/debounce';
export { throttle } from './utils/throttle';
export { validateEmail } from './utils/validation';
export { createVariants } from './utils/variants';

// === TYPES GLOBAUX ===
export type {
  Size,
  Variant,
  Color,
  Spacing as SpacingValue,
  Breakpoint,
  ThemeMode,
  ComponentProps,
  IconProps,
  FormProps,
  LayoutProps
} from './types/global';

// === CONSTANTES ===
export {
  BREAKPOINT_VALUES,
  COLOR_NAMES,
  SPACING_VALUES,
  ANIMATION_DURATIONS,
  Z_INDEX_VALUES
} from './constants';

// === CONFIGURATION CROSS-SERVICES ===
export { microserviceConfig } from './config/microservices';
export { navigationConfig } from './config/navigation';
export { authConfig } from './config/auth';

// === VERSION ===
export const VERSION = '2.0.0';
export const BUILD_DATE = new Date().toISOString();

/**
 * Configuration par défaut pour l'intégration cross-services
 */
export const defaultConfig = {
  theme: 'light' as const,
  primaryColor: 'blue' as const,
  borderRadius: 'md' as const,
  animations: true,
  rtl: false,
  microservice: {
    name: 'main',
    version: '1.0.0',
    apiUrl: process.env.REACT_APP_API_URL || 'http://localhost:3000'
  }
};

/**
 * Fonction d'initialisation du Design System
 * À appeler au démarrage de chaque microservice
 */
export function initializeDesignSystem(config?: Partial<typeof defaultConfig>) {
  const finalConfig = { ...defaultConfig, ...config };
  
  // Injection des CSS variables globales
  if (typeof document !== 'undefined') {
    const root = document.documentElement;
    
    // Injection du thème
    Object.entries(theme.colors.primary).forEach(([key, value]) => {
      root.style.setProperty(`--color-primary-${key}`, value);
    });
    
    // Injection des espacements
    Object.entries(spacing).forEach(([key, value]) => {
      root.style.setProperty(`--spacing-${key}`, value);
    });
    
    // Injection des breakpoints
    Object.entries(breakpoints).forEach(([key, value]) => {
      root.style.setProperty(`--breakpoint-${key}`, value);
    });
  }
  
  return finalConfig;
}

/**
 * Utilitaire pour la migration progressive
 * Permet de détecter si un composant utilise le nouveau Design System
 */
export function isDesignSystemComponent(component: any): boolean {
  return component?.$$designSystem === true;
}

/**
 * HOC pour marquer les composants du Design System
 */
export function withDesignSystem<T extends object>(Component: React.ComponentType<T>) {
  const WrappedComponent = (props: T) => {
    return <Component {...props} />;
  };
  
  WrappedComponent.displayName = `withDesignSystem(${Component.displayName || Component.name})`;
  (WrappedComponent as any).$$designSystem = true;
  
  return WrappedComponent;
}

/**
 * Fonction de validation de compatibilité
 * Vérifie que les dépendances sont compatibles
 */
export function validateCompatibility() {
  const issues: string[] = [];
  
  // Vérifier React version
  if (typeof React !== 'undefined') {
    const reactVersion = React.version;
    const majorVersion = parseInt(reactVersion.split('.')[0]);
    
    if (majorVersion < 18) {
      issues.push(`React version ${reactVersion} is not supported. Please upgrade to React 18+`);
    }
  }
  
  // Vérifier les CSS Custom Properties
  if (typeof window !== 'undefined' && !window.CSS?.supports?.('color', 'var(--test)')) {
    issues.push('CSS Custom Properties are not supported in this browser');
  }
  
  return {
    isCompatible: issues.length === 0,
    issues
  };
}

// Export par défaut pour l'importation simplifiée
export default {
  theme,
  colors,
  typography,
  spacing,
  breakpoints,
  initializeDesignSystem,
  validateCompatibility,
  VERSION,
  BUILD_DATE
};
