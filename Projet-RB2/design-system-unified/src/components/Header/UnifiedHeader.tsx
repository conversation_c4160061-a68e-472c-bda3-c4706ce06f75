/**
 * Header Unifié Cross-Services - Sprint 15
 * Composant de navigation principal pour tous les microservices
 */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Menu, 
  X, 
  Bell, 
  User, 
  Settings, 
  LogOut, 
  ChevronDown,
  Search,
  MessageCircle,
  CreditCard,
  Users,
  Home
} from 'lucide-react';
import { cn } from '../../utils/cn';
import { Button } from '../Button';
import { Avatar } from '../Avatar';
import { Badge } from '../Badge';
import { Popover } from '../Popover';
import { useTheme } from '../../hooks/useTheme';

export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: 'user' | 'admin' | 'moderator';
}

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  timestamp: Date;
  read: boolean;
  actionUrl?: string;
}

export interface MicroserviceInfo {
  id: string;
  name: string;
  displayName: string;
  icon: React.ComponentType<{ className?: string }>;
  url: string;
  isActive: boolean;
  badge?: number;
}

export interface UnifiedHeaderProps {
  /** Utilisateur connecté */
  user?: User;
  
  /** Service actuel */
  currentService: string;
  
  /** Liste des microservices disponibles */
  services: MicroserviceInfo[];
  
  /** Notifications utilisateur */
  notifications?: Notification[];
  
  /** Callback changement de service */
  onServiceChange: (serviceId: string) => void;
  
  /** Callback déconnexion */
  onLogout: () => void;
  
  /** Callback recherche globale */
  onSearch?: (query: string) => void;
  
  /** Mode compact pour mobile */
  compact?: boolean;
  
  /** Classes CSS personnalisées */
  className?: string;
}

export const UnifiedHeader: React.FC<UnifiedHeaderProps> = ({
  user,
  currentService,
  services,
  notifications = [],
  onServiceChange,
  onLogout,
  onSearch,
  compact = false,
  className
}) => {
  const { theme, isDark } = useTheme();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearchFocused, setIsSearchFocused] = useState(false);

  const currentServiceInfo = services.find(s => s.id === currentService);
  const unreadNotifications = notifications.filter(n => !n.read).length;

  // Fermer le menu mobile lors du changement de service
  useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [currentService]);

  const handleServiceSwitch = (serviceId: string) => {
    if (serviceId !== currentService) {
      onServiceChange(serviceId);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (onSearch && searchQuery.trim()) {
      onSearch(searchQuery.trim());
    }
  };

  return (
    <header className={cn(
      'sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60',
      isDark ? 'border-gray-800' : 'border-gray-200',
      className
    )}>
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo et Service Actuel */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              {currentServiceInfo?.icon && (
                <currentServiceInfo.icon className="h-6 w-6 text-primary-600" />
              )}
              <span className="font-semibold text-lg">
                {currentServiceInfo?.displayName || 'Retreat & Be'}
              </span>
            </div>

            {/* Navigation Services - Desktop */}
            {!compact && (
              <nav className="hidden md:flex items-center space-x-1">
                {services.map((service) => (
                  <Button
                    key={service.id}
                    variant={service.isActive ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => handleServiceSwitch(service.id)}
                    className="relative"
                  >
                    <service.icon className="h-4 w-4 mr-2" />
                    {service.displayName}
                    {service.badge && service.badge > 0 && (
                      <Badge 
                        variant="destructive" 
                        className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs"
                      >
                        {service.badge > 99 ? '99+' : service.badge}
                      </Badge>
                    )}
                  </Button>
                ))}
              </nav>
            )}
          </div>

          {/* Barre de Recherche - Desktop */}
          {!compact && onSearch && (
            <div className="hidden md:flex flex-1 max-w-md mx-8">
              <form onSubmit={handleSearch} className="relative w-full">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Rechercher..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onFocus={() => setIsSearchFocused(true)}
                  onBlur={() => setIsSearchFocused(false)}
                  className={cn(
                    'w-full pl-10 pr-4 py-2 border rounded-lg bg-background',
                    'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent',
                    isSearchFocused ? 'ring-2 ring-primary-500' : 'border-gray-300'
                  )}
                />
              </form>
            </div>
          )}

          {/* Actions Utilisateur */}
          <div className="flex items-center space-x-2">
            {/* Notifications */}
            {user && (
              <Popover>
                <Popover.Trigger asChild>
                  <Button variant="ghost" size="sm" className="relative">
                    <Bell className="h-5 w-5" />
                    {unreadNotifications > 0 && (
                      <Badge 
                        variant="destructive" 
                        className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs"
                      >
                        {unreadNotifications > 99 ? '99+' : unreadNotifications}
                      </Badge>
                    )}
                  </Button>
                </Popover.Trigger>
                <Popover.Content className="w-80 p-0">
                  <div className="p-4 border-b">
                    <h3 className="font-semibold">Notifications</h3>
                  </div>
                  <div className="max-h-96 overflow-y-auto">
                    {notifications.length > 0 ? (
                      notifications.slice(0, 5).map((notification) => (
                        <div
                          key={notification.id}
                          className={cn(
                            'p-4 border-b hover:bg-gray-50 cursor-pointer',
                            !notification.read && 'bg-blue-50'
                          )}
                        >
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <p className="font-medium text-sm">{notification.title}</p>
                              <p className="text-sm text-gray-600 mt-1">{notification.message}</p>
                              <p className="text-xs text-gray-400 mt-2">
                                {notification.timestamp.toLocaleDateString()}
                              </p>
                            </div>
                            {!notification.read && (
                              <div className="w-2 h-2 bg-blue-500 rounded-full mt-1"></div>
                            )}
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="p-8 text-center text-gray-500">
                        Aucune notification
                      </div>
                    )}
                  </div>
                </Popover.Content>
              </Popover>
            )}

            {/* Menu Utilisateur */}
            {user ? (
              <Popover>
                <Popover.Trigger asChild>
                  <Button variant="ghost" className="flex items-center space-x-2 px-2">
                    <Avatar src={user.avatar} alt={user.name} size="sm" />
                    {!compact && (
                      <>
                        <span className="hidden md:block font-medium">{user.name}</span>
                        <ChevronDown className="h-4 w-4" />
                      </>
                    )}
                  </Button>
                </Popover.Trigger>
                <Popover.Content className="w-56 p-0">
                  <div className="p-4 border-b">
                    <p className="font-medium">{user.name}</p>
                    <p className="text-sm text-gray-600">{user.email}</p>
                  </div>
                  <div className="p-2">
                    <Button variant="ghost" className="w-full justify-start" size="sm">
                      <User className="h-4 w-4 mr-2" />
                      Profil
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" size="sm">
                      <Settings className="h-4 w-4 mr-2" />
                      Paramètres
                    </Button>
                    <div className="border-t my-2"></div>
                    <Button 
                      variant="ghost" 
                      className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50" 
                      size="sm"
                      onClick={onLogout}
                    >
                      <LogOut className="h-4 w-4 mr-2" />
                      Déconnexion
                    </Button>
                  </div>
                </Popover.Content>
              </Popover>
            ) : (
              <div className="flex items-center space-x-2">
                <Button variant="ghost" size="sm">
                  Connexion
                </Button>
                <Button size="sm">
                  Inscription
                </Button>
              </div>
            )}

            {/* Menu Mobile */}
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              {isMobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
          </div>
        </div>

        {/* Menu Mobile */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="md:hidden border-t"
            >
              <div className="py-4 space-y-2">
                {/* Recherche Mobile */}
                {onSearch && (
                  <form onSubmit={handleSearch} className="relative mb-4">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Rechercher..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </form>
                )}

                {/* Services Mobile */}
                {services.map((service) => (
                  <Button
                    key={service.id}
                    variant={service.isActive ? 'default' : 'ghost'}
                    className="w-full justify-start"
                    onClick={() => handleServiceSwitch(service.id)}
                  >
                    <service.icon className="h-4 w-4 mr-2" />
                    {service.displayName}
                    {service.badge && service.badge > 0 && (
                      <Badge variant="destructive" className="ml-auto">
                        {service.badge > 99 ? '99+' : service.badge}
                      </Badge>
                    )}
                  </Button>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </header>
  );
};

// Configuration par défaut des services
export const defaultServices: MicroserviceInfo[] = [
  {
    id: 'main',
    name: 'main',
    displayName: 'Accueil',
    icon: Home,
    url: '/',
    isActive: true
  },
  {
    id: 'ai',
    name: 'agent-ia',
    displayName: 'Assistant IA',
    icon: MessageCircle,
    url: '/ai',
    isActive: false
  },
  {
    id: 'financial',
    name: 'financial',
    displayName: 'Finances',
    icon: CreditCard,
    url: '/financial',
    isActive: false
  },
  {
    id: 'social',
    name: 'social',
    displayName: 'Social',
    icon: Users,
    url: '/social',
    isActive: false
  }
];

UnifiedHeader.displayName = 'UnifiedHeader';
