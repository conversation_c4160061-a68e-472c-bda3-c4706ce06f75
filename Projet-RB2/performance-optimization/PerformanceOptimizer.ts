/**
 * Performance Optimizer - Sprint 16
 * Optimisation avancée pour navigation <200ms et performance maximale
 */

interface PerformanceConfig {
  targetNavigationTime: number; // ms
  enablePredictivePreloading: boolean;
  enableIntelligentCaching: boolean;
  enableBundleOptimization: boolean;
  enableImageOptimization: boolean;
  enableServiceWorker: boolean;
  enableCriticalResourceHints: boolean;
  debugMode: boolean;
}

interface ResourceHint {
  url: string;
  type: 'preload' | 'prefetch' | 'preconnect' | 'dns-prefetch';
  priority: 'high' | 'medium' | 'low';
  crossorigin?: boolean;
}

interface CacheStrategy {
  pattern: string;
  strategy: 'cache-first' | 'network-first' | 'stale-while-revalidate' | 'network-only';
  maxAge: number;
  maxEntries?: number;
}

export class PerformanceOptimizer {
  private config: PerformanceConfig;
  private navigationTimes: number[] = [];
  private resourceCache = new Map<string, any>();
  private preloadedResources = new Set<string>();
  private criticalResources = new Set<string>();
  private observer?: PerformanceObserver;

  constructor(config: Partial<PerformanceConfig> = {}) {
    this.config = {
      targetNavigationTime: 200,
      enablePredictivePreloading: true,
      enableIntelligentCaching: true,
      enableBundleOptimization: true,
      enableImageOptimization: true,
      enableServiceWorker: true,
      enableCriticalResourceHints: true,
      debugMode: false,
      ...config
    };

    this.initialize();
  }

  /**
   * Initialisation de l'optimiseur
   */
  private async initialize() {
    this.debug('Initializing Performance Optimizer', this.config);

    // Monitoring des performances
    this.setupPerformanceMonitoring();

    // Service Worker pour cache intelligent
    if (this.config.enableServiceWorker) {
      await this.setupServiceWorker();
    }

    // Optimisation des bundles
    if (this.config.enableBundleOptimization) {
      this.optimizeBundles();
    }

    // Optimisation des images
    if (this.config.enableImageOptimization) {
      this.optimizeImages();
    }

    // Preloading prédictif
    if (this.config.enablePredictivePreloading) {
      this.setupPredictivePreloading();
    }

    // Resource hints critiques
    if (this.config.enableCriticalResourceHints) {
      this.setupCriticalResourceHints();
    }

    this.debug('Performance Optimizer initialized successfully');
  }

  /**
   * Monitoring des performances
   */
  private setupPerformanceMonitoring() {
    // Observer pour les métriques de navigation
    this.observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.entryType === 'navigation') {
          const navEntry = entry as PerformanceNavigationTiming;
          const navigationTime = navEntry.loadEventEnd - navEntry.loadEventStart;
          
          this.navigationTimes.push(navigationTime);
          this.analyzeNavigationPerformance(navigationTime);
        }

        if (entry.entryType === 'resource') {
          this.analyzeResourcePerformance(entry as PerformanceResourceTiming);
        }
      });
    });

    this.observer.observe({ entryTypes: ['navigation', 'resource'] });

    // Monitoring des Core Web Vitals
    this.monitorWebVitals();
  }

  /**
   * Analyse des performances de navigation
   */
  private analyzeNavigationPerformance(navigationTime: number) {
    this.debug(`Navigation time: ${navigationTime}ms (target: ${this.config.targetNavigationTime}ms)`);

    if (navigationTime > this.config.targetNavigationTime) {
      this.debug('Navigation time exceeds target, applying optimizations');
      this.applyNavigationOptimizations();
    }

    // Calculer la moyenne mobile
    const recentTimes = this.navigationTimes.slice(-10);
    const averageTime = recentTimes.reduce((a, b) => a + b, 0) / recentTimes.length;

    if (averageTime > this.config.targetNavigationTime * 1.2) {
      this.debug('Average navigation time degraded, applying aggressive optimizations');
      this.applyAggressiveOptimizations();
    }
  }

  /**
   * Analyse des performances des ressources
   */
  private analyzeResourcePerformance(entry: PerformanceResourceTiming) {
    const loadTime = entry.responseEnd - entry.requestStart;
    
    // Identifier les ressources lentes
    if (loadTime > 1000) {
      this.debug(`Slow resource detected: ${entry.name} (${loadTime}ms)`);
      this.optimizeSlowResource(entry);
    }

    // Identifier les ressources critiques
    if (this.isCriticalResource(entry.name)) {
      this.criticalResources.add(entry.name);
      this.prioritizeResource(entry.name);
    }
  }

  /**
   * Monitoring des Core Web Vitals
   */
  private monitorWebVitals() {
    // First Contentful Paint
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
      if (fcpEntry && fcpEntry.startTime > 1800) {
        this.optimizeFCP();
      }
    }).observe({ entryTypes: ['paint'] });

    // Largest Contentful Paint
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      if (lastEntry && lastEntry.startTime > 2500) {
        this.optimizeLCP();
      }
    }).observe({ entryTypes: ['largest-contentful-paint'] });

    // Cumulative Layout Shift
    let clsValue = 0;
    new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value;
        }
      }
      if (clsValue > 0.1) {
        this.optimizeCLS();
      }
    }).observe({ entryTypes: ['layout-shift'] });
  }

  /**
   * Configuration du Service Worker
   */
  private async setupServiceWorker() {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register('/sw-performance.js');
        this.debug('Service Worker registered successfully');

        // Configuration des stratégies de cache
        const cacheStrategies: CacheStrategy[] = [
          {
            pattern: '/api/.*',
            strategy: 'network-first',
            maxAge: 300000, // 5 minutes
            maxEntries: 100
          },
          {
            pattern: '/static/.*\\.(js|css|png|jpg|jpeg|svg|woff2)$',
            strategy: 'cache-first',
            maxAge: 31536000000, // 1 an
            maxEntries: 200
          },
          {
            pattern: '/.*',
            strategy: 'stale-while-revalidate',
            maxAge: 86400000, // 1 jour
            maxEntries: 50
          }
        ];

        // Envoyer les stratégies au Service Worker
        registration.active?.postMessage({
          type: 'CACHE_STRATEGIES',
          strategies: cacheStrategies
        });

      } catch (error) {
        console.error('Service Worker registration failed:', error);
      }
    }
  }

  /**
   * Optimisation des bundles
   */
  private optimizeBundles() {
    // Code splitting dynamique
    this.implementDynamicImports();

    // Tree shaking agressif
    this.optimizeTreeShaking();

    // Compression et minification
    this.optimizeCompression();
  }

  /**
   * Implémentation des imports dynamiques
   */
  private implementDynamicImports() {
    // Intercepter les navigations pour charger les modules à la demande
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    history.pushState = (...args) => {
      this.preloadRouteModule(args[2] as string);
      return originalPushState.apply(history, args);
    };

    history.replaceState = (...args) => {
      this.preloadRouteModule(args[2] as string);
      return originalReplaceState.apply(history, args);
    };

    window.addEventListener('popstate', (event) => {
      this.preloadRouteModule(window.location.pathname);
    });
  }

  /**
   * Preload des modules de route
   */
  private async preloadRouteModule(path: string) {
    const moduleMap: Record<string, () => Promise<any>> = {
      '/': () => import('../components/Home'),
      '/ai': () => import('../components/AI'),
      '/financial': () => import('../components/Financial'),
      '/social': () => import('../components/Social'),
      '/search': () => import('../components/Search'),
      '/booking': () => import('../components/Booking')
    };

    const moduleLoader = moduleMap[path];
    if (moduleLoader && !this.preloadedResources.has(path)) {
      this.debug(`Preloading module for route: ${path}`);
      try {
        await moduleLoader();
        this.preloadedResources.add(path);
      } catch (error) {
        console.error(`Failed to preload module for ${path}:`, error);
      }
    }
  }

  /**
   * Optimisation Tree Shaking
   */
  private optimizeTreeShaking() {
    // Analyser les imports inutilisés
    const unusedImports = this.detectUnusedImports();
    
    if (unusedImports.length > 0) {
      this.debug('Unused imports detected:', unusedImports);
      // En production, ces imports seraient supprimés par le build
    }
  }

  /**
   * Optimisation de la compression
   */
  private optimizeCompression() {
    // Vérifier si Brotli est supporté
    const supportsBrotli = 'CompressionStream' in window;
    
    if (supportsBrotli) {
      this.debug('Brotli compression supported');
      // Configurer la compression Brotli pour les ressources
    }

    // Optimiser les headers de cache
    this.optimizeCacheHeaders();
  }

  /**
   * Optimisation des images
   */
  private optimizeImages() {
    // Observer les images pour lazy loading intelligent
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement;
          this.optimizeImage(img);
          imageObserver.unobserve(img);
        }
      });
    }, { rootMargin: '50px' });

    // Observer toutes les images
    document.querySelectorAll('img[data-src]').forEach((img) => {
      imageObserver.observe(img);
    });

    // Conversion automatique vers WebP/AVIF
    this.setupModernImageFormats();
  }

  /**
   * Optimisation d'une image
   */
  private optimizeImage(img: HTMLImageElement) {
    const dataSrc = img.getAttribute('data-src');
    if (!dataSrc) return;

    // Choisir le format optimal
    const optimizedSrc = this.getOptimizedImageSrc(dataSrc);
    
    // Preload de l'image
    const preloadLink = document.createElement('link');
    preloadLink.rel = 'preload';
    preloadLink.as = 'image';
    preloadLink.href = optimizedSrc;
    document.head.appendChild(preloadLink);

    // Charger l'image
    img.src = optimizedSrc;
    img.removeAttribute('data-src');
  }

  /**
   * Obtenir la source d'image optimisée
   */
  private getOptimizedImageSrc(originalSrc: string): string {
    // Détecter le support des formats modernes
    const supportsWebP = this.supportsImageFormat('webp');
    const supportsAVIF = this.supportsImageFormat('avif');

    if (supportsAVIF) {
      return originalSrc.replace(/\.(jpg|jpeg|png)$/i, '.avif');
    } else if (supportsWebP) {
      return originalSrc.replace(/\.(jpg|jpeg|png)$/i, '.webp');
    }

    return originalSrc;
  }

  /**
   * Vérifier le support d'un format d'image
   */
  private supportsImageFormat(format: string): boolean {
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    return canvas.toDataURL(`image/${format}`).indexOf(`data:image/${format}`) === 0;
  }

  /**
   * Configuration des formats d'images modernes
   */
  private setupModernImageFormats() {
    // Intercepter les requêtes d'images pour servir le format optimal
    if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
      navigator.serviceWorker.controller.postMessage({
        type: 'OPTIMIZE_IMAGES',
        formats: {
          webp: this.supportsImageFormat('webp'),
          avif: this.supportsImageFormat('avif')
        }
      });
    }
  }

  /**
   * Preloading prédictif
   */
  private setupPredictivePreloading() {
    // Analyser les patterns de navigation
    this.analyzeNavigationPatterns();

    // Preload basé sur le hover
    this.setupHoverPreloading();

    // Preload basé sur la probabilité
    this.setupProbabilisticPreloading();
  }

  /**
   * Analyse des patterns de navigation
   */
  private analyzeNavigationPatterns() {
    const navigationHistory = this.getNavigationHistory();
    const patterns = this.extractNavigationPatterns(navigationHistory);

    // Preload des pages probables
    patterns.forEach((pattern) => {
      if (pattern.probability > 0.7) {
        this.preloadResource(pattern.nextPage);
      }
    });
  }

  /**
   * Preloading au hover
   */
  private setupHoverPreloading() {
    let hoverTimer: NodeJS.Timeout;

    document.addEventListener('mouseover', (event) => {
      const target = event.target as HTMLElement;
      const link = target.closest('a[href]') as HTMLAnchorElement;

      if (link && link.href) {
        hoverTimer = setTimeout(() => {
          this.preloadResource(link.href);
        }, 100); // Preload après 100ms de hover
      }
    });

    document.addEventListener('mouseout', () => {
      if (hoverTimer) {
        clearTimeout(hoverTimer);
      }
    });
  }

  /**
   * Preloading probabiliste
   */
  private setupProbabilisticPreloading() {
    // Utiliser l'idle time pour preloader
    if ('requestIdleCallback' in window) {
      const preloadDuringIdle = (deadline: IdleDeadline) => {
        while (deadline.timeRemaining() > 0) {
          const nextResource = this.getNextProbableResource();
          if (nextResource) {
            this.preloadResource(nextResource);
          } else {
            break;
          }
        }
        requestIdleCallback(preloadDuringIdle);
      };

      requestIdleCallback(preloadDuringIdle);
    }
  }

  /**
   * Configuration des resource hints critiques
   */
  private setupCriticalResourceHints() {
    const criticalHints: ResourceHint[] = [
      {
        url: '/api',
        type: 'preconnect',
        priority: 'high'
      },
      {
        url: 'https://fonts.googleapis.com',
        type: 'preconnect',
        priority: 'high',
        crossorigin: true
      },
      {
        url: '/static/css/critical.css',
        type: 'preload',
        priority: 'high'
      },
      {
        url: '/static/js/main.js',
        type: 'preload',
        priority: 'high'
      }
    ];

    criticalHints.forEach((hint) => {
      this.addResourceHint(hint);
    });
  }

  /**
   * Ajouter un resource hint
   */
  private addResourceHint(hint: ResourceHint) {
    const link = document.createElement('link');
    link.rel = hint.type;
    link.href = hint.url;
    
    if (hint.crossorigin) {
      link.crossOrigin = 'anonymous';
    }

    if (hint.type === 'preload') {
      link.as = this.getResourceType(hint.url);
    }

    document.head.appendChild(link);
    this.debug(`Added resource hint: ${hint.type} ${hint.url}`);
  }

  /**
   * Optimisations de navigation
   */
  private applyNavigationOptimizations() {
    // Réduire les requêtes réseau
    this.optimizeNetworkRequests();

    // Optimiser le rendu critique
    this.optimizeCriticalRendering();

    // Réduire la taille des bundles
    this.reduceBundleSize();
  }

  /**
   * Optimisations agressives
   */
  private applyAggressiveOptimizations() {
    // Preload agressif
    this.enableAggressivePreloading();

    // Cache agressif
    this.enableAggressiveCaching();

    // Compression maximale
    this.enableMaximumCompression();
  }

  /**
   * Optimisations spécifiques aux Core Web Vitals
   */
  private optimizeFCP() {
    this.debug('Optimizing First Contentful Paint');
    // Inliner le CSS critique
    this.inlineCriticalCSS();
    // Preload des fonts
    this.preloadCriticalFonts();
  }

  private optimizeLCP() {
    this.debug('Optimizing Largest Contentful Paint');
    // Optimiser les images hero
    this.optimizeHeroImages();
    // Preload des ressources critiques
    this.preloadCriticalResources();
  }

  private optimizeCLS() {
    this.debug('Optimizing Cumulative Layout Shift');
    // Réserver l'espace pour les images
    this.reserveImageSpace();
    // Stabiliser les fonts
    this.stabilizeFonts();
  }

  /**
   * Utilitaires
   */
  private preloadResource(url: string) {
    if (this.preloadedResources.has(url)) return;

    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = url;
    document.head.appendChild(link);
    
    this.preloadedResources.add(url);
    this.debug(`Preloaded resource: ${url}`);
  }

  private isCriticalResource(url: string): boolean {
    return url.includes('/critical/') || 
           url.includes('/main.') || 
           url.includes('/vendor.') ||
           url.includes('/runtime.');
  }

  private getResourceType(url: string): string {
    if (url.endsWith('.css')) return 'style';
    if (url.endsWith('.js')) return 'script';
    if (url.match(/\.(jpg|jpeg|png|webp|avif)$/)) return 'image';
    if (url.match(/\.(woff|woff2|ttf)$/)) return 'font';
    return 'fetch';
  }

  private debug(message: string, data?: any) {
    if (this.config.debugMode) {
      console.log(`[Performance Optimizer] ${message}`, data);
    }
  }

  // Méthodes stub pour les optimisations spécifiques
  private optimizeSlowResource(entry: PerformanceResourceTiming) { /* Implementation */ }
  private prioritizeResource(url: string) { /* Implementation */ }
  private detectUnusedImports(): string[] { return []; }
  private optimizeCacheHeaders() { /* Implementation */ }
  private getNavigationHistory(): any[] { return []; }
  private extractNavigationPatterns(history: any[]): any[] { return []; }
  private getNextProbableResource(): string | null { return null; }
  private optimizeNetworkRequests() { /* Implementation */ }
  private optimizeCriticalRendering() { /* Implementation */ }
  private reduceBundleSize() { /* Implementation */ }
  private enableAggressivePreloading() { /* Implementation */ }
  private enableAggressiveCaching() { /* Implementation */ }
  private enableMaximumCompression() { /* Implementation */ }
  private inlineCriticalCSS() { /* Implementation */ }
  private preloadCriticalFonts() { /* Implementation */ }
  private optimizeHeroImages() { /* Implementation */ }
  private preloadCriticalResources() { /* Implementation */ }
  private reserveImageSpace() { /* Implementation */ }
  private stabilizeFonts() { /* Implementation */ }

  /**
   * Nettoyage
   */
  public destroy() {
    if (this.observer) {
      this.observer.disconnect();
    }
  }
}

export default PerformanceOptimizer;
