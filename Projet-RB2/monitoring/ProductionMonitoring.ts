/**
 * Production Monitoring System - Sprint 16 Jour 4
 * Monitoring complet pour performance et analytics en production
 */

interface MonitoringConfig {
  environment: 'development' | 'staging' | 'production';
  services: MonitoredService[];
  metrics: MetricDefinition[];
  alerts: AlertConfiguration[];
  dashboards: DashboardConfig[];
  retention: RetentionPolicy;
}

interface MonitoredService {
  name: string;
  type: 'frontend' | 'backend' | 'database' | 'cache' | 'ml';
  endpoints: string[];
  healthCheck: string;
  sla: ServiceLevelAgreement;
}

interface ServiceLevelAgreement {
  availability: number; // percentage
  responseTime: number; // ms
  errorRate: number; // percentage
  throughput: number; // requests per second
}

interface MetricDefinition {
  name: string;
  type: 'counter' | 'gauge' | 'histogram' | 'summary';
  description: string;
  labels: string[];
  unit: string;
  aggregation: 'sum' | 'avg' | 'min' | 'max' | 'p50' | 'p95' | 'p99';
}

interface AlertConfiguration {
  name: string;
  severity: 'info' | 'warning' | 'critical';
  condition: string; // PromQL query
  threshold: number;
  duration: string; // e.g., "5m"
  channels: NotificationChannel[];
  runbook?: string;
}

interface NotificationChannel {
  type: 'slack' | 'email' | 'webhook' | 'pagerduty';
  target: string;
  template?: string;
}

interface DashboardConfig {
  name: string;
  category: 'business' | 'technical' | 'sre';
  panels: DashboardPanel[];
  refresh: string;
  timeRange: string;
}

interface DashboardPanel {
  title: string;
  type: 'graph' | 'stat' | 'table' | 'heatmap';
  query: string;
  visualization: any;
}

export class ProductionMonitoring {
  private config: MonitoringConfig;
  private metricsCollector: MetricsCollector;
  private alertManager: AlertManager;
  private dashboardManager: DashboardManager;

  constructor(config: MonitoringConfig) {
    this.config = config;
    this.metricsCollector = new MetricsCollector(config);
    this.alertManager = new AlertManager(config.alerts);
    this.dashboardManager = new DashboardManager(config.dashboards);
    
    this.initialize();
  }

  /**
   * Initialisation du système de monitoring
   */
  private async initialize() {
    console.log('🔍 Initializing Production Monitoring...');

    // Configurer la collecte de métriques
    await this.setupMetricsCollection();

    // Configurer les alertes
    await this.setupAlerting();

    // Créer les dashboards
    await this.setupDashboards();

    // Démarrer les health checks
    this.startHealthChecks();

    // Configurer la rétention des données
    await this.setupDataRetention();

    console.log('✅ Production Monitoring initialized successfully');
  }

  /**
   * Configuration de la collecte de métriques
   */
  private async setupMetricsCollection() {
    // Métriques business
    this.metricsCollector.registerMetric({
      name: 'business_revenue_total',
      type: 'counter',
      description: 'Total revenue generated',
      labels: ['currency', 'source'],
      unit: 'euros'
    });

    this.metricsCollector.registerMetric({
      name: 'business_conversion_rate',
      type: 'gauge',
      description: 'Current conversion rate',
      labels: ['funnel_step', 'segment'],
      unit: 'percentage'
    });

    // Métriques techniques
    this.metricsCollector.registerMetric({
      name: 'http_request_duration_seconds',
      type: 'histogram',
      description: 'HTTP request duration',
      labels: ['method', 'route', 'status_code'],
      unit: 'seconds'
    });

    this.metricsCollector.registerMetric({
      name: 'performance_navigation_timing',
      type: 'histogram',
      description: 'Navigation timing metrics',
      labels: ['page', 'device_type'],
      unit: 'milliseconds'
    });

    // Métriques ML
    this.metricsCollector.registerMetric({
      name: 'ml_model_prediction_accuracy',
      type: 'gauge',
      description: 'ML model prediction accuracy',
      labels: ['model_name', 'version'],
      unit: 'percentage'
    });

    this.metricsCollector.registerMetric({
      name: 'ml_model_inference_duration',
      type: 'histogram',
      description: 'ML model inference time',
      labels: ['model_name', 'version'],
      unit: 'milliseconds'
    });
  }

  /**
   * Configuration des alertes
   */
  private async setupAlerting() {
    const alertConfigurations: AlertConfiguration[] = [
      // Alertes Performance
      {
        name: 'High Response Time',
        severity: 'warning',
        condition: 'histogram_quantile(0.95, http_request_duration_seconds) > 0.5',
        threshold: 0.5,
        duration: '5m',
        channels: [
          { type: 'slack', target: '#alerts-performance' },
          { type: 'email', target: '<EMAIL>' }
        ],
        runbook: 'https://docs.retreat-and-be.com/runbooks/high-response-time'
      },
      
      // Alertes Business
      {
        name: 'Conversion Rate Drop',
        severity: 'critical',
        condition: 'business_conversion_rate < 0.02',
        threshold: 0.02,
        duration: '10m',
        channels: [
          { type: 'slack', target: '#alerts-business' },
          { type: 'email', target: '<EMAIL>' },
          { type: 'pagerduty', target: 'business-oncall' }
        ]
      },

      // Alertes ML
      {
        name: 'ML Model Accuracy Degradation',
        severity: 'warning',
        condition: 'ml_model_prediction_accuracy < 0.85',
        threshold: 0.85,
        duration: '15m',
        channels: [
          { type: 'slack', target: '#alerts-ml' },
          { type: 'email', target: '<EMAIL>' }
        ]
      },

      // Alertes Infrastructure
      {
        name: 'High Error Rate',
        severity: 'critical',
        condition: 'rate(http_requests_total{status=~"5.."}[5m]) > 0.01',
        threshold: 0.01,
        duration: '2m',
        channels: [
          { type: 'slack', target: '#alerts-infrastructure' },
          { type: 'pagerduty', target: 'sre-oncall' }
        ]
      }
    ];

    await this.alertManager.configureAlerts(alertConfigurations);
  }

  /**
   * Configuration des dashboards
   */
  private async setupDashboards() {
    // Dashboard Business
    const businessDashboard: DashboardConfig = {
      name: 'Business Metrics',
      category: 'business',
      refresh: '30s',
      timeRange: '24h',
      panels: [
        {
          title: 'Revenue Trend',
          type: 'graph',
          query: 'rate(business_revenue_total[1h])',
          visualization: {
            type: 'line',
            yAxis: { unit: 'euros/hour' }
          }
        },
        {
          title: 'Conversion Rate',
          type: 'stat',
          query: 'business_conversion_rate',
          visualization: {
            type: 'gauge',
            min: 0,
            max: 0.1,
            thresholds: [0.02, 0.05]
          }
        },
        {
          title: 'User Journey Funnel',
          type: 'table',
          query: 'business_conversion_rate by (funnel_step)',
          visualization: {
            type: 'funnel'
          }
        }
      ]
    };

    // Dashboard Performance
    const performanceDashboard: DashboardConfig = {
      name: 'Performance Metrics',
      category: 'technical',
      refresh: '10s',
      timeRange: '1h',
      panels: [
        {
          title: 'Response Time P95',
          type: 'graph',
          query: 'histogram_quantile(0.95, http_request_duration_seconds)',
          visualization: {
            type: 'line',
            yAxis: { unit: 'seconds' }
          }
        },
        {
          title: 'Navigation Timing',
          type: 'graph',
          query: 'histogram_quantile(0.95, performance_navigation_timing)',
          visualization: {
            type: 'line',
            yAxis: { unit: 'milliseconds' }
          }
        },
        {
          title: 'Error Rate',
          type: 'stat',
          query: 'rate(http_requests_total{status=~"5.."}[5m])',
          visualization: {
            type: 'gauge',
            max: 0.05,
            thresholds: [0.01, 0.03]
          }
        }
      ]
    };

    // Dashboard ML
    const mlDashboard: DashboardConfig = {
      name: 'ML Models Performance',
      category: 'technical',
      refresh: '1m',
      timeRange: '6h',
      panels: [
        {
          title: 'Model Accuracy',
          type: 'graph',
          query: 'ml_model_prediction_accuracy by (model_name)',
          visualization: {
            type: 'line',
            yAxis: { unit: 'percentage' }
          }
        },
        {
          title: 'Inference Time',
          type: 'graph',
          query: 'histogram_quantile(0.95, ml_model_inference_duration)',
          visualization: {
            type: 'line',
            yAxis: { unit: 'milliseconds' }
          }
        }
      ]
    };

    await this.dashboardManager.createDashboards([
      businessDashboard,
      performanceDashboard,
      mlDashboard
    ]);
  }

  /**
   * Health checks des services
   */
  private startHealthChecks() {
    setInterval(async () => {
      for (const service of this.config.services) {
        try {
          const startTime = Date.now();
          const response = await fetch(service.healthCheck, {
            timeout: 5000
          });
          const duration = Date.now() - startTime;

          // Enregistrer métriques de santé
          this.metricsCollector.recordMetric('service_health_check_duration', duration, {
            service: service.name,
            status: response.ok ? 'success' : 'failure'
          });

          this.metricsCollector.recordMetric('service_availability', response.ok ? 1 : 0, {
            service: service.name
          });

          // Vérifier SLA
          this.checkSLA(service, duration, response.ok);

        } catch (error) {
          this.metricsCollector.recordMetric('service_availability', 0, {
            service: service.name
          });

          console.error(`Health check failed for ${service.name}:`, error);
        }
      }
    }, 30000); // Toutes les 30 secondes
  }

  /**
   * Vérification des SLA
   */
  private checkSLA(service: MonitoredService, responseTime: number, isHealthy: boolean) {
    const sla = service.sla;

    // Vérifier temps de réponse
    if (responseTime > sla.responseTime) {
      this.alertManager.triggerAlert({
        service: service.name,
        metric: 'response_time',
        value: responseTime,
        threshold: sla.responseTime,
        severity: 'warning'
      });
    }

    // Vérifier disponibilité
    if (!isHealthy) {
      this.alertManager.triggerAlert({
        service: service.name,
        metric: 'availability',
        value: 0,
        threshold: sla.availability,
        severity: 'critical'
      });
    }
  }

  /**
   * Configuration de la rétention des données
   */
  private async setupDataRetention() {
    const retentionPolicies = {
      // Métriques haute fréquence (1s) gardées 24h
      high_frequency: {
        resolution: '1s',
        retention: '24h'
      },
      
      // Métriques moyennes (1m) gardées 7 jours
      medium_frequency: {
        resolution: '1m',
        retention: '7d'
      },
      
      // Métriques basses (1h) gardées 90 jours
      low_frequency: {
        resolution: '1h',
        retention: '90d'
      },
      
      // Agrégations journalières gardées 1 an
      daily_aggregates: {
        resolution: '1d',
        retention: '365d'
      }
    };

    await this.metricsCollector.configureRetention(retentionPolicies);
  }

  /**
   * API publique pour enregistrer des métriques custom
   */
  public recordBusinessMetric(name: string, value: number, labels: Record<string, string> = {}) {
    this.metricsCollector.recordMetric(`business_${name}`, value, labels);
  }

  public recordPerformanceMetric(name: string, value: number, labels: Record<string, string> = {}) {
    this.metricsCollector.recordMetric(`performance_${name}`, value, labels);
  }

  public recordMLMetric(name: string, value: number, labels: Record<string, string> = {}) {
    this.metricsCollector.recordMetric(`ml_${name}`, value, labels);
  }

  /**
   * Obtenir le statut global du système
   */
  public async getSystemStatus(): Promise<any> {
    const services = await Promise.all(
      this.config.services.map(async (service) => {
        const availability = await this.metricsCollector.getMetric('service_availability', {
          service: service.name
        });
        
        const responseTime = await this.metricsCollector.getMetric('service_health_check_duration', {
          service: service.name
        });

        return {
          name: service.name,
          status: availability > 0.99 ? 'healthy' : 'degraded',
          availability: availability * 100,
          responseTime,
          sla: service.sla
        };
      })
    );

    return {
      overall: services.every(s => s.status === 'healthy') ? 'healthy' : 'degraded',
      services,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Générer rapport de performance
   */
  public async generatePerformanceReport(timeRange: string = '24h'): Promise<any> {
    const metrics = await this.metricsCollector.getMetricsInRange(timeRange);
    
    return {
      timeRange,
      summary: {
        avgResponseTime: metrics.http_request_duration_seconds?.avg,
        p95ResponseTime: metrics.http_request_duration_seconds?.p95,
        errorRate: metrics.error_rate?.avg,
        availability: metrics.service_availability?.avg * 100
      },
      business: {
        revenue: metrics.business_revenue_total?.sum,
        conversionRate: metrics.business_conversion_rate?.avg,
        activeUsers: metrics.active_users?.max
      },
      ml: {
        modelAccuracy: metrics.ml_model_prediction_accuracy?.avg,
        inferenceTime: metrics.ml_model_inference_duration?.p95
      },
      generatedAt: new Date().toISOString()
    };
  }
}

/**
 * Classes utilitaires pour le monitoring
 */
class MetricsCollector {
  constructor(private config: MonitoringConfig) {}

  registerMetric(metric: MetricDefinition) {
    // Implémentation d'enregistrement de métrique
  }

  recordMetric(name: string, value: number, labels: Record<string, string>) {
    // Implémentation d'enregistrement de valeur
  }

  async getMetric(name: string, labels: Record<string, string>): Promise<number> {
    // Implémentation de récupération de métrique
    return 0;
  }

  async getMetricsInRange(timeRange: string): Promise<any> {
    // Implémentation de récupération sur période
    return {};
  }

  async configureRetention(policies: any) {
    // Implémentation de configuration de rétention
  }
}

class AlertManager {
  constructor(private alerts: AlertConfiguration[]) {}

  async configureAlerts(alerts: AlertConfiguration[]) {
    // Implémentation de configuration d'alertes
  }

  triggerAlert(alert: any) {
    // Implémentation de déclenchement d'alerte
  }
}

class DashboardManager {
  constructor(private dashboards: DashboardConfig[]) {}

  async createDashboards(dashboards: DashboardConfig[]) {
    // Implémentation de création de dashboards
  }
}

/**
 * Configuration par défaut pour production
 */
export const productionMonitoringConfig: MonitoringConfig = {
  environment: 'production',
  services: [
    {
      name: 'frontend',
      type: 'frontend',
      endpoints: ['/', '/search', '/booking'],
      healthCheck: '/health',
      sla: {
        availability: 99.9,
        responseTime: 200,
        errorRate: 0.1,
        throughput: 100
      }
    },
    {
      name: 'api-gateway',
      type: 'backend',
      endpoints: ['/api/v1'],
      healthCheck: '/api/health',
      sla: {
        availability: 99.95,
        responseTime: 100,
        errorRate: 0.05,
        throughput: 500
      }
    },
    {
      name: 'analytics-service',
      type: 'backend',
      endpoints: ['/analytics'],
      healthCheck: '/analytics/health',
      sla: {
        availability: 99.5,
        responseTime: 500,
        errorRate: 0.2,
        throughput: 200
      }
    },
    {
      name: 'ml-inference',
      type: 'ml',
      endpoints: ['/predict'],
      healthCheck: '/ml/health',
      sla: {
        availability: 99.0,
        responseTime: 100,
        errorRate: 0.5,
        throughput: 50
      }
    }
  ],
  metrics: [], // Défini dans setupMetricsCollection
  alerts: [], // Défini dans setupAlerting
  dashboards: [], // Défini dans setupDashboards
  retention: {
    shortTerm: '7d',
    mediumTerm: '30d',
    longTerm: '365d'
  }
};

export default ProductionMonitoring;
