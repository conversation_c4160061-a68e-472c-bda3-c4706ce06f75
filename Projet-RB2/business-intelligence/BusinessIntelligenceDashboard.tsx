/**
 * Business Intelligence Dashboard - Sprint 16 Jour 2
 * Dashboards avancés avec analytics temps réel et insights IA
 */

import React, { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  TrendingUp,
  TrendingDown,
  Users,
  DollarSign,
  Target,
  Brain,
  AlertTriangle,
  Calendar,
  Filter,
  Download,
  RefreshCw,
  Eye,
  MousePointer,
  Clock,
  Zap
} from 'lucide-react';
import {
  Card,
  Button,
  Select,
  Badge,
  Tooltip,
  useAnalytics,
  usePredictiveAnalytics
} from '@retreat-and-be/design-system';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  FunnelChart,
  Funnel,
  LabelList
} from 'recharts';

interface BusinessMetrics {
  revenue: {
    total: number;
    growth: number;
    forecast: number;
    bySource: Array<{ source: string; value: number; growth: number }>;
  };
  conversion: {
    rate: number;
    trend: number;
    byFunnel: Array<{ step: string; rate: number; users: number }>;
    predictions: Array<{ date: string; predicted: number; actual?: number }>;
  };
  users: {
    total: number;
    active: number;
    new: number;
    retention: number;
    churnRisk: number;
    ltv: number;
  };
  performance: {
    pageSpeed: number;
    uptime: number;
    errorRate: number;
    satisfaction: number;
  };
  engagement: {
    sessionDuration: number;
    pageViews: number;
    bounceRate: number;
    interactions: number;
  };
}

interface AIInsight {
  id: string;
  type: 'opportunity' | 'risk' | 'trend' | 'anomaly';
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  confidence: number;
  actionable: boolean;
  recommendations: string[];
  data: any;
}

export const BusinessIntelligenceDashboard: React.FC = () => {
  const { track } = useAnalytics();
  const { getPredictions, predictions } = usePredictiveAnalytics();
  
  const [timeRange, setTimeRange] = useState('7d');
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>(['revenue', 'conversion', 'users']);
  const [isLoading, setIsLoading] = useState(false);
  const [metrics, setMetrics] = useState<BusinessMetrics | null>(null);
  const [aiInsights, setAIInsights] = useState<AIInsight[]>([]);
  const [realTimeData, setRealTimeData] = useState<any>({});

  // Charger les données initiales
  useEffect(() => {
    loadBusinessMetrics();
    loadAIInsights();
    setupRealTimeUpdates();
  }, [timeRange]);

  // Charger les métriques business
  const loadBusinessMetrics = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/analytics/business-metrics?range=${timeRange}`);
      const data = await response.json();
      setMetrics(data);
      
      track('bi_dashboard_loaded', {
        timeRange,
        metricsCount: Object.keys(data).length
      });
    } catch (error) {
      console.error('Failed to load business metrics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Charger les insights IA
  const loadAIInsights = async () => {
    try {
      const response = await fetch(`/api/analytics/ai-insights?range=${timeRange}`);
      const insights = await response.json();
      setAIInsights(insights);
      
      track('ai_insights_loaded', {
        insightsCount: insights.length,
        highImpactCount: insights.filter((i: AIInsight) => i.impact === 'high').length
      });
    } catch (error) {
      console.error('Failed to load AI insights:', error);
    }
  };

  // Configuration des mises à jour temps réel
  const setupRealTimeUpdates = () => {
    const eventSource = new EventSource('/api/analytics/real-time');
    
    eventSource.onmessage = (event) => {
      const data = JSON.parse(event.data);
      setRealTimeData(prev => ({ ...prev, ...data }));
    };

    return () => eventSource.close();
  };

  // Métriques calculées
  const calculatedMetrics = useMemo(() => {
    if (!metrics) return null;

    return {
      revenueGrowth: metrics.revenue.growth,
      conversionImprovement: metrics.conversion.trend,
      userGrowth: ((metrics.users.new / metrics.users.total) * 100),
      churnReduction: (100 - metrics.users.churnRisk),
      performanceScore: (
        (metrics.performance.pageSpeed * 0.3) +
        (metrics.performance.uptime * 0.3) +
        ((100 - metrics.performance.errorRate) * 0.2) +
        (metrics.performance.satisfaction * 0.2)
      )
    };
  }, [metrics]);

  // Données pour les graphiques
  const revenueChartData = useMemo(() => {
    if (!metrics) return [];
    
    // Simuler des données temporelles
    const days = Array.from({ length: 30 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (29 - i));
      return {
        date: date.toISOString().split('T')[0],
        revenue: Math.floor(Math.random() * 5000) + 2000,
        forecast: Math.floor(Math.random() * 5500) + 2200,
        target: 4000
      };
    });
    
    return days;
  }, [metrics]);

  const conversionFunnelData = useMemo(() => {
    if (!metrics) return [];
    
    return metrics.conversion.byFunnel.map((step, index) => ({
      name: step.step,
      value: step.users,
      rate: step.rate,
      fill: `hsl(${220 + index * 30}, 70%, ${60 - index * 5}%)`
    }));
  }, [metrics]);

  if (!metrics) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-primary-500" />
          <p className="text-gray-600">Chargement des analytics...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header avec contrôles */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Business Intelligence</h1>
          <p className="text-gray-600 mt-1">Analytics avancées et insights IA temps réel</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <Select
            value={timeRange}
            onValueChange={setTimeRange}
            options={[
              { value: '1d', label: 'Dernières 24h' },
              { value: '7d', label: '7 derniers jours' },
              { value: '30d', label: '30 derniers jours' },
              { value: '90d', label: '3 derniers mois' }
            ]}
          />
          
          <Button
            variant="outline"
            onClick={loadBusinessMetrics}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Actualiser
          </Button>
          
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Exporter
          </Button>
        </div>
      </div>

      {/* KPIs principaux */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Revenus</p>
                <p className="text-2xl font-bold text-gray-900">
                  {new Intl.NumberFormat('fr-FR', {
                    style: 'currency',
                    currency: 'EUR'
                  }).format(metrics.revenue.total)}
                </p>
                <div className="flex items-center mt-1">
                  {metrics.revenue.growth > 0 ? (
                    <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                  )}
                  <span className={`text-sm ${metrics.revenue.growth > 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {metrics.revenue.growth > 0 ? '+' : ''}{metrics.revenue.growth.toFixed(1)}%
                  </span>
                </div>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <DollarSign className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Taux de Conversion</p>
                <p className="text-2xl font-bold text-gray-900">
                  {metrics.conversion.rate.toFixed(1)}%
                </p>
                <div className="flex items-center mt-1">
                  {metrics.conversion.trend > 0 ? (
                    <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                  )}
                  <span className={`text-sm ${metrics.conversion.trend > 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {metrics.conversion.trend > 0 ? '+' : ''}{metrics.conversion.trend.toFixed(1)}%
                  </span>
                </div>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <Target className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Utilisateurs Actifs</p>
                <p className="text-2xl font-bold text-gray-900">
                  {new Intl.NumberFormat('fr-FR').format(metrics.users.active)}
                </p>
                <div className="flex items-center mt-1">
                  <Users className="h-4 w-4 text-blue-500 mr-1" />
                  <span className="text-sm text-gray-600">
                    {metrics.users.new} nouveaux
                  </span>
                </div>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <Users className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Score Performance</p>
                <p className="text-2xl font-bold text-gray-900">
                  {calculatedMetrics?.performanceScore.toFixed(0)}/100
                </p>
                <div className="flex items-center mt-1">
                  <Zap className="h-4 w-4 text-yellow-500 mr-1" />
                  <span className="text-sm text-gray-600">
                    {metrics.performance.pageSpeed}ms avg
                  </span>
                </div>
              </div>
              <div className="p-3 bg-yellow-100 rounded-full">
                <Zap className="h-6 w-6 text-yellow-600" />
              </div>
            </div>
          </Card>
        </motion.div>
      </div>

      {/* Graphiques principaux */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Évolution du chiffre d'affaires */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Évolution du Chiffre d'Affaires</h3>
            <Badge variant="secondary">Prédictions IA</Badge>
          </div>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={revenueChartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <RechartsTooltip />
              <Legend />
              <Line
                type="monotone"
                dataKey="revenue"
                stroke="#3B82F6"
                strokeWidth={2}
                name="Revenus réels"
              />
              <Line
                type="monotone"
                dataKey="forecast"
                stroke="#10B981"
                strokeWidth={2}
                strokeDasharray="5 5"
                name="Prévisions IA"
              />
              <Line
                type="monotone"
                dataKey="target"
                stroke="#EF4444"
                strokeWidth={1}
                strokeDasharray="2 2"
                name="Objectif"
              />
            </LineChart>
          </ResponsiveContainer>
        </Card>

        {/* Funnel de conversion */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Funnel de Conversion</h3>
            <Badge variant="outline">Temps réel</Badge>
          </div>
          <ResponsiveContainer width="100%" height={300}>
            <FunnelChart>
              <Funnel
                dataKey="value"
                data={conversionFunnelData}
                isAnimationActive
              >
                <LabelList position="center" fill="#fff" stroke="none" />
              </Funnel>
              <RechartsTooltip />
            </FunnelChart>
          </ResponsiveContainer>
        </Card>
      </div>

      {/* Insights IA */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <Brain className="h-6 w-6 text-purple-600 mr-2" />
            <h3 className="text-lg font-semibold">Insights IA</h3>
          </div>
          <Badge variant="secondary">
            {aiInsights.length} insights détectés
          </Badge>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {aiInsights.map((insight, index) => (
            <motion.div
              key={insight.id}
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: index * 0.1 }}
              className={`p-4 rounded-lg border-l-4 ${
                insight.impact === 'high'
                  ? 'border-red-500 bg-red-50'
                  : insight.impact === 'medium'
                  ? 'border-yellow-500 bg-yellow-50'
                  : 'border-blue-500 bg-blue-50'
              }`}
            >
              <div className="flex items-start justify-between mb-2">
                <h4 className="font-medium text-gray-900">{insight.title}</h4>
                <Badge
                  variant={insight.impact === 'high' ? 'destructive' : 'secondary'}
                  className="text-xs"
                >
                  {insight.confidence}% confiance
                </Badge>
              </div>
              
              <p className="text-sm text-gray-600 mb-3">{insight.description}</p>
              
              {insight.actionable && (
                <div className="space-y-1">
                  <p className="text-xs font-medium text-gray-700">Recommandations:</p>
                  {insight.recommendations.slice(0, 2).map((rec, i) => (
                    <p key={i} className="text-xs text-gray-600">• {rec}</p>
                  ))}
                </div>
              )}
            </motion.div>
          ))}
        </div>
      </Card>

      {/* Métriques temps réel */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Activité Temps Réel</h3>
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2"></div>
              <span className="text-sm text-gray-600">Live</span>
            </div>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Visiteurs en ligne</span>
              <span className="font-semibold">{realTimeData.onlineUsers || 127}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Pages vues/min</span>
              <span className="font-semibold">{realTimeData.pageViewsPerMin || 45}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Conversions/h</span>
              <span className="font-semibold">{realTimeData.conversionsPerHour || 8}</span>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Top Pages</h3>
          <div className="space-y-3">
            {[
              { page: '/search', views: 1234, change: +12 },
              { page: '/retreat-details', views: 987, change: +8 },
              { page: '/booking', views: 654, change: -3 },
              { page: '/ai-chat', views: 432, change: +25 }
            ].map((item, index) => (
              <div key={index} className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium">{item.page}</p>
                  <p className="text-xs text-gray-600">{item.views} vues</p>
                </div>
                <Badge
                  variant={item.change > 0 ? 'default' : 'destructive'}
                  className="text-xs"
                >
                  {item.change > 0 ? '+' : ''}{item.change}%
                </Badge>
              </div>
            ))}
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Alertes Système</h3>
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="h-4 w-4 text-yellow-500 mt-0.5" />
              <div>
                <p className="text-sm font-medium">Pic de trafic détecté</p>
                <p className="text-xs text-gray-600">+150% sur /search</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <TrendingUp className="h-4 w-4 text-green-500 mt-0.5" />
              <div>
                <p className="text-sm font-medium">Conversion en hausse</p>
                <p className="text-xs text-gray-600">+23% dernière heure</p>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default BusinessIntelligenceDashboard;
