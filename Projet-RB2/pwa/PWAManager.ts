/**
 * PWA Manager - Sprint 17 Jour 1
 * Gestionnaire PWA révolutionnaire avec offline-first et performance <100ms mobile
 */

interface PWAConfig {
  name: string;
  shortName: string;
  description: string;
  themeColor: string;
  backgroundColor: string;
  display: 'standalone' | 'fullscreen' | 'minimal-ui' | 'browser';
  orientation: 'portrait' | 'landscape' | 'any';
  scope: string;
  startUrl: string;
  icons: PWAIcon[];
  capabilities: PWACapability[];
  offlineStrategy: OfflineStrategy;
  updateStrategy: UpdateStrategy;
}

interface PWAIcon {
  src: string;
  sizes: string;
  type: string;
  purpose?: 'any' | 'maskable' | 'monochrome';
}

interface PWACapability {
  name: string;
  enabled: boolean;
  config?: any;
}

interface OfflineStrategy {
  enableOfflineMode: boolean;
  offlinePages: string[];
  offlineAssets: string[];
  offlineData: OfflineDataConfig[];
  fallbackStrategy: 'cache' | 'offline-page' | 'custom';
}

interface OfflineDataConfig {
  key: string;
  endpoint: string;
  ttl: number; // Time to live in ms
  strategy: 'cache-first' | 'network-first' | 'stale-while-revalidate';
}

interface UpdateStrategy {
  autoUpdate: boolean;
  updateInterval: number; // ms
  promptUser: boolean;
  forceUpdate: boolean;
  rollbackOnError: boolean;
}

interface InstallPromptEvent extends Event {
  prompt(): Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

export class PWAManager {
  private config: PWAConfig;
  private serviceWorker?: ServiceWorkerRegistration;
  private installPrompt?: InstallPromptEvent;
  private isOnline = navigator.onLine;
  private offlineQueue: Array<{ url: string; options: RequestInit }> = [];
  private updateAvailable = false;

  constructor(config: PWAConfig) {
    this.config = config;
    this.initialize();
  }

  /**
   * Initialisation du PWA Manager
   */
  private async initialize() {
    console.log('📱 Initializing PWA Manager...');

    // Générer et injecter le manifest
    await this.generateManifest();

    // Enregistrer le Service Worker
    await this.registerServiceWorker();

    // Configurer les événements PWA
    this.setupPWAEvents();

    // Configurer la gestion offline
    this.setupOfflineHandling();

    // Configurer les mises à jour
    this.setupUpdateHandling();

    // Configurer les capacités avancées
    await this.setupAdvancedCapabilities();

    console.log('✅ PWA Manager initialized successfully');
  }

  /**
   * Génération et injection du manifest PWA
   */
  private async generateManifest() {
    const manifest = {
      name: this.config.name,
      short_name: this.config.shortName,
      description: this.config.description,
      theme_color: this.config.themeColor,
      background_color: this.config.backgroundColor,
      display: this.config.display,
      orientation: this.config.orientation,
      scope: this.config.scope,
      start_url: this.config.startUrl,
      icons: this.config.icons,
      
      // Capacités avancées
      categories: ['lifestyle', 'travel', 'wellness'],
      lang: 'fr',
      dir: 'ltr',
      
      // Raccourcis d'application
      shortcuts: [
        {
          name: 'Rechercher Retraites',
          short_name: 'Recherche',
          description: 'Rechercher des retraites rapidement',
          url: '/search',
          icons: [{ src: '/icons/search-96x96.png', sizes: '96x96' }]
        },
        {
          name: 'Mes Réservations',
          short_name: 'Réservations',
          description: 'Voir mes réservations',
          url: '/bookings',
          icons: [{ src: '/icons/bookings-96x96.png', sizes: '96x96' }]
        },
        {
          name: 'Chat IA',
          short_name: 'IA',
          description: 'Assistant IA personnel',
          url: '/ai-chat',
          icons: [{ src: '/icons/ai-96x96.png', sizes: '96x96' }]
        }
      ],

      // Protocoles personnalisés
      protocol_handlers: [
        {
          protocol: 'web+retreat',
          url: '/retreat?id=%s'
        }
      ],

      // Partage de fichiers
      share_target: {
        action: '/share',
        method: 'POST',
        enctype: 'multipart/form-data',
        params: {
          title: 'title',
          text: 'text',
          url: 'url',
          files: [
            {
              name: 'files',
              accept: ['image/*', '.pdf']
            }
          ]
        }
      },

      // Capture d'écran
      screenshots: [
        {
          src: '/screenshots/mobile-home.png',
          sizes: '390x844',
          type: 'image/png',
          form_factor: 'narrow'
        },
        {
          src: '/screenshots/desktop-home.png',
          sizes: '1920x1080',
          type: 'image/png',
          form_factor: 'wide'
        }
      ]
    };

    // Créer et injecter le manifest
    const manifestBlob = new Blob([JSON.stringify(manifest, null, 2)], {
      type: 'application/json'
    });
    const manifestUrl = URL.createObjectURL(manifestBlob);

    const link = document.createElement('link');
    link.rel = 'manifest';
    link.href = manifestUrl;
    document.head.appendChild(link);

    // Meta tags pour iOS
    this.addIOSMetaTags();
  }

  /**
   * Ajout des meta tags pour iOS
   */
  private addIOSMetaTags() {
    const metaTags = [
      { name: 'apple-mobile-web-app-capable', content: 'yes' },
      { name: 'apple-mobile-web-app-status-bar-style', content: 'default' },
      { name: 'apple-mobile-web-app-title', content: this.config.shortName },
      { name: 'mobile-web-app-capable', content: 'yes' },
      { name: 'msapplication-TileColor', content: this.config.themeColor },
      { name: 'msapplication-tap-highlight', content: 'no' }
    ];

    metaTags.forEach(tag => {
      const meta = document.createElement('meta');
      meta.name = tag.name;
      meta.content = tag.content;
      document.head.appendChild(meta);
    });

    // Icônes Apple Touch
    const appleTouchIcons = [
      { sizes: '180x180', href: '/icons/apple-touch-icon-180x180.png' },
      { sizes: '152x152', href: '/icons/apple-touch-icon-152x152.png' },
      { sizes: '144x144', href: '/icons/apple-touch-icon-144x144.png' },
      { sizes: '120x120', href: '/icons/apple-touch-icon-120x120.png' }
    ];

    appleTouchIcons.forEach(icon => {
      const link = document.createElement('link');
      link.rel = 'apple-touch-icon';
      link.sizes = icon.sizes;
      link.href = icon.href;
      document.head.appendChild(link);
    });
  }

  /**
   * Enregistrement du Service Worker
   */
  private async registerServiceWorker() {
    if ('serviceWorker' in navigator) {
      try {
        this.serviceWorker = await navigator.serviceWorker.register('/sw-pwa.js', {
          scope: this.config.scope
        });

        console.log('✅ Service Worker registered successfully');

        // Configurer la communication avec le SW
        this.setupServiceWorkerCommunication();

      } catch (error) {
        console.error('❌ Service Worker registration failed:', error);
      }
    }
  }

  /**
   * Configuration de la communication avec le Service Worker
   */
  private setupServiceWorkerCommunication() {
    if (!this.serviceWorker) return;

    // Écouter les messages du Service Worker
    navigator.serviceWorker.addEventListener('message', (event) => {
      const { type, data } = event.data;

      switch (type) {
        case 'CACHE_UPDATED':
          this.handleCacheUpdate(data);
          break;
        case 'OFFLINE_READY':
          this.handleOfflineReady();
          break;
        case 'UPDATE_AVAILABLE':
          this.handleUpdateAvailable();
          break;
        case 'BACKGROUND_SYNC':
          this.handleBackgroundSync(data);
          break;
      }
    });

    // Envoyer la configuration au Service Worker
    this.sendMessageToSW('CONFIGURE', {
      offlineStrategy: this.config.offlineStrategy,
      updateStrategy: this.config.updateStrategy
    });
  }

  /**
   * Configuration des événements PWA
   */
  private setupPWAEvents() {
    // Événement d'installation
    window.addEventListener('beforeinstallprompt', (event) => {
      event.preventDefault();
      this.installPrompt = event as InstallPromptEvent;
      this.showInstallButton();
    });

    // Événement d'installation réussie
    window.addEventListener('appinstalled', () => {
      console.log('🎉 PWA installed successfully');
      this.hideInstallButton();
      this.trackEvent('pwa_installed');
    });

    // Changement d'état de connexion
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.handleOnlineStateChange(true);
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
      this.handleOnlineStateChange(false);
    });

    // Visibilité de la page
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible') {
        this.handleAppForeground();
      } else {
        this.handleAppBackground();
      }
    });
  }

  /**
   * Configuration de la gestion offline
   */
  private setupOfflineHandling() {
    if (!this.config.offlineStrategy.enableOfflineMode) return;

    // Intercepter les requêtes fetch pour la queue offline
    const originalFetch = window.fetch;
    window.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
      try {
        return await originalFetch(input, init);
      } catch (error) {
        if (!this.isOnline && this.shouldQueueRequest(input, init)) {
          this.queueOfflineRequest(input, init);
          throw new Error('Request queued for when online');
        }
        throw error;
      }
    };

    // Traiter la queue offline quand on revient en ligne
    window.addEventListener('online', () => {
      this.processOfflineQueue();
    });
  }

  /**
   * Configuration de la gestion des mises à jour
   */
  private setupUpdateHandling() {
    if (!this.config.updateStrategy.autoUpdate) return;

    // Vérifier les mises à jour périodiquement
    setInterval(() => {
      this.checkForUpdates();
    }, this.config.updateStrategy.updateInterval);

    // Vérifier au focus de l'application
    window.addEventListener('focus', () => {
      this.checkForUpdates();
    });
  }

  /**
   * Configuration des capacités avancées
   */
  private async setupAdvancedCapabilities() {
    for (const capability of this.config.capabilities) {
      if (!capability.enabled) continue;

      switch (capability.name) {
        case 'push-notifications':
          await this.setupPushNotifications(capability.config);
          break;
        case 'background-sync':
          await this.setupBackgroundSync(capability.config);
          break;
        case 'geolocation':
          await this.setupGeolocation(capability.config);
          break;
        case 'camera':
          await this.setupCamera(capability.config);
          break;
        case 'file-handling':
          await this.setupFileHandling(capability.config);
          break;
        case 'web-share':
          await this.setupWebShare(capability.config);
          break;
      }
    }
  }

  /**
   * Configuration des notifications push
   */
  private async setupPushNotifications(config: any) {
    if (!('Notification' in window) || !('serviceWorker' in navigator)) {
      console.warn('Push notifications not supported');
      return;
    }

    try {
      const permission = await Notification.requestPermission();
      
      if (permission === 'granted') {
        const subscription = await this.serviceWorker?.pushManager.subscribe({
          userVisibleOnly: true,
          applicationServerKey: config.vapidPublicKey
        });

        if (subscription) {
          await this.sendSubscriptionToServer(subscription);
          console.log('✅ Push notifications enabled');
        }
      }
    } catch (error) {
      console.error('Push notification setup failed:', error);
    }
  }

  /**
   * Configuration de la synchronisation en arrière-plan
   */
  private async setupBackgroundSync(config: any) {
    if (!('serviceWorker' in navigator) || !('sync' in window.ServiceWorkerRegistration.prototype)) {
      console.warn('Background sync not supported');
      return;
    }

    // Enregistrer les tags de synchronisation
    const syncTags = config.syncTags || ['background-sync'];
    
    for (const tag of syncTags) {
      try {
        await this.serviceWorker?.sync.register(tag);
        console.log(`✅ Background sync registered: ${tag}`);
      } catch (error) {
        console.error(`Background sync registration failed for ${tag}:`, error);
      }
    }
  }

  /**
   * Configuration de la géolocalisation
   */
  private async setupGeolocation(config: any) {
    if (!('geolocation' in navigator)) {
      console.warn('Geolocation not supported');
      return;
    }

    try {
      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject, {
          enableHighAccuracy: config.enableHighAccuracy || true,
          timeout: config.timeout || 10000,
          maximumAge: config.maximumAge || 300000
        });
      });

      console.log('✅ Geolocation enabled:', position.coords);
      this.trackEvent('geolocation_enabled');
      
    } catch (error) {
      console.error('Geolocation setup failed:', error);
    }
  }

  /**
   * API publique pour l'installation
   */
  public async promptInstall(): Promise<boolean> {
    if (!this.installPrompt) {
      console.warn('Install prompt not available');
      return false;
    }

    try {
      await this.installPrompt.prompt();
      const choice = await this.installPrompt.userChoice;
      
      this.trackEvent('install_prompt_shown', { outcome: choice.outcome });
      
      return choice.outcome === 'accepted';
    } catch (error) {
      console.error('Install prompt failed:', error);
      return false;
    }
  }

  /**
   * API publique pour les notifications
   */
  public async sendNotification(title: string, options: NotificationOptions = {}) {
    if (!('Notification' in window)) return;

    if (Notification.permission === 'granted') {
      const notification = new Notification(title, {
        icon: '/icons/icon-192x192.png',
        badge: '/icons/badge-72x72.png',
        ...options
      });

      notification.onclick = () => {
        window.focus();
        notification.close();
      };

      return notification;
    }
  }

  /**
   * API publique pour le partage
   */
  public async share(data: ShareData): Promise<boolean> {
    if ('share' in navigator) {
      try {
        await navigator.share(data);
        this.trackEvent('content_shared', { method: 'native' });
        return true;
      } catch (error) {
        console.error('Native share failed:', error);
      }
    }

    // Fallback vers partage personnalisé
    this.showCustomShareDialog(data);
    return false;
  }

  /**
   * Utilitaires privés
   */
  private showInstallButton() {
    const installButton = document.getElementById('pwa-install-button');
    if (installButton) {
      installButton.style.display = 'block';
    }
  }

  private hideInstallButton() {
    const installButton = document.getElementById('pwa-install-button');
    if (installButton) {
      installButton.style.display = 'none';
    }
  }

  private handleOnlineStateChange(isOnline: boolean) {
    const statusIndicator = document.getElementById('connection-status');
    if (statusIndicator) {
      statusIndicator.textContent = isOnline ? 'En ligne' : 'Hors ligne';
      statusIndicator.className = isOnline ? 'online' : 'offline';
    }

    this.trackEvent('connection_changed', { isOnline });
  }

  private shouldQueueRequest(input: RequestInfo | URL, init?: RequestInit): boolean {
    const url = typeof input === 'string' ? input : input.toString();
    const method = init?.method || 'GET';
    
    // Queue seulement les requêtes POST/PUT/PATCH vers l'API
    return ['POST', 'PUT', 'PATCH'].includes(method) && url.includes('/api/');
  }

  private queueOfflineRequest(input: RequestInfo | URL, init?: RequestInit) {
    const url = typeof input === 'string' ? input : input.toString();
    this.offlineQueue.push({ url, options: init || {} });
    
    console.log(`📤 Request queued for offline: ${url}`);
  }

  private async processOfflineQueue() {
    console.log(`📥 Processing ${this.offlineQueue.length} queued requests`);
    
    const queue = [...this.offlineQueue];
    this.offlineQueue = [];

    for (const request of queue) {
      try {
        await fetch(request.url, request.options);
        console.log(`✅ Offline request processed: ${request.url}`);
      } catch (error) {
        console.error(`❌ Failed to process offline request: ${request.url}`, error);
        // Re-queue si échec
        this.offlineQueue.push(request);
      }
    }
  }

  private async checkForUpdates() {
    if (!this.serviceWorker) return;

    try {
      await this.serviceWorker.update();
    } catch (error) {
      console.error('Update check failed:', error);
    }
  }

  private handleCacheUpdate(data: any) {
    console.log('📦 Cache updated:', data);
  }

  private handleOfflineReady() {
    console.log('📱 App ready for offline use');
    this.sendNotification('Application prête', {
      body: 'L\'application est maintenant disponible hors ligne',
      tag: 'offline-ready'
    });
  }

  private handleUpdateAvailable() {
    this.updateAvailable = true;
    
    if (this.config.updateStrategy.promptUser) {
      this.showUpdatePrompt();
    } else if (this.config.updateStrategy.forceUpdate) {
      this.applyUpdate();
    }
  }

  private showUpdatePrompt() {
    const updateBanner = document.createElement('div');
    updateBanner.className = 'update-banner';
    updateBanner.innerHTML = `
      <div class="update-content">
        <span>Une nouvelle version est disponible</span>
        <button onclick="window.pwaManager.applyUpdate()">Mettre à jour</button>
        <button onclick="this.parentElement.parentElement.remove()">Plus tard</button>
      </div>
    `;
    document.body.appendChild(updateBanner);
  }

  public async applyUpdate() {
    if (!this.serviceWorker || !this.updateAvailable) return;

    try {
      await this.serviceWorker.waiting?.postMessage({ type: 'SKIP_WAITING' });
      window.location.reload();
    } catch (error) {
      console.error('Update failed:', error);
    }
  }

  private handleBackgroundSync(data: any) {
    console.log('🔄 Background sync completed:', data);
  }

  private handleAppForeground() {
    this.checkForUpdates();
    this.trackEvent('app_foreground');
  }

  private handleAppBackground() {
    this.trackEvent('app_background');
  }

  private async sendSubscriptionToServer(subscription: PushSubscription) {
    try {
      await fetch('/api/push/subscribe', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(subscription)
      });
    } catch (error) {
      console.error('Failed to send subscription to server:', error);
    }
  }

  private showCustomShareDialog(data: ShareData) {
    // Implémentation du dialog de partage personnalisé
    console.log('Custom share dialog:', data);
  }

  private sendMessageToSW(type: string, data?: any) {
    if (this.serviceWorker?.active) {
      this.serviceWorker.active.postMessage({ type, data });
    }
  }

  private trackEvent(event: string, data?: any) {
    // Intégration avec le système d'analytics
    if ((window as any).analytics) {
      (window as any).analytics.track(`pwa_${event}`, data);
    }
  }

  /**
   * API publique pour obtenir le statut PWA
   */
  public getStatus() {
    return {
      isInstalled: window.matchMedia('(display-mode: standalone)').matches,
      isOnline: this.isOnline,
      updateAvailable: this.updateAvailable,
      serviceWorkerReady: !!this.serviceWorker,
      installPromptAvailable: !!this.installPrompt,
      offlineQueueSize: this.offlineQueue.length
    };
  }

  /**
   * Nettoyage
   */
  public destroy() {
    // Nettoyer les event listeners et ressources
    this.offlineQueue = [];
    this.installPrompt = undefined;
  }
}

export default PWAManager;
