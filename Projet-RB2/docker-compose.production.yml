# Docker Compose Production - Sprint 15 Finalisé
# Configuration optimisée pour déploiement production cross-services

version: '3.8'

services:
  # Frontend Principal avec Design System v2.0.0
  frontend:
    build:
      context: ./Front-Audrey-V1-Main-main
      dockerfile: Dockerfile.production
      args:
        - NODE_ENV=production
        - REACT_APP_API_URL=${API_URL:-https://api.retreatandbe.com}
        - REACT_APP_DESIGN_SYSTEM_VERSION=2.0.0
    ports:
      - "80:80"
      - "443:443"
    environment:
      - NODE_ENV=production
      - REACT_APP_API_URL=${API_URL}
      - REACT_APP_WS_URL=${WS_URL}
      - REACT_APP_MONITORING_URL=${MONITORING_URL}
    volumes:
      - ./ssl:/etc/ssl/certs:ro
      - ./logs/frontend:/var/log/nginx
    networks:
      - retreat-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.frontend.rule=Host(`retreatandbe.com`)"
      - "traefik.http.routers.frontend.tls=true"

  # Agent IA avec ChatWindow Unifié
  agent-ia:
    build:
      context: ./Agent IA
      dockerfile: Dockerfile.production
      args:
        - NODE_ENV=production
        - DESIGN_SYSTEM_VERSION=2.0.0
    ports:
      - "3001:3000"
    environment:
      - NODE_ENV=production
      - API_URL=${API_URL}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - REDIS_URL=${REDIS_URL}
    volumes:
      - ./logs/agent-ia:/app/logs
    networks:
      - retreat-network
    restart: unless-stopped
    depends_on:
      - backend
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend NestJS avec APIs Cross-Services
  backend:
    build:
      context: ./Backend-NestJS
      dockerfile: Dockerfile.production
      args:
        - NODE_ENV=production
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_URL=${REDIS_URL}
      - CORS_ORIGINS=https://retreatandbe.com,https://ai.retreatandbe.com
      - MICROSERVICES_ENABLED=true
    volumes:
      - ./logs/backend:/app/logs
      - ./uploads:/app/uploads
    networks:
      - retreat-network
    restart: unless-stopped
    depends_on:
      - postgres
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Service Financier
  financial-service:
    build:
      context: ./Financial-Management
      dockerfile: Dockerfile.production
    ports:
      - "3002:3000"
    environment:
      - NODE_ENV=production
      - API_URL=${API_URL}
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
      - PAYPAL_CLIENT_ID=${PAYPAL_CLIENT_ID}
      - DESIGN_SYSTEM_VERSION=2.0.0
    volumes:
      - ./logs/financial:/app/logs
    networks:
      - retreat-network
    restart: unless-stopped
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Service Social
  social-service:
    build:
      context: ./Social
      dockerfile: Dockerfile.production
    ports:
      - "3003:3000"
    environment:
      - NODE_ENV=production
      - API_URL=${API_URL}
      - DESIGN_SYSTEM_VERSION=2.0.0
    volumes:
      - ./logs/social:/app/logs
    networks:
      - retreat-network
    restart: unless-stopped
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Base de données PostgreSQL
  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-retreatandbe}
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    networks:
      - retreat-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis pour cache et sessions
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - retreat-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring avec Prometheus
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - retreat-network
    restart: unless-stopped

  # Grafana pour visualisation
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3004:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - retreat-network
    restart: unless-stopped
    depends_on:
      - prometheus

  # Reverse Proxy Traefik
  traefik:
    image: traefik:v3.0
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"
    command:
      - --api.dashboard=true
      - --api.insecure=true
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
      - --certificatesresolvers.letsencrypt.acme.email=${ACME_EMAIL}
      - --certificatesresolvers.letsencrypt.acme.storage=/acme.json
      - --certificatesresolvers.letsencrypt.acme.httpchallenge.entrypoint=web
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./acme.json:/acme.json
    networks:
      - retreat-network
    restart: unless-stopped

  # Backup automatique
  backup:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-retreatandbe}
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - ./backups:/backups
      - ./scripts/backup.sh:/backup.sh
    command: |
      sh -c "
        while true; do
          sleep 86400
          /backup.sh
        done
      "
    networks:
      - retreat-network
    restart: unless-stopped
    depends_on:
      - postgres

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  retreat-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
