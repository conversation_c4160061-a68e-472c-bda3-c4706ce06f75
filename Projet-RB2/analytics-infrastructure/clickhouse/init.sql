-- ClickHouse Schema pour Analytics - Sprint 16
-- Tables optimisées pour analytics temps réel et business intelligence

-- Database creation
CREATE DATABASE IF NOT EXISTS analytics;
USE analytics;

-- Table des événements utilisateur
CREATE TABLE IF NOT EXISTS user_events (
    event_id String,
    user_id String,
    session_id String,
    event_type String,
    event_name String,
    timestamp DateTime64(3),
    page_url String,
    referrer String,
    user_agent String,
    ip_address String,
    country String,
    city String,
    device_type String,
    browser String,
    os String,
    screen_resolution String,
    properties Map(String, String),
    created_at DateTime64(3) DEFAULT now64()
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (timestamp, user_id, event_type)
TTL timestamp + INTERVAL 2 YEAR;

-- Table des événements business
CREATE TABLE IF NOT EXISTS business_events (
    event_id String,
    user_id String,
    session_id String,
    event_type String,
    event_name String,
    timestamp DateTime64(3),
    service String,
    action String,
    category String,
    value Float64,
    currency String,
    properties Map(String, String),
    created_at DateTime64(3) DEFAULT now64()
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (timestamp, service, event_type)
TTL timestamp + INTERVAL 5 YEAR;

-- Table des métriques de performance
CREATE TABLE IF NOT EXISTS performance_metrics (
    metric_id String,
    user_id String,
    session_id String,
    timestamp DateTime64(3),
    page_url String,
    metric_name String,
    metric_value Float64,
    metric_unit String,
    browser String,
    device_type String,
    connection_type String,
    properties Map(String, String),
    created_at DateTime64(3) DEFAULT now64()
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (timestamp, metric_name, page_url)
TTL timestamp + INTERVAL 1 YEAR;

-- Table des sessions utilisateur
CREATE TABLE IF NOT EXISTS user_sessions (
    session_id String,
    user_id String,
    start_time DateTime64(3),
    end_time DateTime64(3),
    duration_seconds UInt32,
    page_views UInt32,
    events_count UInt32,
    bounce Boolean,
    conversion Boolean,
    revenue Float64,
    entry_page String,
    exit_page String,
    referrer String,
    utm_source String,
    utm_medium String,
    utm_campaign String,
    device_type String,
    browser String,
    os String,
    country String,
    city String,
    created_at DateTime64(3) DEFAULT now64()
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(start_time)
ORDER BY (start_time, user_id)
TTL start_time + INTERVAL 2 YEAR;

-- Table des conversions
CREATE TABLE IF NOT EXISTS conversions (
    conversion_id String,
    user_id String,
    session_id String,
    timestamp DateTime64(3),
    conversion_type String,
    conversion_value Float64,
    currency String,
    funnel_step String,
    attribution_source String,
    attribution_medium String,
    attribution_campaign String,
    time_to_conversion_seconds UInt32,
    properties Map(String, String),
    created_at DateTime64(3) DEFAULT now64()
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (timestamp, conversion_type, user_id)
TTL timestamp + INTERVAL 7 YEAR;

-- Table des erreurs et exceptions
CREATE TABLE IF NOT EXISTS error_events (
    error_id String,
    user_id String,
    session_id String,
    timestamp DateTime64(3),
    error_type String,
    error_message String,
    error_stack String,
    page_url String,
    user_agent String,
    browser String,
    os String,
    severity String,
    resolved Boolean DEFAULT false,
    properties Map(String, String),
    created_at DateTime64(3) DEFAULT now64()
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (timestamp, error_type, severity)
TTL timestamp + INTERVAL 1 YEAR;

-- Table des A/B tests
CREATE TABLE IF NOT EXISTS ab_test_events (
    test_id String,
    user_id String,
    session_id String,
    timestamp DateTime64(3),
    experiment_name String,
    variant String,
    event_type String,
    converted Boolean,
    conversion_value Float64,
    properties Map(String, String),
    created_at DateTime64(3) DEFAULT now64()
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (timestamp, experiment_name, variant)
TTL timestamp + INTERVAL 2 YEAR;

-- Vues matérialisées pour analytics temps réel

-- Vue des métriques quotidiennes
CREATE MATERIALIZED VIEW IF NOT EXISTS daily_metrics
ENGINE = SummingMergeTree()
PARTITION BY toYYYYMM(date)
ORDER BY (date, event_type, service)
AS SELECT
    toDate(timestamp) as date,
    event_type,
    service,
    count() as events_count,
    uniq(user_id) as unique_users,
    uniq(session_id) as unique_sessions,
    sum(value) as total_value
FROM business_events
GROUP BY date, event_type, service;

-- Vue des métriques horaires
CREATE MATERIALIZED VIEW IF NOT EXISTS hourly_metrics
ENGINE = SummingMergeTree()
PARTITION BY toYYYYMM(hour)
ORDER BY (hour, event_type)
AS SELECT
    toStartOfHour(timestamp) as hour,
    event_type,
    count() as events_count,
    uniq(user_id) as unique_users,
    avg(if(event_name = 'page_load', toFloat64(properties['duration']), null)) as avg_page_load_time
FROM user_events
GROUP BY hour, event_type;

-- Vue des conversions par funnel
CREATE MATERIALIZED VIEW IF NOT EXISTS funnel_conversions
ENGINE = SummingMergeTree()
PARTITION BY toYYYYMM(date)
ORDER BY (date, funnel_step)
AS SELECT
    toDate(timestamp) as date,
    funnel_step,
    count() as conversions_count,
    sum(conversion_value) as total_value,
    uniq(user_id) as unique_converters
FROM conversions
GROUP BY date, funnel_step;

-- Vue des performances par page
CREATE MATERIALIZED VIEW IF NOT EXISTS page_performance
ENGINE = AggregatingMergeTree()
PARTITION BY toYYYYMM(date)
ORDER BY (date, page_url)
AS SELECT
    toDate(timestamp) as date,
    page_url,
    metric_name,
    avgState(metric_value) as avg_value,
    quantileState(0.5)(metric_value) as median_value,
    quantileState(0.95)(metric_value) as p95_value,
    countState() as measurements_count
FROM performance_metrics
GROUP BY date, page_url, metric_name;

-- Indexes pour optimisation des requêtes
CREATE INDEX IF NOT EXISTS idx_user_events_user_id ON user_events (user_id) TYPE bloom_filter GRANULARITY 1;
CREATE INDEX IF NOT EXISTS idx_user_events_session_id ON user_events (session_id) TYPE bloom_filter GRANULARITY 1;
CREATE INDEX IF NOT EXISTS idx_business_events_service ON business_events (service) TYPE bloom_filter GRANULARITY 1;
CREATE INDEX IF NOT EXISTS idx_performance_metrics_page ON performance_metrics (page_url) TYPE bloom_filter GRANULARITY 1;

-- Fonctions pour analytics

-- Fonction de calcul du taux de conversion
CREATE OR REPLACE FUNCTION conversion_rate(start_date Date, end_date Date, funnel_step String)
RETURNS Float64
AS $$
    SELECT 
        (SELECT count() FROM conversions WHERE toDate(timestamp) BETWEEN start_date AND end_date AND funnel_step = funnel_step) /
        (SELECT count() FROM user_sessions WHERE toDate(start_time) BETWEEN start_date AND end_date) * 100
$$;

-- Fonction de calcul de la rétention
CREATE OR REPLACE FUNCTION user_retention(cohort_date Date, period_days UInt32)
RETURNS Float64
AS $$
    WITH cohort_users AS (
        SELECT DISTINCT user_id
        FROM user_sessions
        WHERE toDate(start_time) = cohort_date
    ),
    retained_users AS (
        SELECT DISTINCT s.user_id
        FROM user_sessions s
        INNER JOIN cohort_users c ON s.user_id = c.user_id
        WHERE toDate(s.start_time) = cohort_date + INTERVAL period_days DAY
    )
    SELECT 
        (SELECT count() FROM retained_users) / 
        (SELECT count() FROM cohort_users) * 100
$$;

-- Insertion de données de test
INSERT INTO user_events VALUES
    ('test_1', 'user_1', 'session_1', 'page_view', 'page_load', now64(), '/', '', 'Mozilla/5.0', '127.0.0.1', 'FR', 'Paris', 'desktop', 'Chrome', 'Windows', '1920x1080', {'duration': '1200'}, now64()),
    ('test_2', 'user_1', 'session_1', 'interaction', 'button_click', now64(), '/', '', 'Mozilla/5.0', '127.0.0.1', 'FR', 'Paris', 'desktop', 'Chrome', 'Windows', '1920x1080', {'button_id': 'cta_main'}, now64()),
    ('test_3', 'user_2', 'session_2', 'page_view', 'page_load', now64(), '/search', '', 'Mozilla/5.0', '127.0.0.1', 'FR', 'Lyon', 'mobile', 'Safari', 'iOS', '375x667', {'duration': '800'}, now64());

INSERT INTO business_events VALUES
    ('biz_1', 'user_1', 'session_1', 'booking', 'retreat_view', now64(), 'main', 'view', 'retreat', 0, 'EUR', {'retreat_id': 'retreat_123'}, now64()),
    ('biz_2', 'user_1', 'session_1', 'booking', 'add_to_cart', now64(), 'main', 'add', 'cart', 299.99, 'EUR', {'retreat_id': 'retreat_123'}, now64()),
    ('biz_3', 'user_2', 'session_2', 'search', 'search_query', now64(), 'main', 'search', 'discovery', 0, 'EUR', {'query': 'yoga bali'}, now64());

-- Optimisation des performances
OPTIMIZE TABLE user_events;
OPTIMIZE TABLE business_events;
OPTIMIZE TABLE performance_metrics;
OPTIMIZE TABLE user_sessions;
OPTIMIZE TABLE conversions;
OPTIMIZE TABLE error_events;
OPTIMIZE TABLE ab_test_events;
