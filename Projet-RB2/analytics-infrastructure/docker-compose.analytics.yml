# Infrastructure Analytics - Sprint 16
# <PERSON><PERSON><PERSON>ouse + Kafka + Redis pour analytics temps réel

version: '3.8'

services:
  # ClickHouse pour stockage analytics haute performance
  clickhouse:
    image: clickhouse/clickhouse-server:23.8-alpine
    container_name: retreat-clickhouse
    ports:
      - "8123:8123"  # HTTP interface
      - "9000:9000"  # Native interface
    environment:
      - CLICKHOUSE_DB=analytics
      - CLICKHOUSE_USER=analytics_user
      - CLICKHOUSE_PASSWORD=${CLICKHOUSE_PASSWORD:-analytics_password}
      - CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT=1
    volumes:
      - clickhouse_data:/var/lib/clickhouse
      - ./clickhouse/config.xml:/etc/clickhouse-server/config.xml
      - ./clickhouse/users.xml:/etc/clickhouse-server/users.xml
      - ./clickhouse/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - analytics-network
    restart: unless-stopped
    healthcheck:
      test: ["<PERSON><PERSON>", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8123/ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Apache Kafka pour streaming events
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: retreat-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    volumes:
      - zookeeper_data:/var/lib/zookeeper/data
      - zookeeper_logs:/var/lib/zookeeper/log
    networks:
      - analytics-network
    restart: unless-stopped

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: retreat-kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
      - "9094:9094"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_METRIC_REPORTERS: io.confluent.metrics.reporter.ConfluentMetricsReporter
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_CONFLUENT_METRICS_REPORTER_BOOTSTRAP_SERVERS: kafka:29092
      KAFKA_CONFLUENT_METRICS_REPORTER_TOPIC_REPLICAS: 1
      KAFKA_CONFLUENT_METRICS_ENABLE: 'true'
      KAFKA_CONFLUENT_SUPPORT_CUSTOMER_ID: anonymous
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
      KAFKA_LOG_RETENTION_HOURS: 168
      KAFKA_LOG_RETENTION_BYTES: **********
    volumes:
      - kafka_data:/var/lib/kafka/data
    networks:
      - analytics-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "kafka-broker-api-versions", "--bootstrap-server", "localhost:9092"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Kafka Connect pour intégration ClickHouse
  kafka-connect:
    image: confluentinc/cp-kafka-connect:7.4.0
    container_name: retreat-kafka-connect
    depends_on:
      - kafka
      - clickhouse
    ports:
      - "8083:8083"
    environment:
      CONNECT_BOOTSTRAP_SERVERS: kafka:29092
      CONNECT_REST_ADVERTISED_HOST_NAME: kafka-connect
      CONNECT_REST_PORT: 8083
      CONNECT_GROUP_ID: compose-connect-group
      CONNECT_CONFIG_STORAGE_TOPIC: docker-connect-configs
      CONNECT_CONFIG_STORAGE_REPLICATION_FACTOR: 1
      CONNECT_OFFSET_FLUSH_INTERVAL_MS: 10000
      CONNECT_OFFSET_STORAGE_TOPIC: docker-connect-offsets
      CONNECT_OFFSET_STORAGE_REPLICATION_FACTOR: 1
      CONNECT_STATUS_STORAGE_TOPIC: docker-connect-status
      CONNECT_STATUS_STORAGE_REPLICATION_FACTOR: 1
      CONNECT_KEY_CONVERTER: org.apache.kafka.connect.storage.StringConverter
      CONNECT_VALUE_CONVERTER: org.apache.kafka.connect.json.JsonConverter
      CONNECT_VALUE_CONVERTER_SCHEMAS_ENABLE: "false"
      CONNECT_INTERNAL_KEY_CONVERTER: org.apache.kafka.connect.json.JsonConverter
      CONNECT_INTERNAL_VALUE_CONVERTER: org.apache.kafka.connect.json.JsonConverter
      CONNECT_ZOOKEEPER_CONNECT: zookeeper:2181
      CONNECT_PLUGIN_PATH: "/usr/share/java,/usr/share/confluent-hub-components"
      CONNECT_LOG4J_LOGGERS: org.apache.zookeeper=ERROR,org.I0Itec.zkclient=ERROR,org.reflections=ERROR
    volumes:
      - ./kafka-connect/plugins:/usr/share/confluent-hub-components
    networks:
      - analytics-network
    restart: unless-stopped

  # Redis pour cache analytics
  redis-analytics:
    image: redis:7-alpine
    container_name: retreat-redis-analytics
    ports:
      - "6380:6379"
    command: redis-server --appendonly yes --requirepass ${REDIS_ANALYTICS_PASSWORD:-analytics_redis}
    volumes:
      - redis_analytics_data:/data
      - ./redis/redis-analytics.conf:/usr/local/etc/redis/redis.conf
    networks:
      - analytics-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Service de traitement analytics
  analytics-processor:
    build:
      context: ./analytics-processor
      dockerfile: Dockerfile
    container_name: retreat-analytics-processor
    depends_on:
      - kafka
      - clickhouse
      - redis-analytics
    environment:
      - NODE_ENV=production
      - KAFKA_BROKERS=kafka:29092
      - CLICKHOUSE_URL=http://clickhouse:8123
      - CLICKHOUSE_DATABASE=analytics
      - CLICKHOUSE_USER=analytics_user
      - CLICKHOUSE_PASSWORD=${CLICKHOUSE_PASSWORD}
      - REDIS_URL=redis://redis-analytics:6379
      - REDIS_PASSWORD=${REDIS_ANALYTICS_PASSWORD}
      - LOG_LEVEL=info
    volumes:
      - ./logs/analytics-processor:/app/logs
    networks:
      - analytics-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # API Analytics pour dashboards
  analytics-api:
    build:
      context: ./analytics-api
      dockerfile: Dockerfile
    container_name: retreat-analytics-api
    depends_on:
      - clickhouse
      - redis-analytics
    ports:
      - "3005:3000"
    environment:
      - NODE_ENV=production
      - CLICKHOUSE_URL=http://clickhouse:8123
      - CLICKHOUSE_DATABASE=analytics
      - CLICKHOUSE_USER=analytics_user
      - CLICKHOUSE_PASSWORD=${CLICKHOUSE_PASSWORD}
      - REDIS_URL=redis://redis-analytics:6379
      - REDIS_PASSWORD=${REDIS_ANALYTICS_PASSWORD}
      - JWT_SECRET=${JWT_SECRET}
      - CORS_ORIGINS=http://localhost:3000,http://localhost:3004
    volumes:
      - ./logs/analytics-api:/app/logs
    networks:
      - analytics-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Grafana pour visualisation analytics
  grafana-analytics:
    image: grafana/grafana:10.1.0
    container_name: retreat-grafana-analytics
    depends_on:
      - clickhouse
    ports:
      - "3006:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ANALYTICS_PASSWORD:-analytics_admin}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clickhouse-datasource
      - GF_FEATURE_TOGGLES_ENABLE=publicDashboards
    volumes:
      - grafana_analytics_data:/var/lib/grafana
      - ./grafana/analytics/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/analytics/datasources:/etc/grafana/provisioning/datasources
      - ./grafana/analytics/plugins:/var/lib/grafana/plugins
    networks:
      - analytics-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx pour reverse proxy analytics
  nginx-analytics:
    image: nginx:alpine
    container_name: retreat-nginx-analytics
    depends_on:
      - analytics-api
      - grafana-analytics
    ports:
      - "8080:80"
    volumes:
      - ./nginx/analytics.conf:/etc/nginx/conf.d/default.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./logs/nginx-analytics:/var/log/nginx
    networks:
      - analytics-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  clickhouse_data:
    driver: local
  kafka_data:
    driver: local
  zookeeper_data:
    driver: local
  zookeeper_logs:
    driver: local
  redis_analytics_data:
    driver: local
  grafana_analytics_data:
    driver: local

networks:
  analytics-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
