/**
 * Advanced Notification System - Sprint 17 Jour 2
 * Système de notifications push avancé avec segmentation et personnalisation
 */

interface NotificationConfig {
  vapidPublicKey: string;
  vapidPrivateKey: string;
  apiEndpoint: string;
  enableSegmentation: boolean;
  enablePersonalization: boolean;
  enableAnalytics: boolean;
  defaultIcon: string;
  defaultBadge: string;
}

interface NotificationSegment {
  id: string;
  name: string;
  description: string;
  criteria: SegmentCriteria[];
  priority: 'low' | 'medium' | 'high' | 'critical';
  enabled: boolean;
}

interface SegmentCriteria {
  field: string;
  operator: 'equals' | 'contains' | 'greater_than' | 'less_than' | 'in' | 'not_in';
  value: any;
  weight: number; // 0-1
}

interface NotificationTemplate {
  id: string;
  name: string;
  type: 'promotional' | 'transactional' | 'reminder' | 'alert' | 'social';
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  image?: string;
  actions?: NotificationAction[];
  data?: Record<string, any>;
  personalization: PersonalizationRule[];
  scheduling: SchedulingRule;
}

interface NotificationAction {
  action: string;
  title: string;
  icon?: string;
  type?: 'button' | 'text';
  placeholder?: string;
}

interface PersonalizationRule {
  field: string;
  placeholder: string;
  fallback: string;
  transform?: 'uppercase' | 'lowercase' | 'capitalize';
}

interface SchedulingRule {
  immediate: boolean;
  delay?: number; // ms
  timezone?: string;
  optimalTime?: boolean;
  frequency?: FrequencyRule;
}

interface FrequencyRule {
  type: 'once' | 'daily' | 'weekly' | 'monthly' | 'custom';
  interval?: number;
  maxPerDay?: number;
  maxPerWeek?: number;
}

interface NotificationCampaign {
  id: string;
  name: string;
  description: string;
  template: NotificationTemplate;
  segments: string[];
  startDate: Date;
  endDate?: Date;
  status: 'draft' | 'scheduled' | 'running' | 'paused' | 'completed';
  analytics: CampaignAnalytics;
}

interface CampaignAnalytics {
  sent: number;
  delivered: number;
  opened: number;
  clicked: number;
  dismissed: number;
  converted: number;
  revenue: number;
}

interface UserSubscription {
  userId: string;
  endpoint: string;
  keys: {
    p256dh: string;
    auth: string;
  };
  userAgent: string;
  createdAt: Date;
  lastUsed: Date;
  segments: string[];
  preferences: NotificationPreferences;
}

interface NotificationPreferences {
  enabled: boolean;
  types: Record<string, boolean>;
  frequency: 'all' | 'important' | 'minimal';
  quietHours: {
    enabled: boolean;
    start: string; // HH:mm
    end: string; // HH:mm
    timezone: string;
  };
  channels: {
    push: boolean;
    email: boolean;
    sms: boolean;
  };
}

export class AdvancedNotificationSystem {
  private config: NotificationConfig;
  private subscriptions: Map<string, UserSubscription> = new Map();
  private segments: Map<string, NotificationSegment> = new Map();
  private templates: Map<string, NotificationTemplate> = new Map();
  private campaigns: Map<string, NotificationCampaign> = new Map();
  private analytics: Map<string, any> = new Map();

  constructor(config: NotificationConfig) {
    this.config = config;
    this.initialize();
  }

  /**
   * Initialisation du système de notifications
   */
  private async initialize() {
    console.log('🔔 Initializing Advanced Notification System...');

    // Charger les segments existants
    await this.loadSegments();

    // Charger les templates
    await this.loadTemplates();

    // Configurer les segments par défaut
    this.setupDefaultSegments();

    // Configurer les templates par défaut
    this.setupDefaultTemplates();

    // Démarrer le processeur de campagnes
    this.startCampaignProcessor();

    // Configurer l'analytics
    if (this.config.enableAnalytics) {
      this.setupAnalytics();
    }

    console.log('✅ Advanced Notification System initialized successfully');
  }

  /**
   * Abonner un utilisateur aux notifications
   */
  public async subscribeUser(
    userId: string, 
    subscription: PushSubscription,
    preferences?: Partial<NotificationPreferences>
  ): Promise<boolean> {
    try {
      const userSubscription: UserSubscription = {
        userId,
        endpoint: subscription.endpoint,
        keys: {
          p256dh: subscription.getKey('p256dh') ? 
            btoa(String.fromCharCode(...new Uint8Array(subscription.getKey('p256dh')!))) : '',
          auth: subscription.getKey('auth') ? 
            btoa(String.fromCharCode(...new Uint8Array(subscription.getKey('auth')!))) : ''
        },
        userAgent: navigator.userAgent,
        createdAt: new Date(),
        lastUsed: new Date(),
        segments: [],
        preferences: {
          enabled: true,
          types: {
            promotional: true,
            transactional: true,
            reminder: true,
            alert: true,
            social: true
          },
          frequency: 'all',
          quietHours: {
            enabled: false,
            start: '22:00',
            end: '08:00',
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
          },
          channels: {
            push: true,
            email: true,
            sms: false
          },
          ...preferences
        }
      };

      // Sauvegarder l'abonnement
      this.subscriptions.set(userId, userSubscription);

      // Assigner aux segments
      await this.assignUserToSegments(userId);

      // Envoyer notification de bienvenue
      await this.sendWelcomeNotification(userId);

      console.log(`✅ User ${userId} subscribed to notifications`);
      return true;

    } catch (error) {
      console.error('Failed to subscribe user:', error);
      return false;
    }
  }

  /**
   * Créer un segment d'utilisateurs
   */
  public createSegment(segment: NotificationSegment): void {
    this.segments.set(segment.id, segment);
    console.log(`📊 Segment created: ${segment.name}`);
  }

  /**
   * Créer un template de notification
   */
  public createTemplate(template: NotificationTemplate): void {
    this.templates.set(template.id, template);
    console.log(`📝 Template created: ${template.name}`);
  }

  /**
   * Créer et lancer une campagne
   */
  public async createCampaign(campaign: NotificationCampaign): Promise<string> {
    campaign.analytics = {
      sent: 0,
      delivered: 0,
      opened: 0,
      clicked: 0,
      dismissed: 0,
      converted: 0,
      revenue: 0
    };

    this.campaigns.set(campaign.id, campaign);

    if (campaign.status === 'running') {
      await this.executeCampaign(campaign.id);
    }

    console.log(`🚀 Campaign created: ${campaign.name}`);
    return campaign.id;
  }

  /**
   * Envoyer une notification immédiate
   */
  public async sendNotification(
    userId: string,
    templateId: string,
    data?: Record<string, any>
  ): Promise<boolean> {
    const subscription = this.subscriptions.get(userId);
    const template = this.templates.get(templateId);

    if (!subscription || !template) {
      console.error('Subscription or template not found');
      return false;
    }

    // Vérifier les préférences utilisateur
    if (!this.canSendNotification(subscription, template)) {
      console.log('Notification blocked by user preferences');
      return false;
    }

    try {
      // Personnaliser le contenu
      const personalizedContent = await this.personalizeContent(template, userId, data);

      // Construire la notification
      const notification = {
        title: personalizedContent.title,
        body: personalizedContent.body,
        icon: template.icon || this.config.defaultIcon,
        badge: template.badge || this.config.defaultBadge,
        image: template.image,
        data: {
          ...template.data,
          ...data,
          templateId,
          userId,
          timestamp: Date.now()
        },
        actions: template.actions,
        tag: `${templateId}_${userId}`,
        requireInteraction: template.type === 'alert',
        silent: false,
        vibrate: [200, 100, 200]
      };

      // Envoyer la notification
      const success = await this.sendPushNotification(subscription, notification);

      if (success) {
        // Tracker l'envoi
        this.trackNotificationEvent('sent', templateId, userId);
        subscription.lastUsed = new Date();
      }

      return success;

    } catch (error) {
      console.error('Failed to send notification:', error);
      return false;
    }
  }

  /**
   * Exécuter une campagne
   */
  private async executeCampaign(campaignId: string): Promise<void> {
    const campaign = this.campaigns.get(campaignId);
    if (!campaign) return;

    console.log(`🚀 Executing campaign: ${campaign.name}`);

    // Obtenir les utilisateurs ciblés
    const targetUsers = await this.getTargetUsers(campaign.segments);

    // Envoyer les notifications
    for (const userId of targetUsers) {
      try {
        const success = await this.sendNotification(
          userId,
          campaign.template.id,
          campaign.template.data
        );

        if (success) {
          campaign.analytics.sent++;
        }

        // Respecter les limites de fréquence
        await this.delay(100); // 100ms entre chaque envoi

      } catch (error) {
        console.error(`Failed to send to user ${userId}:`, error);
      }
    }

    console.log(`✅ Campaign executed: ${campaign.analytics.sent} notifications sent`);
  }

  /**
   * Obtenir les utilisateurs ciblés par segments
   */
  private async getTargetUsers(segmentIds: string[]): Promise<string[]> {
    const targetUsers = new Set<string>();

    for (const segmentId of segmentIds) {
      const segment = this.segments.get(segmentId);
      if (!segment || !segment.enabled) continue;

      for (const [userId, subscription] of this.subscriptions) {
        if (subscription.segments.includes(segmentId)) {
          targetUsers.add(userId);
        }
      }
    }

    return Array.from(targetUsers);
  }

  /**
   * Personnaliser le contenu
   */
  private async personalizeContent(
    template: NotificationTemplate,
    userId: string,
    data?: Record<string, any>
  ): Promise<{ title: string; body: string }> {
    let title = template.title;
    let body = template.body;

    if (this.config.enablePersonalization) {
      // Obtenir les données utilisateur
      const userData = await this.getUserData(userId);
      const contextData = { ...userData, ...data };

      // Appliquer les règles de personnalisation
      for (const rule of template.personalization) {
        const value = contextData[rule.field] || rule.fallback;
        const transformedValue = this.transformValue(value, rule.transform);

        title = title.replace(rule.placeholder, transformedValue);
        body = body.replace(rule.placeholder, transformedValue);
      }
    }

    return { title, body };
  }

  /**
   * Vérifier si on peut envoyer une notification
   */
  private canSendNotification(
    subscription: UserSubscription,
    template: NotificationTemplate
  ): boolean {
    const prefs = subscription.preferences;

    // Vérifier si les notifications sont activées
    if (!prefs.enabled || !prefs.channels.push) {
      return false;
    }

    // Vérifier le type de notification
    if (!prefs.types[template.type]) {
      return false;
    }

    // Vérifier les heures silencieuses
    if (prefs.quietHours.enabled) {
      const now = new Date();
      const currentTime = now.toLocaleTimeString('en-GB', { 
        hour12: false, 
        timeZone: prefs.quietHours.timezone 
      }).slice(0, 5);

      if (currentTime >= prefs.quietHours.start && currentTime <= prefs.quietHours.end) {
        return template.type === 'alert'; // Seules les alertes passent en heures silencieuses
      }
    }

    // Vérifier la fréquence
    return this.checkFrequencyLimits(subscription, template);
  }

  /**
   * Vérifier les limites de fréquence
   */
  private checkFrequencyLimits(
    subscription: UserSubscription,
    template: NotificationTemplate
  ): boolean {
    // Implémentation simplifiée - en production, vérifier la base de données
    return true;
  }

  /**
   * Envoyer la notification push
   */
  private async sendPushNotification(
    subscription: UserSubscription,
    notification: any
  ): Promise<boolean> {
    try {
      const response = await fetch(this.config.apiEndpoint + '/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.vapidPrivateKey}`
        },
        body: JSON.stringify({
          subscription: {
            endpoint: subscription.endpoint,
            keys: subscription.keys
          },
          payload: JSON.stringify(notification),
          options: {
            TTL: 24 * 60 * 60, // 24 heures
            urgency: 'normal',
            topic: notification.tag
          }
        })
      });

      return response.ok;

    } catch (error) {
      console.error('Push notification failed:', error);
      return false;
    }
  }

  /**
   * Assigner un utilisateur aux segments
   */
  private async assignUserToSegments(userId: string): Promise<void> {
    const subscription = this.subscriptions.get(userId);
    if (!subscription) return;

    const userData = await this.getUserData(userId);
    const assignedSegments: string[] = [];

    for (const [segmentId, segment] of this.segments) {
      if (!segment.enabled) continue;

      const matches = this.evaluateSegmentCriteria(segment.criteria, userData);
      if (matches) {
        assignedSegments.push(segmentId);
      }
    }

    subscription.segments = assignedSegments;
    console.log(`📊 User ${userId} assigned to segments:`, assignedSegments);
  }

  /**
   * Évaluer les critères de segment
   */
  private evaluateSegmentCriteria(criteria: SegmentCriteria[], userData: any): boolean {
    let totalWeight = 0;
    let matchedWeight = 0;

    for (const criterion of criteria) {
      totalWeight += criterion.weight;

      const userValue = userData[criterion.field];
      const matches = this.evaluateCriterion(criterion, userValue);

      if (matches) {
        matchedWeight += criterion.weight;
      }
    }

    // Retourner true si au moins 70% des critères pondérés sont satisfaits
    return (matchedWeight / totalWeight) >= 0.7;
  }

  /**
   * Évaluer un critère individuel
   */
  private evaluateCriterion(criterion: SegmentCriteria, userValue: any): boolean {
    switch (criterion.operator) {
      case 'equals':
        return userValue === criterion.value;
      case 'contains':
        return String(userValue).includes(String(criterion.value));
      case 'greater_than':
        return Number(userValue) > Number(criterion.value);
      case 'less_than':
        return Number(userValue) < Number(criterion.value);
      case 'in':
        return Array.isArray(criterion.value) && criterion.value.includes(userValue);
      case 'not_in':
        return Array.isArray(criterion.value) && !criterion.value.includes(userValue);
      default:
        return false;
    }
  }

  /**
   * Configuration des segments par défaut
   */
  private setupDefaultSegments(): void {
    const defaultSegments: NotificationSegment[] = [
      {
        id: 'new_users',
        name: 'Nouveaux Utilisateurs',
        description: 'Utilisateurs inscrits depuis moins de 7 jours',
        criteria: [
          { field: 'daysSinceRegistration', operator: 'less_than', value: 7, weight: 1 }
        ],
        priority: 'high',
        enabled: true
      },
      {
        id: 'active_users',
        name: 'Utilisateurs Actifs',
        description: 'Utilisateurs connectés dans les 30 derniers jours',
        criteria: [
          { field: 'daysSinceLastLogin', operator: 'less_than', value: 30, weight: 1 }
        ],
        priority: 'medium',
        enabled: true
      },
      {
        id: 'premium_users',
        name: 'Utilisateurs Premium',
        description: 'Utilisateurs avec abonnement premium',
        criteria: [
          { field: 'subscriptionType', operator: 'equals', value: 'premium', weight: 1 }
        ],
        priority: 'high',
        enabled: true
      },
      {
        id: 'yoga_enthusiasts',
        name: 'Passionnés de Yoga',
        description: 'Utilisateurs intéressés par le yoga',
        criteria: [
          { field: 'interests', operator: 'contains', value: 'yoga', weight: 0.8 },
          { field: 'bookingHistory', operator: 'contains', value: 'yoga', weight: 0.6 }
        ],
        priority: 'medium',
        enabled: true
      }
    ];

    defaultSegments.forEach(segment => {
      this.segments.set(segment.id, segment);
    });
  }

  /**
   * Configuration des templates par défaut
   */
  private setupDefaultTemplates(): void {
    const defaultTemplates: NotificationTemplate[] = [
      {
        id: 'welcome',
        name: 'Bienvenue',
        type: 'transactional',
        title: 'Bienvenue {{firstName}} ! 🙏',
        body: 'Découvrez nos retraites exceptionnelles et trouvez votre équilibre.',
        actions: [
          { action: 'explore', title: 'Explorer', icon: '/icons/explore.png' },
          { action: 'dismiss', title: 'Plus tard', icon: '/icons/dismiss.png' }
        ],
        personalization: [
          { field: 'firstName', placeholder: '{{firstName}}', fallback: 'cher utilisateur' }
        ],
        scheduling: { immediate: true }
      },
      {
        id: 'booking_reminder',
        name: 'Rappel Réservation',
        type: 'reminder',
        title: 'Votre retraite commence demain ! 🧘‍♀️',
        body: 'N\'oubliez pas votre retraite {{retreatName}} qui commence demain à {{startTime}}.',
        personalization: [
          { field: 'retreatName', placeholder: '{{retreatName}}', fallback: 'yoga' },
          { field: 'startTime', placeholder: '{{startTime}}', fallback: '9h00' }
        ],
        scheduling: { immediate: false, delay: 24 * 60 * 60 * 1000 } // 24h avant
      },
      {
        id: 'special_offer',
        name: 'Offre Spéciale',
        type: 'promotional',
        title: '🎉 Offre exclusive pour vous !',
        body: 'Profitez de {{discount}}% de réduction sur nos retraites premium.',
        actions: [
          { action: 'view_offer', title: 'Voir l\'offre', icon: '/icons/offer.png' },
          { action: 'dismiss', title: 'Non merci', icon: '/icons/dismiss.png' }
        ],
        personalization: [
          { field: 'discount', placeholder: '{{discount}}', fallback: '20' }
        ],
        scheduling: { immediate: true }
      }
    ];

    defaultTemplates.forEach(template => {
      this.templates.set(template.id, template);
    });
  }

  /**
   * Envoyer notification de bienvenue
   */
  private async sendWelcomeNotification(userId: string): Promise<void> {
    await this.sendNotification(userId, 'welcome');
  }

  /**
   * Démarrer le processeur de campagnes
   */
  private startCampaignProcessor(): void {
    setInterval(() => {
      this.processCampaigns();
    }, 60000); // Vérifier toutes les minutes
  }

  /**
   * Traiter les campagnes programmées
   */
  private async processCampaigns(): Promise<void> {
    const now = new Date();

    for (const [campaignId, campaign] of this.campaigns) {
      if (campaign.status === 'scheduled' && campaign.startDate <= now) {
        campaign.status = 'running';
        await this.executeCampaign(campaignId);
      }

      if (campaign.status === 'running' && campaign.endDate && campaign.endDate <= now) {
        campaign.status = 'completed';
      }
    }
  }

  /**
   * Configurer l'analytics
   */
  private setupAnalytics(): void {
    // Écouter les événements de notification
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('message', (event) => {
        const { type, data } = event.data;

        switch (type) {
          case 'NOTIFICATION_CLICKED':
            this.trackNotificationEvent('clicked', data.templateId, data.userId);
            break;
          case 'NOTIFICATION_DISMISSED':
            this.trackNotificationEvent('dismissed', data.templateId, data.userId);
            break;
        }
      });
    }
  }

  /**
   * Tracker un événement de notification
   */
  private trackNotificationEvent(
    event: 'sent' | 'delivered' | 'opened' | 'clicked' | 'dismissed' | 'converted',
    templateId: string,
    userId: string
  ): void {
    const key = `${templateId}_${userId}`;
    
    if (!this.analytics.has(key)) {
      this.analytics.set(key, {
        templateId,
        userId,
        sent: 0,
        delivered: 0,
        opened: 0,
        clicked: 0,
        dismissed: 0,
        converted: 0
      });
    }

    const analytics = this.analytics.get(key);
    analytics[event]++;

    console.log(`📊 Notification event tracked: ${event} for ${templateId}`);
  }

  /**
   * Utilitaires
   */
  private async getUserData(userId: string): Promise<any> {
    // En production, récupérer depuis la base de données
    return {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      subscriptionType: 'premium',
      interests: ['yoga', 'meditation'],
      daysSinceRegistration: 5,
      daysSinceLastLogin: 1,
      bookingHistory: ['yoga-retreat-1', 'meditation-workshop-2']
    };
  }

  private transformValue(value: string, transform?: string): string {
    if (!transform) return value;

    switch (transform) {
      case 'uppercase':
        return value.toUpperCase();
      case 'lowercase':
        return value.toLowerCase();
      case 'capitalize':
        return value.charAt(0).toUpperCase() + value.slice(1).toLowerCase();
      default:
        return value;
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private async loadSegments(): Promise<void> {
    // Charger depuis la base de données
  }

  private async loadTemplates(): Promise<void> {
    // Charger depuis la base de données
  }

  /**
   * API publique pour analytics
   */
  public getCampaignAnalytics(campaignId: string): CampaignAnalytics | null {
    const campaign = this.campaigns.get(campaignId);
    return campaign ? campaign.analytics : null;
  }

  public getSegmentStats(segmentId: string): any {
    const segment = this.segments.get(segmentId);
    if (!segment) return null;

    const users = Array.from(this.subscriptions.values())
      .filter(sub => sub.segments.includes(segmentId));

    return {
      segmentId,
      name: segment.name,
      userCount: users.length,
      activeUsers: users.filter(sub => 
        (Date.now() - sub.lastUsed.getTime()) < 30 * 24 * 60 * 60 * 1000
      ).length
    };
  }

  /**
   * Nettoyage
   */
  public destroy(): void {
    this.subscriptions.clear();
    this.segments.clear();
    this.templates.clear();
    this.campaigns.clear();
    this.analytics.clear();
  }
}

export default AdvancedNotificationSystem;
