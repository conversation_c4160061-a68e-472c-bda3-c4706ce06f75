/**
 * Advanced Gesture System - Sprint 17 Jour 2
 * Système de gestures avancés pour interactions mobiles fluides
 */

interface GestureConfig {
  enableSwipe: boolean;
  enablePinch: boolean;
  enableRotate: boolean;
  enableLongPress: boolean;
  enableDoubleTap: boolean;
  enablePan: boolean;
  swipeThreshold: number;
  pinchThreshold: number;
  longPressDelay: number;
  doubleTapDelay: number;
  enableHapticFeedback: boolean;
  debugMode: boolean;
}

interface GestureEvent {
  type: GestureType;
  target: HTMLElement;
  startPoint: TouchPoint;
  currentPoint: TouchPoint;
  deltaX: number;
  deltaY: number;
  distance: number;
  angle: number;
  scale: number;
  rotation: number;
  velocity: number;
  direction: SwipeDirection;
  timestamp: number;
  preventDefault: () => void;
  stopPropagation: () => void;
}

interface TouchPoint {
  x: number;
  y: number;
  timestamp: number;
}

type GestureType = 
  | 'swipe' 
  | 'pinch' 
  | 'rotate' 
  | 'longpress' 
  | 'doubletap' 
  | 'pan' 
  | 'tap'
  | 'pinchstart'
  | 'pinchend'
  | 'rotatestart'
  | 'rotateend'
  | 'panstart'
  | 'panend';

type SwipeDirection = 'up' | 'down' | 'left' | 'right';

interface GestureHandler {
  element: HTMLElement;
  gestures: Set<GestureType>;
  handlers: Map<GestureType, (event: GestureEvent) => void>;
  options: Partial<GestureConfig>;
}

export class AdvancedGestureSystem {
  private config: GestureConfig;
  private handlers: Map<HTMLElement, GestureHandler> = new Map();
  private activeGestures: Map<HTMLElement, any> = new Map();
  private touchHistory: TouchPoint[] = [];

  constructor(config: Partial<GestureConfig> = {}) {
    this.config = {
      enableSwipe: true,
      enablePinch: true,
      enableRotate: true,
      enableLongPress: true,
      enableDoubleTap: true,
      enablePan: true,
      swipeThreshold: 50, // pixels
      pinchThreshold: 0.1, // scale difference
      longPressDelay: 500, // ms
      doubleTapDelay: 300, // ms
      enableHapticFeedback: true,
      debugMode: false,
      ...config
    };

    this.initialize();
  }

  /**
   * Initialisation du système de gestures
   */
  private initialize(): void {
    this.debug('Initializing Advanced Gesture System...');

    // Désactiver le zoom par défaut sur mobile
    this.disableDefaultZoom();

    // Configurer les événements globaux
    this.setupGlobalEvents();

    this.debug('Advanced Gesture System initialized successfully');
  }

  /**
   * Enregistrer des gestures sur un élément
   */
  public registerGestures(
    element: HTMLElement,
    gestures: GestureType[],
    handlers: Partial<Record<GestureType, (event: GestureEvent) => void>>,
    options: Partial<GestureConfig> = {}
  ): void {
    const gestureHandler: GestureHandler = {
      element,
      gestures: new Set(gestures),
      handlers: new Map(Object.entries(handlers) as [GestureType, (event: GestureEvent) => void][]),
      options: { ...this.config, ...options }
    };

    this.handlers.set(element, gestureHandler);

    // Configurer les événements tactiles
    this.setupTouchEvents(element);

    this.debug(`Gestures registered for element:`, gestures);
  }

  /**
   * Supprimer les gestures d'un élément
   */
  public unregisterGestures(element: HTMLElement): void {
    if (this.handlers.has(element)) {
      this.removeTouchEvents(element);
      this.handlers.delete(element);
      this.activeGestures.delete(element);
      this.debug('Gestures unregistered for element');
    }
  }

  /**
   * Configurer les événements tactiles
   */
  private setupTouchEvents(element: HTMLElement): void {
    // Événements tactiles
    element.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: false });
    element.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false });
    element.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: false });
    element.addEventListener('touchcancel', this.handleTouchCancel.bind(this), { passive: false });

    // Événements souris pour le développement desktop
    element.addEventListener('mousedown', this.handleMouseDown.bind(this), { passive: false });
    element.addEventListener('mousemove', this.handleMouseMove.bind(this), { passive: false });
    element.addEventListener('mouseup', this.handleMouseUp.bind(this), { passive: false });

    // Désactiver la sélection de texte
    element.style.userSelect = 'none';
    element.style.webkitUserSelect = 'none';
    element.style.touchAction = 'none';
  }

  /**
   * Supprimer les événements tactiles
   */
  private removeTouchEvents(element: HTMLElement): void {
    element.removeEventListener('touchstart', this.handleTouchStart.bind(this));
    element.removeEventListener('touchmove', this.handleTouchMove.bind(this));
    element.removeEventListener('touchend', this.handleTouchEnd.bind(this));
    element.removeEventListener('touchcancel', this.handleTouchCancel.bind(this));
    element.removeEventListener('mousedown', this.handleMouseDown.bind(this));
    element.removeEventListener('mousemove', this.handleMouseMove.bind(this));
    element.removeEventListener('mouseup', this.handleMouseUp.bind(this));
  }

  /**
   * Gestionnaire touchstart
   */
  private handleTouchStart(event: TouchEvent): void {
    const element = event.currentTarget as HTMLElement;
    const handler = this.handlers.get(element);
    if (!handler) return;

    const touches = Array.from(event.touches);
    const touch = touches[0];

    const gestureState = {
      startTime: Date.now(),
      startPoint: { x: touch.clientX, y: touch.clientY, timestamp: Date.now() },
      currentPoint: { x: touch.clientX, y: touch.clientY, timestamp: Date.now() },
      touches: touches.length,
      initialDistance: touches.length > 1 ? this.getDistance(touches[0], touches[1]) : 0,
      initialAngle: touches.length > 1 ? this.getAngle(touches[0], touches[1]) : 0,
      longPressTimer: null,
      lastTapTime: 0,
      tapCount: 0,
      isPanning: false,
      isPinching: false,
      isRotating: false
    };

    this.activeGestures.set(element, gestureState);

    // Démarrer le timer pour long press
    if (handler.gestures.has('longpress')) {
      gestureState.longPressTimer = setTimeout(() => {
        this.triggerLongPress(element, gestureState);
      }, handler.options.longPressDelay || this.config.longPressDelay);
    }

    // Détecter pinch/rotate start
    if (touches.length > 1) {
      if (handler.gestures.has('pinch')) {
        this.triggerGestureEvent(element, 'pinchstart', gestureState);
        gestureState.isPinching = true;
      }
      if (handler.gestures.has('rotate')) {
        this.triggerGestureEvent(element, 'rotatestart', gestureState);
        gestureState.isRotating = true;
      }
    }

    this.addToTouchHistory({ x: touch.clientX, y: touch.clientY, timestamp: Date.now() });
  }

  /**
   * Gestionnaire touchmove
   */
  private handleTouchMove(event: TouchEvent): void {
    const element = event.currentTarget as HTMLElement;
    const handler = this.handlers.get(element);
    const gestureState = this.activeGestures.get(element);
    if (!handler || !gestureState) return;

    const touches = Array.from(event.touches);
    const touch = touches[0];

    // Mettre à jour la position actuelle
    gestureState.currentPoint = { x: touch.clientX, y: touch.clientY, timestamp: Date.now() };

    // Annuler le long press si mouvement
    if (gestureState.longPressTimer) {
      clearTimeout(gestureState.longPressTimer);
      gestureState.longPressTimer = null;
    }

    // Détecter pan
    if (touches.length === 1 && handler.gestures.has('pan')) {
      if (!gestureState.isPanning) {
        this.triggerGestureEvent(element, 'panstart', gestureState);
        gestureState.isPanning = true;
      }
      this.triggerGestureEvent(element, 'pan', gestureState);
    }

    // Détecter pinch
    if (touches.length > 1 && handler.gestures.has('pinch') && gestureState.isPinching) {
      this.triggerGestureEvent(element, 'pinch', gestureState, touches);
    }

    // Détecter rotation
    if (touches.length > 1 && handler.gestures.has('rotate') && gestureState.isRotating) {
      this.triggerGestureEvent(element, 'rotate', gestureState, touches);
    }

    this.addToTouchHistory({ x: touch.clientX, y: touch.clientY, timestamp: Date.now() });
  }

  /**
   * Gestionnaire touchend
   */
  private handleTouchEnd(event: TouchEvent): void {
    const element = event.currentTarget as HTMLElement;
    const handler = this.handlers.get(element);
    const gestureState = this.activeGestures.get(element);
    if (!handler || !gestureState) return;

    // Annuler le timer long press
    if (gestureState.longPressTimer) {
      clearTimeout(gestureState.longPressTimer);
      gestureState.longPressTimer = null;
    }

    const deltaX = gestureState.currentPoint.x - gestureState.startPoint.x;
    const deltaY = gestureState.currentPoint.y - gestureState.startPoint.y;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    const duration = Date.now() - gestureState.startTime;

    // Détecter swipe
    if (handler.gestures.has('swipe') && distance > (handler.options.swipeThreshold || this.config.swipeThreshold)) {
      this.triggerSwipe(element, gestureState, deltaX, deltaY, duration);
    }

    // Détecter tap/double tap
    if (distance < 10 && duration < 300) {
      const now = Date.now();
      if (handler.gestures.has('doubletap') && 
          now - gestureState.lastTapTime < (handler.options.doubleTapDelay || this.config.doubleTapDelay)) {
        this.triggerGestureEvent(element, 'doubletap', gestureState);
        gestureState.tapCount = 0;
      } else {
        if (handler.gestures.has('tap')) {
          this.triggerGestureEvent(element, 'tap', gestureState);
        }
        gestureState.lastTapTime = now;
        gestureState.tapCount = 1;
      }
    }

    // Terminer pan
    if (gestureState.isPanning && handler.gestures.has('pan')) {
      this.triggerGestureEvent(element, 'panend', gestureState);
      gestureState.isPanning = false;
    }

    // Terminer pinch
    if (gestureState.isPinching && handler.gestures.has('pinch')) {
      this.triggerGestureEvent(element, 'pinchend', gestureState);
      gestureState.isPinching = false;
    }

    // Terminer rotation
    if (gestureState.isRotating && handler.gestures.has('rotate')) {
      this.triggerGestureEvent(element, 'rotateend', gestureState);
      gestureState.isRotating = false;
    }

    // Nettoyer si plus de touches
    if (event.touches.length === 0) {
      this.activeGestures.delete(element);
    }
  }

  /**
   * Gestionnaire touchcancel
   */
  private handleTouchCancel(event: TouchEvent): void {
    const element = event.currentTarget as HTMLElement;
    const gestureState = this.activeGestures.get(element);
    
    if (gestureState) {
      if (gestureState.longPressTimer) {
        clearTimeout(gestureState.longPressTimer);
      }
      this.activeGestures.delete(element);
    }
  }

  /**
   * Déclencher un événement de gesture
   */
  private triggerGestureEvent(
    element: HTMLElement,
    type: GestureType,
    gestureState: any,
    touches?: Touch[]
  ): void {
    const handler = this.handlers.get(element);
    if (!handler || !handler.handlers.has(type)) return;

    const deltaX = gestureState.currentPoint.x - gestureState.startPoint.x;
    const deltaY = gestureState.currentPoint.y - gestureState.startPoint.y;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    const angle = Math.atan2(deltaY, deltaX) * 180 / Math.PI;
    const velocity = this.calculateVelocity();

    let scale = 1;
    let rotation = 0;

    if (touches && touches.length > 1) {
      const currentDistance = this.getDistance(touches[0], touches[1]);
      const currentAngle = this.getAngle(touches[0], touches[1]);
      
      scale = currentDistance / gestureState.initialDistance;
      rotation = currentAngle - gestureState.initialAngle;
    }

    const gestureEvent: GestureEvent = {
      type,
      target: element,
      startPoint: gestureState.startPoint,
      currentPoint: gestureState.currentPoint,
      deltaX,
      deltaY,
      distance,
      angle,
      scale,
      rotation,
      velocity,
      direction: this.getSwipeDirection(deltaX, deltaY),
      timestamp: Date.now(),
      preventDefault: () => {},
      stopPropagation: () => {}
    };

    // Feedback haptique
    if (this.config.enableHapticFeedback && 'vibrate' in navigator) {
      this.triggerHapticFeedback(type);
    }

    // Appeler le handler
    const eventHandler = handler.handlers.get(type);
    if (eventHandler) {
      eventHandler(gestureEvent);
    }

    this.debug(`Gesture triggered: ${type}`, gestureEvent);
  }

  /**
   * Déclencher un swipe
   */
  private triggerSwipe(element: HTMLElement, gestureState: any, deltaX: number, deltaY: number, duration: number): void {
    const direction = this.getSwipeDirection(deltaX, deltaY);
    const velocity = Math.sqrt(deltaX * deltaX + deltaY * deltaY) / duration;

    this.triggerGestureEvent(element, 'swipe', {
      ...gestureState,
      direction,
      velocity
    });
  }

  /**
   * Déclencher un long press
   */
  private triggerLongPress(element: HTMLElement, gestureState: any): void {
    this.triggerGestureEvent(element, 'longpress', gestureState);
  }

  /**
   * Obtenir la direction du swipe
   */
  private getSwipeDirection(deltaX: number, deltaY: number): SwipeDirection {
    if (Math.abs(deltaX) > Math.abs(deltaY)) {
      return deltaX > 0 ? 'right' : 'left';
    } else {
      return deltaY > 0 ? 'down' : 'up';
    }
  }

  /**
   * Calculer la distance entre deux touches
   */
  private getDistance(touch1: Touch, touch2: Touch): number {
    const deltaX = touch2.clientX - touch1.clientX;
    const deltaY = touch2.clientY - touch1.clientY;
    return Math.sqrt(deltaX * deltaX + deltaY * deltaY);
  }

  /**
   * Calculer l'angle entre deux touches
   */
  private getAngle(touch1: Touch, touch2: Touch): number {
    const deltaX = touch2.clientX - touch1.clientX;
    const deltaY = touch2.clientY - touch1.clientY;
    return Math.atan2(deltaY, deltaX) * 180 / Math.PI;
  }

  /**
   * Calculer la vélocité
   */
  private calculateVelocity(): number {
    if (this.touchHistory.length < 2) return 0;

    const recent = this.touchHistory.slice(-5); // 5 derniers points
    if (recent.length < 2) return 0;

    const first = recent[0];
    const last = recent[recent.length - 1];
    
    const deltaX = last.x - first.x;
    const deltaY = last.y - first.y;
    const deltaTime = last.timestamp - first.timestamp;
    
    if (deltaTime === 0) return 0;
    
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    return distance / deltaTime; // pixels per ms
  }

  /**
   * Ajouter à l'historique tactile
   */
  private addToTouchHistory(point: TouchPoint): void {
    this.touchHistory.push(point);
    
    // Garder seulement les 20 derniers points
    if (this.touchHistory.length > 20) {
      this.touchHistory = this.touchHistory.slice(-20);
    }
  }

  /**
   * Déclencher un feedback haptique
   */
  private triggerHapticFeedback(gestureType: GestureType): void {
    if (!('vibrate' in navigator)) return;

    const patterns: Record<GestureType, number[]> = {
      tap: [10],
      doubletap: [10, 50, 10],
      longpress: [50],
      swipe: [20],
      pinch: [5],
      rotate: [5],
      pan: [5],
      pinchstart: [20],
      pinchend: [20],
      rotatestart: [20],
      rotateend: [20],
      panstart: [10],
      panend: [10]
    };

    const pattern = patterns[gestureType];
    if (pattern) {
      navigator.vibrate(pattern);
    }
  }

  /**
   * Désactiver le zoom par défaut
   */
  private disableDefaultZoom(): void {
    // Empêcher le zoom par double tap
    document.addEventListener('touchstart', (event) => {
      if (event.touches.length > 1) {
        event.preventDefault();
      }
    }, { passive: false });

    // Empêcher le zoom par pinch sur les éléments non-gesture
    document.addEventListener('touchmove', (event) => {
      if (event.touches.length > 1) {
        const target = event.target as HTMLElement;
        if (!this.handlers.has(target)) {
          event.preventDefault();
        }
      }
    }, { passive: false });
  }

  /**
   * Configurer les événements globaux
   */
  private setupGlobalEvents(): void {
    // Empêcher le menu contextuel sur long press
    document.addEventListener('contextmenu', (event) => {
      if (event.target && this.handlers.has(event.target as HTMLElement)) {
        event.preventDefault();
      }
    });
  }

  /**
   * Gestionnaires souris (pour développement desktop)
   */
  private handleMouseDown(event: MouseEvent): void {
    // Simuler touchstart avec la souris
    const touchEvent = this.createTouchEventFromMouse(event, 'touchstart');
    this.handleTouchStart(touchEvent as TouchEvent);
  }

  private handleMouseMove(event: MouseEvent): void {
    // Simuler touchmove avec la souris
    const touchEvent = this.createTouchEventFromMouse(event, 'touchmove');
    this.handleTouchMove(touchEvent as TouchEvent);
  }

  private handleMouseUp(event: MouseEvent): void {
    // Simuler touchend avec la souris
    const touchEvent = this.createTouchEventFromMouse(event, 'touchend');
    this.handleTouchEnd(touchEvent as TouchEvent);
  }

  /**
   * Créer un événement tactile à partir d'un événement souris
   */
  private createTouchEventFromMouse(mouseEvent: MouseEvent, type: string): any {
    const touch = {
      clientX: mouseEvent.clientX,
      clientY: mouseEvent.clientY,
      identifier: 0
    };

    return {
      type,
      currentTarget: mouseEvent.currentTarget,
      touches: type === 'touchend' ? [] : [touch],
      preventDefault: () => mouseEvent.preventDefault(),
      stopPropagation: () => mouseEvent.stopPropagation()
    };
  }

  /**
   * API publique pour gestures prédéfinis
   */
  public enableSwipeNavigation(element: HTMLElement, onSwipe: (direction: SwipeDirection) => void): void {
    this.registerGestures(element, ['swipe'], {
      swipe: (event) => onSwipe(event.direction)
    });
  }

  public enablePinchZoom(element: HTMLElement, onPinch: (scale: number) => void): void {
    this.registerGestures(element, ['pinch'], {
      pinch: (event) => onPinch(event.scale)
    });
  }

  public enableRotation(element: HTMLElement, onRotate: (rotation: number) => void): void {
    this.registerGestures(element, ['rotate'], {
      rotate: (event) => onRotate(event.rotation)
    });
  }

  public enablePullToRefresh(element: HTMLElement, onRefresh: () => void): void {
    this.registerGestures(element, ['pan'], {
      pan: (event) => {
        if (event.direction === 'down' && event.distance > 100) {
          onRefresh();
        }
      }
    });
  }

  private debug(message: string, data?: any): void {
    if (this.config.debugMode) {
      console.log(`[Gestures] ${message}`, data);
    }
  }

  /**
   * Nettoyage
   */
  public destroy(): void {
    for (const element of this.handlers.keys()) {
      this.unregisterGestures(element);
    }
    this.handlers.clear();
    this.activeGestures.clear();
    this.touchHistory = [];
  }
}

export default AdvancedGestureSystem;
