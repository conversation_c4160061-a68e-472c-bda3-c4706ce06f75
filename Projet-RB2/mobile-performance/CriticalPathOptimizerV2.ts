/**
 * Critical Path Optimizer V2 - Sprint 17 Jour 3
 * Optimisation finale du chemin critique pour performance mobile <50ms
 */

interface CriticalResource {
  url: string;
  type: 'css' | 'js' | 'font' | 'image';
  priority: 'critical' | 'high' | 'medium' | 'low';
  size: number;
  loadTime: number;
}

interface CriticalPathConfig {
  maxCriticalResources: number;
  targetRenderTime: number; // ms
  enableInlining: boolean;
  enablePreloading: boolean;
  enablePrefetching: boolean;
  debugMode: boolean;
}

export class CriticalPathOptimizerV2 {
  private config: CriticalPathConfig;
  private criticalResources: CriticalResource[] = [];
  private performanceObserver: PerformanceObserver | null = null;

  constructor(config: Partial<CriticalPathConfig> = {}) {
    this.config = {
      maxCriticalResources: 6,
      targetRenderTime: 180, // ms
      enableInlining: true,
      enablePreloading: true,
      enablePrefetching: true,
      debugMode: false,
      ...config
    };

    this.initializeOptimizer();
  }

  /**
   * Initialiser l'optimiseur
   */
  private async initializeOptimizer(): Promise<void> {
    // Analyser les ressources critiques
    await this.analyzeCriticalResources();
    
    // Configurer l'observation des performances
    this.setupPerformanceObserver();
    
    // Optimiser le chemin critique
    await this.optimizeCriticalPath();
  }

  /**
   * Optimiser le chemin critique complet
   */
  async optimizeCriticalPath(): Promise<void> {
    try {
      // 1. Inline critical CSS
      await this.inlineCriticalCSS();
      
      // 2. Preload critical resources
      await this.preloadCriticalResources();
      
      // 3. Optimize critical JavaScript
      await this.optimizeCriticalJS();
      
      // 4. Minimize critical path length
      await this.minimizeCriticalPathLength();
      
      // 5. Implement resource hints
      await this.implementResourceHints();
      
      // 6. Validate performance
      await this.validatePerformance();

      if (this.config.debugMode) {
        console.log('✅ Critical Path Optimization completed');
      }
    } catch (error) {
      console.error('❌ Critical Path Optimization failed:', error);
      throw error;
    }
  }

  /**
   * Analyser les ressources critiques
   */
  private async analyzeCriticalResources(): Promise<void> {
    // Analyser les CSS critiques
    const criticalCSS = await this.identifyCriticalCSS();
    
    // Analyser les JS critiques
    const criticalJS = await this.identifyCriticalJS();
    
    // Analyser les fonts critiques
    const criticalFonts = await this.identifyCriticalFonts();
    
    // Analyser les images critiques
    const criticalImages = await this.identifyCriticalImages();

    this.criticalResources = [
      ...criticalCSS,
      ...criticalJS,
      ...criticalFonts,
      ...criticalImages
    ].sort((a, b) => this.calculatePriority(b) - this.calculatePriority(a));
  }

  /**
   * Identifier les CSS critiques
   */
  private async identifyCriticalCSS(): Promise<CriticalResource[]> {
    const criticalCSS: CriticalResource[] = [];
    const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');

    for (const stylesheet of Array.from(stylesheets)) {
      const href = (stylesheet as HTMLLinkElement).href;
      
      // Analyser si le CSS est critique (above-the-fold)
      const isCritical = await this.isCSSCritical(href);
      
      if (isCritical) {
        criticalCSS.push({
          url: href,
          type: 'css',
          priority: 'critical',
          size: await this.getResourceSize(href),
          loadTime: await this.measureLoadTime(href)
        });
      }
    }

    return criticalCSS;
  }

  /**
   * Inline critical CSS
   */
  async inlineCriticalCSS(): Promise<void> {
    if (!this.config.enableInlining) return;

    const criticalCSS = this.criticalResources.filter(r => 
      r.type === 'css' && r.priority === 'critical'
    );

    for (const css of criticalCSS) {
      try {
        // Récupérer le contenu CSS
        const cssContent = await this.fetchCSSContent(css.url);
        
        // Extraire seulement le CSS critique (above-the-fold)
        const criticalCSSContent = await this.extractCriticalCSS(cssContent);
        
        // Inline le CSS critique
        this.inlineCSS(criticalCSSContent);
        
        // Lazy load le CSS non-critique
        this.lazyLoadCSS(css.url);

      } catch (error) {
        console.warn(`Failed to inline critical CSS: ${css.url}`, error);
      }
    }
  }

  /**
   * Preload critical resources
   */
  async preloadCriticalResources(): Promise<void> {
    if (!this.config.enablePreloading) return;

    const criticalResources = this.criticalResources
      .slice(0, this.config.maxCriticalResources)
      .filter(r => r.priority === 'critical');

    for (const resource of criticalResources) {
      this.preloadResource(resource);
    }
  }

  /**
   * Optimiser le JavaScript critique
   */
  async optimizeCriticalJS(): Promise<void> {
    const criticalJS = this.criticalResources.filter(r => 
      r.type === 'js' && r.priority === 'critical'
    );

    for (const js of criticalJS) {
      // Inline small critical JS
      if (js.size < 5000) { // 5KB
        await this.inlineJS(js.url);
      } else {
        // Preload larger critical JS
        this.preloadResource(js);
      }
    }
  }

  /**
   * Minimiser la longueur du chemin critique
   */
  async minimizeCriticalPathLength(): Promise<void> {
    // Réduire le nombre de ressources critiques
    const criticalCount = this.criticalResources.filter(r => 
      r.priority === 'critical'
    ).length;

    if (criticalCount > this.config.maxCriticalResources) {
      // Déprioriser certaines ressources
      await this.deprioritizeResources();
    }

    // Combiner les ressources critiques
    await this.combineResources();
  }

  /**
   * Implémenter les resource hints
   */
  async implementResourceHints(): Promise<void> {
    // DNS prefetch pour les domaines externes
    this.implementDNSPrefetch();
    
    // Preconnect pour les ressources critiques
    this.implementPreconnect();
    
    // Prefetch pour les ressources suivantes
    if (this.config.enablePrefetching) {
      this.implementPrefetch();
    }
  }

  /**
   * Preload une ressource
   */
  private preloadResource(resource: CriticalResource): void {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = resource.url;
    link.as = this.getResourceAs(resource.type);
    
    if (resource.type === 'font') {
      link.crossOrigin = 'anonymous';
    }
    
    document.head.appendChild(link);
  }

  /**
   * Inline CSS content
   */
  private inlineCSS(cssContent: string): void {
    const style = document.createElement('style');
    style.textContent = cssContent;
    style.setAttribute('data-critical', 'true');
    document.head.appendChild(style);
  }

  /**
   * Lazy load CSS
   */
  private lazyLoadCSS(href: string): void {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = href;
    link.as = 'style';
    link.onload = () => {
      link.rel = 'stylesheet';
    };
    document.head.appendChild(link);
  }

  /**
   * Mesurer les performances
   */
  private setupPerformanceObserver(): void {
    if ('PerformanceObserver' in window) {
      this.performanceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        
        for (const entry of entries) {
          if (entry.entryType === 'paint') {
            this.handlePaintTiming(entry as PerformancePaintTiming);
          } else if (entry.entryType === 'largest-contentful-paint') {
            this.handleLCPTiming(entry);
          }
        }
      });

      this.performanceObserver.observe({ 
        entryTypes: ['paint', 'largest-contentful-paint'] 
      });
    }
  }

  /**
   * Valider les performances
   */
  async validatePerformance(): Promise<boolean> {
    return new Promise((resolve) => {
      // Attendre le FCP
      const checkPerformance = () => {
        const paintEntries = performance.getEntriesByType('paint');
        const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint');
        
        if (fcp && fcp.startTime <= this.config.targetRenderTime) {
          if (this.config.debugMode) {
            console.log(`✅ Critical Path Performance: ${fcp.startTime}ms`);
          }
          resolve(true);
        } else if (fcp) {
          console.warn(`⚠️ Critical Path Performance: ${fcp.startTime}ms (target: ${this.config.targetRenderTime}ms)`);
          resolve(false);
        } else {
          // Retry after 100ms
          setTimeout(checkPerformance, 100);
        }
      };

      checkPerformance();
    });
  }

  /**
   * Calculer la priorité d'une ressource
   */
  private calculatePriority(resource: CriticalResource): number {
    let score = 0;
    
    // Priorité par type
    switch (resource.priority) {
      case 'critical': score += 100; break;
      case 'high': score += 75; break;
      case 'medium': score += 50; break;
      case 'low': score += 25; break;
    }
    
    // Pénalité pour la taille
    score -= Math.min(resource.size / 1000, 50);
    
    // Pénalité pour le temps de chargement
    score -= Math.min(resource.loadTime / 10, 30);
    
    return score;
  }

  /**
   * Obtenir l'attribut 'as' pour preload
   */
  private getResourceAs(type: string): string {
    switch (type) {
      case 'css': return 'style';
      case 'js': return 'script';
      case 'font': return 'font';
      case 'image': return 'image';
      default: return 'fetch';
    }
  }

  /**
   * Nettoyer l'optimiseur
   */
  destroy(): void {
    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
      this.performanceObserver = null;
    }
  }

  // Méthodes utilitaires (implémentation simplifiée)
  private async isCSSCritical(href: string): Promise<boolean> {
    // Logique pour déterminer si le CSS est critique
    return true; // Simplifié pour l'exemple
  }

  private async getResourceSize(url: string): Promise<number> {
    // Obtenir la taille de la ressource
    return 1000; // Simplifié pour l'exemple
  }

  private async measureLoadTime(url: string): Promise<number> {
    // Mesurer le temps de chargement
    return 100; // Simplifié pour l'exemple
  }

  private async fetchCSSContent(url: string): Promise<string> {
    const response = await fetch(url);
    return response.text();
  }

  private async extractCriticalCSS(cssContent: string): Promise<string> {
    // Extraire seulement le CSS critique
    return cssContent; // Simplifié pour l'exemple
  }

  private async identifyCriticalJS(): Promise<CriticalResource[]> {
    return []; // Simplifié pour l'exemple
  }

  private async identifyCriticalFonts(): Promise<CriticalResource[]> {
    return []; // Simplifié pour l'exemple
  }

  private async identifyCriticalImages(): Promise<CriticalResource[]> {
    return []; // Simplifié pour l'exemple
  }

  private async inlineJS(url: string): Promise<void> {
    // Inline JavaScript critique
  }

  private async deprioritizeResources(): Promise<void> {
    // Déprioriser certaines ressources
  }

  private async combineResources(): Promise<void> {
    // Combiner les ressources critiques
  }

  private implementDNSPrefetch(): void {
    // Implémenter DNS prefetch
  }

  private implementPreconnect(): void {
    // Implémenter preconnect
  }

  private implementPrefetch(): void {
    // Implémenter prefetch
  }

  private handlePaintTiming(entry: PerformancePaintTiming): void {
    if (this.config.debugMode) {
      console.log(`Paint timing: ${entry.name} at ${entry.startTime}ms`);
    }
  }

  private handleLCPTiming(entry: any): void {
    if (this.config.debugMode) {
      console.log(`LCP timing: ${entry.startTime}ms`);
    }
  }
}
