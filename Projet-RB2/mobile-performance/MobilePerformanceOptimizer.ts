/**
 * Mobile Performance Optimizer - Sprint 17 Jour 1
 * Optimiseur spécialisé pour performance mobile <100ms
 */

interface MobilePerformanceConfig {
  targetNavigationTime: number; // ms
  targetFirstPaint: number; // ms
  targetLCP: number; // ms
  enableAgressiveOptimizations: boolean;
  enableMobileSpecificOptimizations: boolean;
  enableAdaptiveLoading: boolean;
  enableConnectionAwareLoading: boolean;
  debugMode: boolean;
}

interface DeviceCapabilities {
  deviceMemory: number; // GB
  hardwareConcurrency: number; // CPU cores
  connectionType: string;
  connectionSpeed: string;
  isLowEndDevice: boolean;
  supportedFormats: string[];
}

interface MobileOptimization {
  name: string;
  priority: 'critical' | 'high' | 'medium' | 'low';
  impact: number; // 1-10
  mobileSpecific: boolean;
  implementation: () => Promise<void>;
  validation: () => Promise<boolean>;
}

export class MobilePerformanceOptimizer {
  private config: MobilePerformanceConfig;
  private deviceCapabilities: DeviceCapabilities;
  private appliedOptimizations: Set<string> = new Set();
  private performanceObserver?: PerformanceObserver;
  private connectionObserver?: any;

  constructor(config: Partial<MobilePerformanceConfig> = {}) {
    this.config = {
      targetNavigationTime: 100, // Objectif agressif <100ms mobile
      targetFirstPaint: 500,
      targetLCP: 1000,
      enableAgressiveOptimizations: true,
      enableMobileSpecificOptimizations: true,
      enableAdaptiveLoading: true,
      enableConnectionAwareLoading: true,
      debugMode: false,
      ...config
    };

    this.deviceCapabilities = this.detectDeviceCapabilities();
    this.initialize();
  }

  /**
   * Initialisation de l'optimiseur mobile
   */
  private async initialize() {
    this.debug('Initializing Mobile Performance Optimizer...');
    this.debug('Device capabilities:', this.deviceCapabilities);

    // Analyser les performances actuelles
    const currentMetrics = await this.analyzeCurrentPerformance();
    this.debug('Current metrics:', currentMetrics);

    // Sélectionner les optimisations nécessaires
    const optimizations = this.selectMobileOptimizations(currentMetrics);

    // Appliquer les optimisations par ordre de priorité
    await this.applyOptimizations(optimizations);

    // Démarrer le monitoring continu
    this.startContinuousMonitoring();

    // Configurer l'adaptation à la connexion
    if (this.config.enableConnectionAwareLoading) {
      this.setupConnectionAwareLoading();
    }

    this.debug('Mobile Performance Optimizer initialized successfully');
  }

  /**
   * Détecter les capacités de l'appareil
   */
  private detectDeviceCapabilities(): DeviceCapabilities {
    const nav = navigator as any;
    
    // Mémoire de l'appareil
    const deviceMemory = nav.deviceMemory || 4; // Default 4GB
    
    // Nombre de cœurs CPU
    const hardwareConcurrency = nav.hardwareConcurrency || 4;
    
    // Type de connexion
    const connection = nav.connection || nav.mozConnection || nav.webkitConnection;
    const connectionType = connection?.type || 'unknown';
    const connectionSpeed = connection?.effectiveType || '4g';
    
    // Détection appareil bas de gamme
    const isLowEndDevice = deviceMemory <= 2 || hardwareConcurrency <= 2;
    
    // Formats supportés
    const supportedFormats = this.detectSupportedFormats();

    return {
      deviceMemory,
      hardwareConcurrency,
      connectionType,
      connectionSpeed,
      isLowEndDevice,
      supportedFormats
    };
  }

  /**
   * Détecter les formats d'image supportés
   */
  private detectSupportedFormats(): string[] {
    const formats: string[] = ['jpeg', 'png'];
    
    // Test WebP
    const webpCanvas = document.createElement('canvas');
    webpCanvas.width = 1;
    webpCanvas.height = 1;
    if (webpCanvas.toDataURL('image/webp').indexOf('data:image/webp') === 0) {
      formats.push('webp');
    }
    
    // Test AVIF (approximatif)
    if ('createImageBitmap' in window) {
      formats.push('avif');
    }
    
    return formats;
  }

  /**
   * Sélectionner les optimisations mobiles
   */
  private selectMobileOptimizations(currentMetrics: any): MobileOptimization[] {
    const optimizations: MobileOptimization[] = [
      // Optimisations critiques mobiles
      {
        name: 'Mobile Critical CSS',
        priority: 'critical',
        impact: 10,
        mobileSpecific: true,
        implementation: this.implementMobileCriticalCSS.bind(this),
        validation: this.validateMobileCriticalCSS.bind(this)
      },
      {
        name: 'Touch Optimizations',
        priority: 'critical',
        impact: 9,
        mobileSpecific: true,
        implementation: this.implementTouchOptimizations.bind(this),
        validation: this.validateTouchOptimizations.bind(this)
      },
      {
        name: 'Mobile Bundle Splitting',
        priority: 'critical',
        impact: 9,
        mobileSpecific: true,
        implementation: this.implementMobileBundleSplitting.bind(this),
        validation: this.validateMobileBundleSplitting.bind(this)
      },
      {
        name: 'Adaptive Image Loading',
        priority: 'high',
        impact: 8,
        mobileSpecific: true,
        implementation: this.implementAdaptiveImageLoading.bind(this),
        validation: this.validateAdaptiveImageLoading.bind(this)
      },
      {
        name: 'Mobile Lazy Loading',
        priority: 'high',
        impact: 8,
        mobileSpecific: true,
        implementation: this.implementMobileLazyLoading.bind(this),
        validation: this.validateMobileLazyLoading.bind(this)
      },
      {
        name: 'Connection Aware Loading',
        priority: 'high',
        impact: 7,
        mobileSpecific: true,
        implementation: this.implementConnectionAwareLoading.bind(this),
        validation: this.validateConnectionAwareLoading.bind(this)
      },
      {
        name: 'Mobile Preloading',
        priority: 'medium',
        impact: 6,
        mobileSpecific: true,
        implementation: this.implementMobilePreloading.bind(this),
        validation: this.validateMobilePreloading.bind(this)
      },
      {
        name: 'Viewport Optimizations',
        priority: 'medium',
        impact: 6,
        mobileSpecific: true,
        implementation: this.implementViewportOptimizations.bind(this),
        validation: this.validateViewportOptimizations.bind(this)
      }
    ];

    // Filtrer selon les capacités de l'appareil
    return optimizations.filter(opt => {
      if (this.deviceCapabilities.isLowEndDevice && opt.impact < 7) {
        return false; // Skip optimisations moins importantes sur bas de gamme
      }
      return true;
    }).sort((a, b) => {
      const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  /**
   * Appliquer les optimisations
   */
  private async applyOptimizations(optimizations: MobileOptimization[]) {
    for (const optimization of optimizations) {
      try {
        this.debug(`Applying mobile optimization: ${optimization.name}`);
        
        await optimization.implementation();
        
        const isValid = await optimization.validation();
        
        if (isValid) {
          this.appliedOptimizations.add(optimization.name);
          this.debug(`✅ Applied: ${optimization.name}`);
        } else {
          this.debug(`❌ Failed validation: ${optimization.name}`);
        }
      } catch (error) {
        this.debug(`Error applying ${optimization.name}:`, error);
      }
    }
  }

  /**
   * CSS critique mobile
   */
  private async implementMobileCriticalCSS(): Promise<void> {
    const mobileCriticalCSS = `
      /* CSS critique mobile optimisé */
      * {
        box-sizing: border-box;
        -webkit-tap-highlight-color: transparent;
      }
      
      body {
        margin: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        touch-action: manipulation;
      }
      
      .mobile-header {
        position: sticky;
        top: 0;
        background: #fff;
        z-index: 1000;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transform: translateZ(0); /* Force GPU acceleration */
      }
      
      .mobile-nav {
        display: flex;
        justify-content: space-around;
        padding: 8px 0;
        background: #fff;
        border-top: 1px solid #eee;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 1000;
      }
      
      .touch-target {
        min-height: 44px;
        min-width: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      .loading-skeleton {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
      }
      
      @keyframes loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
      }
      
      @media (max-width: 768px) {
        .desktop-only { display: none !important; }
        .mobile-hidden { display: none !important; }
      }
    `;

    const style = document.createElement('style');
    style.textContent = mobileCriticalCSS;
    style.setAttribute('data-mobile-critical', 'true');
    document.head.insertBefore(style, document.head.firstChild);
  }

  /**
   * Optimisations tactiles
   */
  private async implementTouchOptimizations(): Promise<void> {
    // Désactiver le zoom sur les inputs
    const viewport = document.querySelector('meta[name="viewport"]');
    if (viewport) {
      viewport.setAttribute('content', 
        'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no'
      );
    }

    // Optimiser les événements tactiles
    document.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: true });
    document.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: true });
    document.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: true });

    // Ajouter des classes CSS pour les interactions tactiles
    document.body.classList.add('touch-device');
  }

  /**
   * Bundle splitting mobile
   */
  private async implementMobileBundleSplitting(): Promise<void> {
    // Charger seulement les composants nécessaires sur mobile
    const isMobile = window.innerWidth <= 768;
    
    if (isMobile) {
      // Lazy load des composants desktop
      (window as any).__MOBILE_MODE__ = true;
      
      // Preload des composants mobiles critiques
      const mobileComponents = [
        '/js/mobile-navigation.js',
        '/js/mobile-search.js',
        '/js/touch-gestures.js'
      ];

      for (const component of mobileComponents) {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = component;
        link.as = 'script';
        document.head.appendChild(link);
      }
    }
  }

  /**
   * Chargement d'images adaptatif
   */
  private async implementAdaptiveImageLoading(): Promise<void> {
    const images = document.querySelectorAll('img[data-src]');
    
    images.forEach(img => {
      const imageElement = img as HTMLImageElement;
      const dataSrc = imageElement.dataset.src;
      
      if (dataSrc) {
        // Choisir le format optimal
        const optimalSrc = this.getOptimalImageSrc(dataSrc);
        
        // Lazy loading avec Intersection Observer
        const observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const img = entry.target as HTMLImageElement;
              img.src = optimalSrc;
              img.classList.add('loaded');
              observer.unobserve(img);
            }
          });
        }, {
          rootMargin: '50px' // Preload 50px avant d'être visible
        });

        observer.observe(imageElement);
      }
    });
  }

  /**
   * Obtenir la source d'image optimale
   */
  private getOptimalImageSrc(baseSrc: string): string {
    const connection = (navigator as any).connection;
    const isSlowConnection = connection && 
      ['slow-2g', '2g', '3g'].includes(connection.effectiveType);
    
    // Choisir la qualité selon la connexion
    const quality = isSlowConnection ? 'low' : 'high';
    
    // Choisir le format selon le support
    let format = 'jpg';
    if (this.deviceCapabilities.supportedFormats.includes('avif')) {
      format = 'avif';
    } else if (this.deviceCapabilities.supportedFormats.includes('webp')) {
      format = 'webp';
    }

    // Construire l'URL optimisée
    return baseSrc
      .replace(/\.(jpg|jpeg|png)$/i, `.${format}`)
      .replace(/(\.[^.]+)$/, `_${quality}$1`);
  }

  /**
   * Lazy loading mobile optimisé
   */
  private async implementMobileLazyLoading(): Promise<void> {
    // Configuration plus agressive pour mobile
    const lazyElements = document.querySelectorAll('[data-lazy]');
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const element = entry.target as HTMLElement;
          
          // Charger le contenu
          this.loadLazyContent(element);
          
          observer.unobserve(element);
        }
      });
    }, {
      rootMargin: this.deviceCapabilities.isLowEndDevice ? '20px' : '100px'
    });

    lazyElements.forEach(element => observer.observe(element));
  }

  /**
   * Chargement conscient de la connexion
   */
  private async implementConnectionAwareLoading(): Promise<void> {
    const connection = (navigator as any).connection;
    
    if (connection) {
      // Adapter le comportement selon la connexion
      this.adaptToConnection(connection.effectiveType);
      
      // Écouter les changements de connexion
      connection.addEventListener('change', () => {
        this.adaptToConnection(connection.effectiveType);
      });
    }
  }

  /**
   * Adapter le comportement à la connexion
   */
  private adaptToConnection(effectiveType: string) {
    const isSlowConnection = ['slow-2g', '2g', '3g'].includes(effectiveType);
    
    if (isSlowConnection) {
      // Mode économie de données
      document.body.classList.add('data-saver-mode');
      
      // Désactiver les animations non critiques
      this.disableNonCriticalAnimations();
      
      // Réduire la qualité des images
      this.reduceImageQuality();
      
    } else {
      // Mode performance normale
      document.body.classList.remove('data-saver-mode');
    }
  }

  /**
   * Preloading mobile optimisé
   */
  private async implementMobilePreloading(): Promise<void> {
    // Preload seulement les ressources critiques sur mobile
    const criticalResources = [
      { href: '/css/mobile-critical.css', as: 'style' },
      { href: '/js/mobile-core.js', as: 'script' },
      { href: '/api/user/profile', as: 'fetch' }
    ];

    criticalResources.forEach(resource => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = resource.href;
      link.as = resource.as;
      if (resource.as === 'fetch') {
        link.crossOrigin = 'anonymous';
      }
      document.head.appendChild(link);
    });
  }

  /**
   * Optimisations viewport
   */
  private async implementViewportOptimizations(): Promise<void> {
    // Optimiser le viewport pour mobile
    let viewport = document.querySelector('meta[name="viewport"]');
    
    if (!viewport) {
      viewport = document.createElement('meta');
      viewport.setAttribute('name', 'viewport');
      document.head.appendChild(viewport);
    }

    // Configuration optimisée pour performance
    viewport.setAttribute('content', 
      'width=device-width, initial-scale=1, viewport-fit=cover'
    );

    // Ajouter les meta tags pour PWA
    const metaTags = [
      { name: 'mobile-web-app-capable', content: 'yes' },
      { name: 'apple-mobile-web-app-capable', content: 'yes' },
      { name: 'apple-mobile-web-app-status-bar-style', content: 'default' }
    ];

    metaTags.forEach(tag => {
      let meta = document.querySelector(`meta[name="${tag.name}"]`);
      if (!meta) {
        meta = document.createElement('meta');
        meta.setAttribute('name', tag.name);
        document.head.appendChild(meta);
      }
      meta.setAttribute('content', tag.content);
    });
  }

  /**
   * Gestionnaires d'événements tactiles
   */
  private handleTouchStart(event: TouchEvent) {
    // Optimiser les interactions tactiles
    const target = event.target as HTMLElement;
    target.classList.add('touch-active');
  }

  private handleTouchMove(event: TouchEvent) {
    // Gérer le défilement tactile
  }

  private handleTouchEnd(event: TouchEvent) {
    const target = event.target as HTMLElement;
    target.classList.remove('touch-active');
  }

  /**
   * Charger le contenu lazy
   */
  private loadLazyContent(element: HTMLElement) {
    const dataSrc = element.dataset.src;
    const dataContent = element.dataset.content;
    
    if (dataSrc && element instanceof HTMLImageElement) {
      element.src = dataSrc;
    }
    
    if (dataContent) {
      element.innerHTML = dataContent;
    }
    
    element.classList.add('loaded');
  }

  /**
   * Désactiver les animations non critiques
   */
  private disableNonCriticalAnimations() {
    const style = document.createElement('style');
    style.textContent = `
      .non-critical-animation,
      .fade-in,
      .slide-in {
        animation: none !important;
        transition: none !important;
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * Réduire la qualité des images
   */
  private reduceImageQuality() {
    const images = document.querySelectorAll('img');
    images.forEach(img => {
      if (img.src && !img.dataset.optimized) {
        img.src = img.src.replace(/(\.[^.]+)$/, '_low$1');
        img.dataset.optimized = 'true';
      }
    });
  }

  /**
   * Analyser les performances actuelles
   */
  private async analyzeCurrentPerformance(): Promise<any> {
    return new Promise((resolve) => {
      const metrics = {
        navigation: 0,
        firstPaint: 0,
        largestContentfulPaint: 0,
        firstInputDelay: 0,
        cumulativeLayoutShift: 0
      };

      // Navigation Timing
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigation) {
        metrics.navigation = navigation.loadEventEnd - navigation.loadEventStart;
      }

      // Paint Timing
      const paintEntries = performance.getEntriesByType('paint');
      const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
      if (firstPaint) {
        metrics.firstPaint = firstPaint.startTime;
      }

      setTimeout(() => resolve(metrics), 1000);
    });
  }

  /**
   * Monitoring continu
   */
  private startContinuousMonitoring() {
    // Observer les Core Web Vitals
    if ('PerformanceObserver' in window) {
      // LCP
      new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.debug('LCP:', lastEntry.startTime);
      }).observe({ entryTypes: ['largest-contentful-paint'] });

      // FID
      new PerformanceObserver((list) => {
        const firstInput = list.getEntries()[0];
        this.debug('FID:', (firstInput as any).processingStart - firstInput.startTime);
      }).observe({ entryTypes: ['first-input'] });

      // CLS
      let clsValue = 0;
      new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value;
          }
        }
        this.debug('CLS:', clsValue);
      }).observe({ entryTypes: ['layout-shift'] });
    }
  }

  /**
   * Configuration de l'adaptation à la connexion
   */
  private setupConnectionAwareLoading() {
    const connection = (navigator as any).connection;
    
    if (connection) {
      connection.addEventListener('change', () => {
        this.debug('Connection changed:', connection.effectiveType);
        this.adaptToConnection(connection.effectiveType);
      });
    }
  }

  /**
   * Méthodes de validation
   */
  private async validateMobileCriticalCSS(): Promise<boolean> {
    return document.querySelector('style[data-mobile-critical]') !== null;
  }

  private async validateTouchOptimizations(): Promise<boolean> {
    return document.body.classList.contains('touch-device');
  }

  private async validateMobileBundleSplitting(): Promise<boolean> {
    return (window as any).__MOBILE_MODE__ === true;
  }

  private async validateAdaptiveImageLoading(): Promise<boolean> {
    return document.querySelectorAll('img[data-src]').length > 0;
  }

  private async validateMobileLazyLoading(): Promise<boolean> {
    return document.querySelectorAll('[data-lazy]').length > 0;
  }

  private async validateConnectionAwareLoading(): Promise<boolean> {
    return (navigator as any).connection !== undefined;
  }

  private async validateMobilePreloading(): Promise<boolean> {
    return document.querySelectorAll('link[rel="preload"]').length > 0;
  }

  private async validateViewportOptimizations(): Promise<boolean> {
    const viewport = document.querySelector('meta[name="viewport"]');
    return viewport !== null;
  }

  /**
   * API publique
   */
  public async getPerformanceReport(): Promise<any> {
    const currentMetrics = await this.analyzeCurrentPerformance();
    
    return {
      deviceCapabilities: this.deviceCapabilities,
      currentMetrics,
      appliedOptimizations: Array.from(this.appliedOptimizations),
      recommendations: this.generateMobileRecommendations(currentMetrics)
    };
  }

  private generateMobileRecommendations(metrics: any): string[] {
    const recommendations: string[] = [];
    
    if (metrics.navigation > this.config.targetNavigationTime) {
      recommendations.push('Consider more aggressive mobile bundle splitting');
    }
    
    if (this.deviceCapabilities.isLowEndDevice) {
      recommendations.push('Enable data saver mode for low-end devices');
    }
    
    return recommendations;
  }

  private debug(message: string, data?: any) {
    if (this.config.debugMode) {
      console.log(`[Mobile Performance] ${message}`, data);
    }
  }

  /**
   * Nettoyage
   */
  public destroy() {
    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
    }
    this.appliedOptimizations.clear();
  }
}

export default MobilePerformanceOptimizer;
