/**
 * Adaptive UX Engine - Sprint 17 Jour 3
 * Moteur d'interface utilisateur adaptative pour mobile
 */

interface DeviceCapabilities {
  screenSize: 'small' | 'medium' | 'large';
  pixelRatio: number;
  touchSupport: boolean;
  orientationSupport: boolean;
  memoryGB: number;
  cpuCores: number;
  connectionType: string;
  connectionSpeed: 'slow' | 'medium' | 'fast';
  batteryLevel?: number;
  isLowEndDevice: boolean;
}

interface UXAdaptation {
  simplifiedUI: boolean;
  reducedAnimations: boolean;
  compactLayout: boolean;
  optimizedImages: boolean;
  reducedFunctionality: boolean;
  touchOptimized: boolean;
  accessibilityEnhanced: boolean;
}

interface AdaptiveConfig {
  enableDeviceDetection: boolean;
  enablePerformanceAdaptation: boolean;
  enableConnectionAdaptation: boolean;
  enableBatteryAdaptation: boolean;
  enableAccessibilityAdaptation: boolean;
  debugMode: boolean;
}

export class AdaptiveUXEngine {
  private config: AdaptiveConfig;
  private deviceCapabilities: DeviceCapabilities;
  private currentAdaptation: UXAdaptation;
  private observers: Map<string, (adaptation: UXAdaptation) => void> = new Map();

  constructor(config: Partial<AdaptiveConfig> = {}) {
    this.config = {
      enableDeviceDetection: true,
      enablePerformanceAdaptation: true,
      enableConnectionAdaptation: true,
      enableBatteryAdaptation: true,
      enableAccessibilityAdaptation: true,
      debugMode: false,
      ...config
    };

    this.deviceCapabilities = {
      screenSize: 'medium',
      pixelRatio: 1,
      touchSupport: false,
      orientationSupport: false,
      memoryGB: 4,
      cpuCores: 4,
      connectionType: 'unknown',
      connectionSpeed: 'medium',
      isLowEndDevice: false
    };

    this.currentAdaptation = {
      simplifiedUI: false,
      reducedAnimations: false,
      compactLayout: false,
      optimizedImages: false,
      reducedFunctionality: false,
      touchOptimized: false,
      accessibilityEnhanced: false
    };

    this.initializeAdaptiveUX();
  }

  /**
   * Initialiser le moteur UX adaptatif
   */
  private async initializeAdaptiveUX(): Promise<void> {
    try {
      // Détecter les capacités de l'appareil
      await this.detectDeviceCapabilities();
      
      // Implémenter l'interface adaptative
      await this.implementAdaptiveUX();
      
      // Démarrer le monitoring
      this.startAdaptiveMonitoring();

      if (this.config.debugMode) {
        console.log('✅ Adaptive UX Engine initialized', this.deviceCapabilities);
      }
    } catch (error) {
      console.error('❌ Adaptive UX Engine initialization failed:', error);
      throw error;
    }
  }

  /**
   * Implémenter l'UX adaptative
   */
  async implementAdaptiveUX(): Promise<void> {
    try {
      // 1. Détecter les capacités de l'appareil
      await this.detectDeviceCapabilities();
      
      // 2. Rendre l'interface adaptative
      await this.renderAdaptiveInterface();
      
      // 3. Basculer les fonctionnalités selon les performances
      await this.toggleFeaturesBasedOnPerformance();
      
      // 4. Optimiser pour le tactile
      await this.optimizeForTouch();
      
      // 5. Améliorer l'accessibilité
      await this.enhanceAccessibility();

      if (this.config.debugMode) {
        console.log('✅ Adaptive UX implementation completed');
      }
    } catch (error) {
      console.error('❌ Adaptive UX implementation failed:', error);
      throw error;
    }
  }

  /**
   * Détecter les capacités de l'appareil
   */
  async detectDeviceCapabilities(): Promise<void> {
    if (!this.config.enableDeviceDetection) return;

    // Taille d'écran
    this.deviceCapabilities.screenSize = this.detectScreenSize();
    
    // Pixel ratio
    this.deviceCapabilities.pixelRatio = window.devicePixelRatio || 1;
    
    // Support tactile
    this.deviceCapabilities.touchSupport = this.detectTouchSupport();
    
    // Support orientation
    this.deviceCapabilities.orientationSupport = 'orientation' in window;
    
    // Mémoire et CPU
    await this.detectHardwareCapabilities();
    
    // Connexion
    this.detectConnectionCapabilities();
    
    // Appareil bas de gamme
    this.deviceCapabilities.isLowEndDevice = this.detectLowEndDevice();
  }

  /**
   * Détecter la taille d'écran
   */
  private detectScreenSize(): 'small' | 'medium' | 'large' {
    const width = window.innerWidth;
    
    if (width < 576) return 'small';
    if (width < 992) return 'medium';
    return 'large';
  }

  /**
   * Détecter le support tactile
   */
  private detectTouchSupport(): boolean {
    return 'ontouchstart' in window || 
           navigator.maxTouchPoints > 0 || 
           (navigator as any).msMaxTouchPoints > 0;
  }

  /**
   * Détecter les capacités matérielles
   */
  private async detectHardwareCapabilities(): Promise<void> {
    // Mémoire
    if ('deviceMemory' in navigator) {
      this.deviceCapabilities.memoryGB = (navigator as any).deviceMemory;
    }
    
    // CPU
    if ('hardwareConcurrency' in navigator) {
      this.deviceCapabilities.cpuCores = navigator.hardwareConcurrency;
    }
  }

  /**
   * Détecter les capacités de connexion
   */
  private detectConnectionCapabilities(): void {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      this.deviceCapabilities.connectionType = connection.effectiveType || 'unknown';
      
      // Déterminer la vitesse
      switch (connection.effectiveType) {
        case 'slow-2g':
        case '2g':
          this.deviceCapabilities.connectionSpeed = 'slow';
          break;
        case '3g':
          this.deviceCapabilities.connectionSpeed = 'medium';
          break;
        case '4g':
        default:
          this.deviceCapabilities.connectionSpeed = 'fast';
          break;
      }
    }
  }

  /**
   * Détecter un appareil bas de gamme
   */
  private detectLowEndDevice(): boolean {
    return this.deviceCapabilities.memoryGB <= 2 || 
           this.deviceCapabilities.cpuCores <= 2 ||
           this.deviceCapabilities.connectionSpeed === 'slow';
  }

  /**
   * Rendre l'interface adaptative
   */
  async renderAdaptiveInterface(): Promise<void> {
    const adaptation = this.calculateAdaptation();
    
    // Appliquer l'interface simplifiée
    if (adaptation.simplifiedUI) {
      this.applySimplifiedUI();
    }
    
    // Appliquer la mise en page compacte
    if (adaptation.compactLayout) {
      this.applyCompactLayout();
    }
    
    // Optimiser les images
    if (adaptation.optimizedImages) {
      this.optimizeImages();
    }
    
    // Réduire les animations
    if (adaptation.reducedAnimations) {
      this.reduceAnimations();
    }
    
    this.currentAdaptation = adaptation;
    this.notifyObservers();
  }

  /**
   * Calculer l'adaptation nécessaire
   */
  private calculateAdaptation(): UXAdaptation {
    const adaptation: UXAdaptation = {
      simplifiedUI: false,
      reducedAnimations: false,
      compactLayout: false,
      optimizedImages: false,
      reducedFunctionality: false,
      touchOptimized: false,
      accessibilityEnhanced: false
    };

    // Adaptation selon l'appareil
    if (this.deviceCapabilities.isLowEndDevice) {
      adaptation.simplifiedUI = true;
      adaptation.reducedAnimations = true;
      adaptation.optimizedImages = true;
      adaptation.reducedFunctionality = true;
    }

    // Adaptation selon la taille d'écran
    if (this.deviceCapabilities.screenSize === 'small') {
      adaptation.compactLayout = true;
      adaptation.touchOptimized = true;
    }

    // Adaptation selon la connexion
    if (this.deviceCapabilities.connectionSpeed === 'slow') {
      adaptation.optimizedImages = true;
      adaptation.reducedFunctionality = true;
    }

    // Adaptation selon le tactile
    if (this.deviceCapabilities.touchSupport) {
      adaptation.touchOptimized = true;
    }

    return adaptation;
  }

  /**
   * Basculer les fonctionnalités selon les performances
   */
  async toggleFeaturesBasedOnPerformance(): Promise<void> {
    if (!this.config.enablePerformanceAdaptation) return;

    const performanceScore = this.calculatePerformanceScore();
    
    if (performanceScore < 50) {
      // Performance faible - désactiver les fonctionnalités lourdes
      this.disableHeavyFeatures();
      this.currentAdaptation.reducedFunctionality = true;
    } else if (performanceScore < 75) {
      // Performance moyenne - optimiser
      this.optimizeFeatures();
    } else {
      // Performance élevée - activer toutes les fonctionnalités
      this.enableAllFeatures();
    }
  }

  /**
   * Calculer le score de performance
   */
  private calculatePerformanceScore(): number {
    let score = 100;
    
    // Pénalité pour mémoire faible
    if (this.deviceCapabilities.memoryGB <= 2) score -= 30;
    else if (this.deviceCapabilities.memoryGB <= 4) score -= 15;
    
    // Pénalité pour CPU faible
    if (this.deviceCapabilities.cpuCores <= 2) score -= 25;
    else if (this.deviceCapabilities.cpuCores <= 4) score -= 10;
    
    // Pénalité pour connexion lente
    if (this.deviceCapabilities.connectionSpeed === 'slow') score -= 25;
    else if (this.deviceCapabilities.connectionSpeed === 'medium') score -= 10;
    
    return Math.max(0, score);
  }

  /**
   * Optimiser pour le tactile
   */
  async optimizeForTouch(): Promise<void> {
    if (!this.deviceCapabilities.touchSupport) return;

    // Augmenter la taille des zones tactiles
    this.increaseTouchTargets();
    
    // Ajouter les gestes tactiles
    this.addTouchGestures();
    
    // Optimiser les interactions tactiles
    this.optimizeTouchInteractions();
    
    this.currentAdaptation.touchOptimized = true;
  }

  /**
   * Améliorer l'accessibilité
   */
  async enhanceAccessibility(): Promise<void> {
    if (!this.config.enableAccessibilityAdaptation) return;

    // Améliorer le contraste
    this.enhanceContrast();
    
    // Optimiser la navigation au clavier
    this.optimizeKeyboardNavigation();
    
    // Ajouter les descriptions ARIA
    this.addARIADescriptions();
    
    // Optimiser pour les lecteurs d'écran
    this.optimizeForScreenReaders();
    
    this.currentAdaptation.accessibilityEnhanced = true;
  }

  /**
   * Appliquer l'interface simplifiée
   */
  private applySimplifiedUI(): void {
    document.body.classList.add('simplified-ui');
    
    // Masquer les éléments non-essentiels
    const nonEssential = document.querySelectorAll('[data-non-essential]');
    nonEssential.forEach(el => (el as HTMLElement).style.display = 'none');
  }

  /**
   * Appliquer la mise en page compacte
   */
  private applyCompactLayout(): void {
    document.body.classList.add('compact-layout');
    
    // Réduire les espacements
    const style = document.createElement('style');
    style.textContent = `
      .compact-layout .container { padding: 0.5rem; }
      .compact-layout .card { margin: 0.25rem; }
      .compact-layout .btn { padding: 0.375rem 0.75rem; }
    `;
    document.head.appendChild(style);
  }

  /**
   * Optimiser les images
   */
  private optimizeImages(): void {
    const images = document.querySelectorAll('img');
    images.forEach(img => {
      // Utiliser des images de qualité réduite
      const src = img.src;
      if (src && !src.includes('_optimized')) {
        img.src = src.replace(/\.(jpg|jpeg|png)$/i, '_optimized.$1');
      }
    });
  }

  /**
   * Réduire les animations
   */
  private reduceAnimations(): void {
    const style = document.createElement('style');
    style.textContent = `
      *, *::before, *::after {
        animation-duration: 0.1s !important;
        transition-duration: 0.1s !important;
      }
    `;
    style.setAttribute('data-adaptive-ux', 'reduced-animations');
    document.head.appendChild(style);
  }

  /**
   * Démarrer le monitoring adaptatif
   */
  private startAdaptiveMonitoring(): void {
    // Écouter les changements d'orientation
    window.addEventListener('orientationchange', () => {
      setTimeout(() => this.handleOrientationChange(), 100);
    });

    // Écouter les changements de taille d'écran
    window.addEventListener('resize', () => {
      this.handleScreenSizeChange();
    });

    // Écouter les changements de connexion
    if ('connection' in navigator) {
      (navigator as any).connection.addEventListener('change', () => {
        this.handleConnectionChange();
      });
    }
  }

  /**
   * Gérer le changement d'orientation
   */
  private handleOrientationChange(): void {
    this.deviceCapabilities.screenSize = this.detectScreenSize();
    this.renderAdaptiveInterface();
  }

  /**
   * Gérer le changement de taille d'écran
   */
  private handleScreenSizeChange(): void {
    const newScreenSize = this.detectScreenSize();
    if (newScreenSize !== this.deviceCapabilities.screenSize) {
      this.deviceCapabilities.screenSize = newScreenSize;
      this.renderAdaptiveInterface();
    }
  }

  /**
   * Gérer le changement de connexion
   */
  private handleConnectionChange(): void {
    this.detectConnectionCapabilities();
    this.renderAdaptiveInterface();
  }

  /**
   * Ajouter un observateur
   */
  addObserver(name: string, callback: (adaptation: UXAdaptation) => void): void {
    this.observers.set(name, callback);
  }

  /**
   * Supprimer un observateur
   */
  removeObserver(name: string): void {
    this.observers.delete(name);
  }

  /**
   * Notifier les observateurs
   */
  private notifyObservers(): void {
    this.observers.forEach(callback => callback(this.currentAdaptation));
  }

  /**
   * Obtenir l'adaptation actuelle
   */
  getCurrentAdaptation(): UXAdaptation {
    return { ...this.currentAdaptation };
  }

  /**
   * Obtenir les capacités de l'appareil
   */
  getDeviceCapabilities(): DeviceCapabilities {
    return { ...this.deviceCapabilities };
  }

  // Méthodes utilitaires (implémentation simplifiée)
  private disableHeavyFeatures(): void {
    // Désactiver les fonctionnalités lourdes
  }

  private optimizeFeatures(): void {
    // Optimiser les fonctionnalités
  }

  private enableAllFeatures(): void {
    // Activer toutes les fonctionnalités
  }

  private increaseTouchTargets(): void {
    // Augmenter la taille des zones tactiles
  }

  private addTouchGestures(): void {
    // Ajouter les gestes tactiles
  }

  private optimizeTouchInteractions(): void {
    // Optimiser les interactions tactiles
  }

  private enhanceContrast(): void {
    // Améliorer le contraste
  }

  private optimizeKeyboardNavigation(): void {
    // Optimiser la navigation au clavier
  }

  private addARIADescriptions(): void {
    // Ajouter les descriptions ARIA
  }

  private optimizeForScreenReaders(): void {
    // Optimiser pour les lecteurs d'écran
  }
}
