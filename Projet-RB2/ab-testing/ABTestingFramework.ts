/**
 * A/B Testing Framework - Sprint 16 Jour 2
 * Framework automatisé pour tests A/B avec significance statistique
 */

export interface ABTestConfig {
  name: string;
  description: string;
  hypothesis: string;
  variants: ABTestVariant[];
  trafficAllocation: Record<string, number>;
  targetMetrics: TargetMetric[];
  duration: number; // en millisecondes
  minSampleSize: number;
  significanceLevel: number; // alpha (ex: 0.05)
  power: number; // 1 - beta (ex: 0.8)
  segmentation?: SegmentationRule[];
  exclusionRules?: ExclusionRule[];
}

export interface ABTestVariant {
  id: string;
  name: string;
  description: string;
  config: Record<string, any>;
  isControl: boolean;
  weight: number; // 0-1
}

export interface TargetMetric {
  name: string;
  type: 'conversion' | 'revenue' | 'engagement' | 'retention' | 'custom';
  aggregation: 'sum' | 'avg' | 'count' | 'rate';
  goal: 'increase' | 'decrease';
  minimumDetectableEffect: number; // MDE en %
  baseline?: number;
}

export interface SegmentationRule {
  field: string;
  operator: 'equals' | 'contains' | 'greater_than' | 'less_than' | 'in';
  value: any;
}

export interface ExclusionRule {
  field: string;
  operator: 'equals' | 'contains' | 'greater_than' | 'less_than' | 'in';
  value: any;
  reason: string;
}

export interface ABTestResult {
  testId: string;
  status: 'running' | 'completed' | 'stopped' | 'failed';
  startDate: Date;
  endDate?: Date;
  participants: number;
  variants: VariantResult[];
  overallSignificance: boolean;
  recommendations: string[];
  confidence: number;
  statisticalPower: number;
  summary: TestSummary;
}

export interface VariantResult {
  variantId: string;
  participants: number;
  metrics: MetricResult[];
  conversionRate?: number;
  revenue?: number;
  engagement?: number;
  confidence: number;
  isWinner?: boolean;
  liftOverControl?: number;
}

export interface MetricResult {
  name: string;
  value: number;
  variance: number;
  confidenceInterval: [number, number];
  pValue: number;
  isSignificant: boolean;
  effect: number; // Cohen's d ou autre mesure d'effet
}

export interface TestSummary {
  winner?: string;
  confidence: number;
  expectedLift: number;
  businessImpact: string;
  nextSteps: string[];
  learnings: string[];
}

export interface UserAssignment {
  userId: string;
  testId: string;
  variantId: string;
  assignedAt: Date;
  segment?: string;
}

export class ABTestingFramework {
  private activeTests: Map<string, ABTestConfig> = new Map();
  private userAssignments: Map<string, UserAssignment[]> = new Map();
  private testResults: Map<string, ABTestResult> = new Map();
  private eventCollector: any;
  private statisticalEngine: StatisticalEngine;
  private config: any;

  constructor(config: any = {}) {
    this.config = {
      defaultSignificanceLevel: 0.05,
      defaultPower: 0.8,
      defaultMinSampleSize: 1000,
      maxTestDuration: 30 * 24 * 60 * 60 * 1000, // 30 jours
      enableAutoStop: true,
      enableBayesian: false,
      debugMode: false,
      ...config
    };

    this.statisticalEngine = new StatisticalEngine(this.config);
    this.initialize();
  }

  /**
   * Initialisation du framework
   */
  private async initialize() {
    this.debug('Initializing A/B Testing Framework...');

    // Charger les tests actifs
    await this.loadActiveTests();

    // Configurer la collecte d'événements
    this.setupEventCollection();

    // Démarrer le monitoring automatique
    this.startAutomaticMonitoring();

    this.debug('A/B Testing Framework initialized successfully');
  }

  /**
   * Créer et démarrer un nouveau test A/B
   */
  public async createTest(config: ABTestConfig): Promise<string> {
    const testId = this.generateTestId();

    try {
      // Valider la configuration
      this.validateTestConfig(config);

      // Calculer la taille d'échantillon requise
      const requiredSampleSize = this.calculateSampleSize(config);
      if (requiredSampleSize > config.minSampleSize) {
        config.minSampleSize = requiredSampleSize;
      }

      // Enregistrer le test
      this.activeTests.set(testId, config);

      // Initialiser les résultats
      const initialResult: ABTestResult = {
        testId,
        status: 'running',
        startDate: new Date(),
        participants: 0,
        variants: config.variants.map(v => ({
          variantId: v.id,
          participants: 0,
          metrics: config.targetMetrics.map(m => ({
            name: m.name,
            value: 0,
            variance: 0,
            confidenceInterval: [0, 0],
            pValue: 1,
            isSignificant: false,
            effect: 0
          })),
          confidence: 0
        })),
        overallSignificance: false,
        recommendations: [],
        confidence: 0,
        statisticalPower: 0,
        summary: {
          confidence: 0,
          expectedLift: 0,
          businessImpact: '',
          nextSteps: [],
          learnings: []
        }
      };

      this.testResults.set(testId, initialResult);

      // Programmer l'arrêt automatique
      if (config.duration > 0) {
        setTimeout(() => {
          this.stopTest(testId, 'duration_reached');
        }, config.duration);
      }

      this.debug(`A/B Test created: ${testId} - ${config.name}`);
      return testId;

    } catch (error) {
      this.debug(`Failed to create A/B test:`, error);
      throw error;
    }
  }

  /**
   * Assigner un utilisateur à une variante
   */
  public assignUser(userId: string, testId: string, context?: any): string | null {
    const test = this.activeTests.get(testId);
    if (!test) {
      this.debug(`Test ${testId} not found`);
      return null;
    }

    // Vérifier les règles d'exclusion
    if (this.isUserExcluded(userId, test, context)) {
      return null;
    }

    // Vérifier si l'utilisateur est déjà assigné
    const existingAssignment = this.getUserAssignment(userId, testId);
    if (existingAssignment) {
      return existingAssignment.variantId;
    }

    // Déterminer le segment utilisateur
    const segment = this.determineUserSegment(userId, test, context);

    // Assigner à une variante
    const variantId = this.assignToVariant(userId, test, segment);

    // Enregistrer l'assignation
    const assignment: UserAssignment = {
      userId,
      testId,
      variantId,
      assignedAt: new Date(),
      segment
    };

    if (!this.userAssignments.has(userId)) {
      this.userAssignments.set(userId, []);
    }
    this.userAssignments.get(userId)!.push(assignment);

    // Mettre à jour les statistiques
    this.updateParticipantCount(testId, variantId);

    this.debug(`User ${userId} assigned to variant ${variantId} in test ${testId}`);
    return variantId;
  }

  /**
   * Enregistrer un événement de conversion
   */
  public trackConversion(
    userId: string,
    testId: string,
    metricName: string,
    value: number = 1,
    metadata?: any
  ) {
    const assignment = this.getUserAssignment(userId, testId);
    if (!assignment) {
      this.debug(`No assignment found for user ${userId} in test ${testId}`);
      return;
    }

    const test = this.activeTests.get(testId);
    if (!test || this.testResults.get(testId)?.status !== 'running') {
      return;
    }

    // Enregistrer l'événement
    this.recordEvent(testId, assignment.variantId, metricName, value, metadata);

    // Mettre à jour les résultats en temps réel
    this.updateTestResults(testId);

    // Vérifier si le test peut être arrêté automatiquement
    if (this.config.enableAutoStop) {
      this.checkAutoStop(testId);
    }

    this.debug(`Conversion tracked: ${metricName} = ${value} for user ${userId}`);
  }

  /**
   * Obtenir les résultats d'un test
   */
  public getTestResults(testId: string): ABTestResult | null {
    const result = this.testResults.get(testId);
    if (!result) {
      return null;
    }

    // Recalculer les statistiques en temps réel
    this.updateTestResults(testId);

    return this.testResults.get(testId) || null;
  }

  /**
   * Arrêter un test manuellement
   */
  public async stopTest(testId: string, reason: string = 'manual'): Promise<void> {
    const test = this.activeTests.get(testId);
    const result = this.testResults.get(testId);

    if (!test || !result) {
      throw new Error(`Test ${testId} not found`);
    }

    if (result.status !== 'running') {
      throw new Error(`Test ${testId} is not running`);
    }

    // Mettre à jour le statut
    result.status = 'completed';
    result.endDate = new Date();

    // Calculer les résultats finaux
    await this.calculateFinalResults(testId);

    // Générer les recommandations
    result.recommendations = this.generateRecommendations(testId);

    // Générer le résumé
    result.summary = this.generateTestSummary(testId);

    this.debug(`Test ${testId} stopped: ${reason}`);
  }

  /**
   * Calculer la taille d'échantillon requise
   */
  private calculateSampleSize(config: ABTestConfig): number {
    const primaryMetric = config.targetMetrics[0];
    const alpha = config.significanceLevel || this.config.defaultSignificanceLevel;
    const power = config.power || this.config.defaultPower;
    const mde = primaryMetric.minimumDetectableEffect / 100;

    // Formule pour test de proportion (conversion)
    if (primaryMetric.type === 'conversion') {
      const p = primaryMetric.baseline || 0.1; // Taux de base
      const z_alpha = this.getZScore(alpha / 2);
      const z_beta = this.getZScore(1 - power);

      const numerator = Math.pow(z_alpha + z_beta, 2) * 2 * p * (1 - p);
      const denominator = Math.pow(mde, 2);

      return Math.ceil(numerator / denominator);
    }

    // Formule pour test de moyenne (revenue, engagement)
    else {
      const z_alpha = this.getZScore(alpha / 2);
      const z_beta = this.getZScore(1 - power);
      const sigma = 1; // Écart-type normalisé

      const numerator = Math.pow(z_alpha + z_beta, 2) * 2 * Math.pow(sigma, 2);
      const denominator = Math.pow(mde, 2);

      return Math.ceil(numerator / denominator);
    }
  }

  /**
   * Assigner un utilisateur à une variante
   */
  private assignToVariant(userId: string, test: ABTestConfig, segment?: string): string {
    // Utiliser un hash déterministe pour assurer la cohérence
    const hash = this.hashUserId(userId, test.name);
    const random = hash % 10000 / 10000; // 0-1

    let cumulativeWeight = 0;
    for (const variant of test.variants) {
      cumulativeWeight += variant.weight;
      if (random < cumulativeWeight) {
        return variant.id;
      }
    }

    // Fallback vers la variante de contrôle
    return test.variants.find(v => v.isControl)?.id || test.variants[0].id;
  }

  /**
   * Mettre à jour les résultats du test
   */
  private updateTestResults(testId: string) {
    const test = this.activeTests.get(testId);
    const result = this.testResults.get(testId);

    if (!test || !result) return;

    // Recalculer les métriques pour chaque variante
    for (const variantResult of result.variants) {
      this.calculateVariantMetrics(testId, variantResult);
    }

    // Calculer la significance globale
    result.overallSignificance = this.calculateOverallSignificance(result);

    // Calculer la puissance statistique
    result.statisticalPower = this.calculateStatisticalPower(testId);

    // Déterminer le gagnant
    this.determineWinner(result);
  }

  /**
   * Vérification d'arrêt automatique
   */
  private checkAutoStop(testId: string) {
    const result = this.testResults.get(testId);
    if (!result) return;

    const test = this.activeTests.get(testId);
    if (!test) return;

    // Vérifier la taille d'échantillon minimale
    if (result.participants < test.minSampleSize) {
      return;
    }

    // Vérifier la significance
    if (result.overallSignificance && result.statisticalPower > 0.8) {
      this.stopTest(testId, 'significance_reached');
      return;
    }

    // Vérifier la durée maximale
    const runningTime = Date.now() - result.startDate.getTime();
    if (runningTime > this.config.maxTestDuration) {
      this.stopTest(testId, 'max_duration_reached');
      return;
    }
  }

  /**
   * Utilitaires
   */
  private generateTestId(): string {
    return `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private hashUserId(userId: string, testName: string): number {
    const str = `${userId}_${testName}`;
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  private getZScore(probability: number): number {
    // Approximation inverse de la fonction de répartition normale
    if (probability === 0.5) return 0;
    if (probability < 0.5) return -this.getZScore(1 - probability);

    const t = Math.sqrt(-2 * Math.log(1 - probability));
    return t - (2.515517 + 0.802853 * t + 0.010328 * t * t) /
           (1 + 1.432788 * t + 0.189269 * t * t + 0.001308 * t * t * t);
  }

  private debug(message: string, data?: any) {
    if (this.config.debugMode) {
      console.log(`[A/B Testing] ${message}`, data);
    }
  }

  // Méthodes stub pour les fonctionnalités avancées
  private validateTestConfig(config: ABTestConfig): void { /* Implementation */ }
  private async loadActiveTests(): Promise<void> { /* Implementation */ }
  private setupEventCollection(): void { /* Implementation */ }
  private startAutomaticMonitoring(): void { /* Implementation */ }
  private isUserExcluded(userId: string, test: ABTestConfig, context?: any): boolean { return false; }
  private getUserAssignment(userId: string, testId: string): UserAssignment | null { return null; }
  private determineUserSegment(userId: string, test: ABTestConfig, context?: any): string | undefined { return undefined; }
  private updateParticipantCount(testId: string, variantId: string): void { /* Implementation */ }
  private recordEvent(testId: string, variantId: string, metricName: string, value: number, metadata?: any): void { /* Implementation */ }
  private calculateVariantMetrics(testId: string, variantResult: VariantResult): void { /* Implementation */ }
  private calculateOverallSignificance(result: ABTestResult): boolean { return false; }
  private calculateStatisticalPower(testId: string): number { return 0.8; }
  private determineWinner(result: ABTestResult): void { /* Implementation */ }
  private async calculateFinalResults(testId: string): Promise<void> { /* Implementation */ }
  private generateRecommendations(testId: string): string[] { return []; }
  private generateTestSummary(testId: string): TestSummary { return {} as TestSummary; }

  /**
   * Nettoyage
   */
  public destroy() {
    this.activeTests.clear();
    this.userAssignments.clear();
    this.testResults.clear();
  }
}

/**
 * Moteur statistique pour les calculs A/B
 */
class StatisticalEngine {
  constructor(private config: any) {}

  // Méthodes pour calculs statistiques
  calculateTTest(sample1: number[], sample2: number[]): { tStat: number; pValue: number } {
    // Implementation du t-test
    return { tStat: 0, pValue: 0.5 };
  }

  calculateChiSquare(observed: number[], expected: number[]): { chiStat: number; pValue: number } {
    // Implementation du test du chi-carré
    return { chiStat: 0, pValue: 0.5 };
  }

  calculateBayesianProbability(priorA: number, priorB: number, dataA: number[], dataB: number[]): number {
    // Implementation Bayésienne
    return 0.5;
  }
}

export default ABTestingFramework;
