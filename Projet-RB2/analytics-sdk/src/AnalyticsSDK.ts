/**
 * Analytics SDK Unifié - Sprint 16
 * SDK pour tracking cross-services avec IA prédictive
 */

import { v4 as uuidv4 } from 'uuid';

export interface AnalyticsConfig {
  apiUrl: string;
  apiKey: string;
  service: string;
  userId?: string;
  sessionId?: string;
  enableAutoTracking?: boolean;
  enablePerformanceTracking?: boolean;
  enableErrorTracking?: boolean;
  enablePredictiveAnalytics?: boolean;
  batchSize?: number;
  flushInterval?: number;
  debug?: boolean;
}

export interface AnalyticsEvent {
  eventId: string;
  userId?: string;
  sessionId: string;
  eventType: string;
  eventName: string;
  timestamp: number;
  properties: Record<string, any>;
  context: EventContext;
}

export interface EventContext {
  page: {
    url: string;
    title: string;
    referrer: string;
    path: string;
    search: string;
  };
  user: {
    id?: string;
    anonymousId: string;
    traits?: Record<string, any>;
  };
  device: {
    type: 'desktop' | 'mobile' | 'tablet';
    browser: string;
    os: string;
    screenResolution: string;
    viewport: string;
  };
  session: {
    id: string;
    startTime: number;
    pageViews: number;
    events: number;
  };
  service: {
    name: string;
    version: string;
  };
  location?: {
    country?: string;
    city?: string;
    timezone: string;
  };
}

export interface PerformanceMetric {
  metricId: string;
  name: string;
  value: number;
  unit: string;
  timestamp: number;
  context: EventContext;
}

export interface PredictionRequest {
  userId: string;
  sessionId: string;
  currentPage: string;
  userBehavior: UserBehavior;
  contextData: Record<string, any>;
}

export interface UserBehavior {
  pageViews: number;
  timeOnSite: number;
  interactions: number;
  scrollDepth: number;
  clickPattern: string[];
  deviceType: string;
  trafficSource: string;
}

export interface PredictionResponse {
  nextPageProbability: Record<string, number>;
  conversionProbability: number;
  churnRisk: number;
  recommendedActions: string[];
  personalizedContent: Record<string, any>;
}

export class AnalyticsSDK {
  private config: AnalyticsConfig;
  private eventQueue: AnalyticsEvent[] = [];
  private performanceQueue: PerformanceMetric[] = [];
  private sessionData: any = {};
  private flushTimer?: NodeJS.Timeout;
  private isInitialized = false;

  constructor(config: AnalyticsConfig) {
    this.config = {
      batchSize: 50,
      flushInterval: 5000,
      enableAutoTracking: true,
      enablePerformanceTracking: true,
      enableErrorTracking: true,
      enablePredictiveAnalytics: true,
      debug: false,
      ...config
    };

    this.initialize();
  }

  /**
   * Initialisation du SDK
   */
  private async initialize() {
    try {
      // Générer session ID si non fourni
      if (!this.config.sessionId) {
        this.config.sessionId = this.generateSessionId();
      }

      // Initialiser les données de session
      this.sessionData = {
        id: this.config.sessionId,
        startTime: Date.now(),
        pageViews: 0,
        events: 0,
        lastActivity: Date.now()
      };

      // Démarrer le timer de flush
      this.startFlushTimer();

      // Activer le tracking automatique
      if (this.config.enableAutoTracking) {
        this.enableAutoTracking();
      }

      // Activer le tracking de performance
      if (this.config.enablePerformanceTracking) {
        this.enablePerformanceTracking();
      }

      // Activer le tracking d'erreurs
      if (this.config.enableErrorTracking) {
        this.enableErrorTracking();
      }

      this.isInitialized = true;
      this.debug('Analytics SDK initialized', this.config);

      // Envoyer événement d'initialisation
      this.track('sdk_initialized', {
        service: this.config.service,
        version: '1.0.0',
        features: {
          autoTracking: this.config.enableAutoTracking,
          performanceTracking: this.config.enablePerformanceTracking,
          errorTracking: this.config.enableErrorTracking,
          predictiveAnalytics: this.config.enablePredictiveAnalytics
        }
      });

    } catch (error) {
      console.error('Failed to initialize Analytics SDK:', error);
    }
  }

  /**
   * Tracking d'événement principal
   */
  public track(eventName: string, properties: Record<string, any> = {}) {
    if (!this.isInitialized) {
      console.warn('Analytics SDK not initialized');
      return;
    }

    const event: AnalyticsEvent = {
      eventId: uuidv4(),
      userId: this.config.userId,
      sessionId: this.config.sessionId!,
      eventType: 'track',
      eventName,
      timestamp: Date.now(),
      properties,
      context: this.getEventContext()
    };

    this.addToQueue(event);
    this.updateSessionData();
    this.debug('Event tracked:', event);

    // Prédictions en temps réel si activées
    if (this.config.enablePredictiveAnalytics) {
      this.requestPredictions(event);
    }
  }

  /**
   * Tracking de page vue
   */
  public page(pageName?: string, properties: Record<string, any> = {}) {
    const pageData = {
      name: pageName || document.title,
      url: window.location.href,
      path: window.location.pathname,
      search: window.location.search,
      referrer: document.referrer,
      ...properties
    };

    this.track('page_view', pageData);
    this.sessionData.pageViews++;
  }

  /**
   * Identification utilisateur
   */
  public identify(userId: string, traits: Record<string, any> = {}) {
    this.config.userId = userId;
    
    this.track('user_identified', {
      userId,
      traits,
      previousAnonymousId: this.getAnonymousId()
    });
  }

  /**
   * Tracking de conversion
   */
  public conversion(conversionType: string, value?: number, properties: Record<string, any> = {}) {
    this.track('conversion', {
      conversionType,
      value,
      currency: 'EUR',
      ...properties
    });
  }

  /**
   * Tracking d'erreur
   */
  public error(error: Error, context: Record<string, any> = {}) {
    this.track('error', {
      errorMessage: error.message,
      errorStack: error.stack,
      errorName: error.name,
      context,
      url: window.location.href,
      userAgent: navigator.userAgent
    });
  }

  /**
   * Demande de prédictions IA
   */
  public async requestPredictions(event?: AnalyticsEvent): Promise<PredictionResponse | null> {
    try {
      const request: PredictionRequest = {
        userId: this.config.userId || this.getAnonymousId(),
        sessionId: this.config.sessionId!,
        currentPage: window.location.pathname,
        userBehavior: this.getUserBehavior(),
        contextData: event ? event.properties : {}
      };

      const response = await fetch(`${this.config.apiUrl}/predictions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`
        },
        body: JSON.stringify(request)
      });

      if (response.ok) {
        const predictions: PredictionResponse = await response.json();
        this.debug('Predictions received:', predictions);
        
        // Déclencher événement de prédictions reçues
        this.track('predictions_received', {
          conversionProbability: predictions.conversionProbability,
          churnRisk: predictions.churnRisk,
          recommendedActionsCount: predictions.recommendedActions.length
        });

        return predictions;
      }
    } catch (error) {
      this.debug('Failed to get predictions:', error);
    }

    return null;
  }

  /**
   * Activation du tracking automatique
   */
  private enableAutoTracking() {
    // Tracking des clics
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      const tagName = target.tagName.toLowerCase();
      
      if (tagName === 'button' || tagName === 'a' || target.getAttribute('role') === 'button') {
        this.track('click', {
          elementType: tagName,
          elementText: target.textContent?.trim(),
          elementId: target.id,
          elementClass: target.className,
          href: target.getAttribute('href'),
          x: event.clientX,
          y: event.clientY
        });
      }
    });

    // Tracking du scroll
    let scrollDepth = 0;
    window.addEventListener('scroll', () => {
      const currentDepth = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
      
      if (currentDepth > scrollDepth && currentDepth % 25 === 0) {
        scrollDepth = currentDepth;
        this.track('scroll', { depth: scrollDepth });
      }
    });

    // Tracking de la visibilité de la page
    document.addEventListener('visibilitychange', () => {
      this.track('page_visibility', {
        hidden: document.hidden,
        visibilityState: document.visibilityState
      });
    });

    // Tracking de la fermeture de page
    window.addEventListener('beforeunload', () => {
      this.track('page_unload', {
        timeOnPage: Date.now() - this.sessionData.startTime,
        pageViews: this.sessionData.pageViews,
        events: this.sessionData.events
      });
      this.flush();
    });
  }

  /**
   * Activation du tracking de performance
   */
  private enablePerformanceTracking() {
    // Core Web Vitals
    this.trackWebVitals();

    // Performance Navigation
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        
        this.trackPerformance('page_load_time', navigation.loadEventEnd - navigation.loadEventStart, 'ms');
        this.trackPerformance('dom_content_loaded', navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart, 'ms');
        this.trackPerformance('first_byte', navigation.responseStart - navigation.requestStart, 'ms');
      }, 0);
    });
  }

  /**
   * Tracking des Core Web Vitals
   */
  private trackWebVitals() {
    // First Contentful Paint
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
      if (fcpEntry) {
        this.trackPerformance('first_contentful_paint', fcpEntry.startTime, 'ms');
      }
    }).observe({ entryTypes: ['paint'] });

    // Largest Contentful Paint
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      if (lastEntry) {
        this.trackPerformance('largest_contentful_paint', lastEntry.startTime, 'ms');
      }
    }).observe({ entryTypes: ['largest-contentful-paint'] });

    // Cumulative Layout Shift
    let clsValue = 0;
    new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value;
        }
      }
      this.trackPerformance('cumulative_layout_shift', clsValue, 'score');
    }).observe({ entryTypes: ['layout-shift'] });
  }

  /**
   * Activation du tracking d'erreurs
   */
  private enableErrorTracking() {
    window.addEventListener('error', (event) => {
      this.error(new Error(event.message), {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      });
    });

    window.addEventListener('unhandledrejection', (event) => {
      this.error(new Error(event.reason), {
        type: 'unhandled_promise_rejection'
      });
    });
  }

  /**
   * Tracking de métrique de performance
   */
  private trackPerformance(name: string, value: number, unit: string) {
    const metric: PerformanceMetric = {
      metricId: uuidv4(),
      name,
      value,
      unit,
      timestamp: Date.now(),
      context: this.getEventContext()
    };

    this.performanceQueue.push(metric);
    this.debug('Performance metric tracked:', metric);
  }

  /**
   * Obtenir le contexte d'événement
   */
  private getEventContext(): EventContext {
    return {
      page: {
        url: window.location.href,
        title: document.title,
        referrer: document.referrer,
        path: window.location.pathname,
        search: window.location.search
      },
      user: {
        id: this.config.userId,
        anonymousId: this.getAnonymousId()
      },
      device: {
        type: this.getDeviceType(),
        browser: this.getBrowser(),
        os: this.getOS(),
        screenResolution: `${screen.width}x${screen.height}`,
        viewport: `${window.innerWidth}x${window.innerHeight}`
      },
      session: {
        id: this.config.sessionId!,
        startTime: this.sessionData.startTime,
        pageViews: this.sessionData.pageViews,
        events: this.sessionData.events
      },
      service: {
        name: this.config.service,
        version: '1.0.0'
      },
      location: {
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
      }
    };
  }

  /**
   * Obtenir le comportement utilisateur pour les prédictions
   */
  private getUserBehavior(): UserBehavior {
    return {
      pageViews: this.sessionData.pageViews,
      timeOnSite: Date.now() - this.sessionData.startTime,
      interactions: this.sessionData.events,
      scrollDepth: 0, // À implémenter
      clickPattern: [], // À implémenter
      deviceType: this.getDeviceType(),
      trafficSource: document.referrer || 'direct'
    };
  }

  /**
   * Utilitaires
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getAnonymousId(): string {
    let anonymousId = localStorage.getItem('analytics_anonymous_id');
    if (!anonymousId) {
      anonymousId = `anon_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      localStorage.setItem('analytics_anonymous_id', anonymousId);
    }
    return anonymousId;
  }

  private getDeviceType(): 'desktop' | 'mobile' | 'tablet' {
    const userAgent = navigator.userAgent;
    if (/tablet|ipad|playbook|silk/i.test(userAgent)) return 'tablet';
    if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/i.test(userAgent)) return 'mobile';
    return 'desktop';
  }

  private getBrowser(): string {
    const userAgent = navigator.userAgent;
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    return 'Unknown';
  }

  private getOS(): string {
    const userAgent = navigator.userAgent;
    if (userAgent.includes('Windows')) return 'Windows';
    if (userAgent.includes('Mac')) return 'macOS';
    if (userAgent.includes('Linux')) return 'Linux';
    if (userAgent.includes('Android')) return 'Android';
    if (userAgent.includes('iOS')) return 'iOS';
    return 'Unknown';
  }

  private addToQueue(event: AnalyticsEvent) {
    this.eventQueue.push(event);
    
    if (this.eventQueue.length >= this.config.batchSize!) {
      this.flush();
    }
  }

  private updateSessionData() {
    this.sessionData.events++;
    this.sessionData.lastActivity = Date.now();
  }

  private startFlushTimer() {
    this.flushTimer = setInterval(() => {
      this.flush();
    }, this.config.flushInterval);
  }

  /**
   * Envoi des événements en batch
   */
  public async flush() {
    if (this.eventQueue.length === 0 && this.performanceQueue.length === 0) {
      return;
    }

    try {
      const payload = {
        events: [...this.eventQueue],
        performance: [...this.performanceQueue],
        timestamp: Date.now()
      };

      const response = await fetch(`${this.config.apiUrl}/events`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`
        },
        body: JSON.stringify(payload)
      });

      if (response.ok) {
        this.debug(`Flushed ${this.eventQueue.length} events and ${this.performanceQueue.length} metrics`);
        this.eventQueue = [];
        this.performanceQueue = [];
      } else {
        console.error('Failed to send analytics data:', response.statusText);
      }
    } catch (error) {
      console.error('Failed to flush analytics data:', error);
    }
  }

  private debug(message: string, data?: any) {
    if (this.config.debug) {
      console.log(`[Analytics SDK] ${message}`, data);
    }
  }

  /**
   * Nettoyage
   */
  public destroy() {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }
    this.flush();
    this.isInitialized = false;
  }
}

// Export par défaut
export default AnalyticsSDK;
