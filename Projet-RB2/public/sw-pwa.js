/**
 * PWA Service Worker - Sprint 17 Jour 1
 * Service Worker avancé pour PWA avec offline-first et performance mobile optimisée
 */

const CACHE_VERSION = 'pwa-v1.0.0';
const CACHE_NAMES = {
  static: `pwa-static-${CACHE_VERSION}`,
  dynamic: `pwa-dynamic-${CACHE_VERSION}`,
  api: `pwa-api-${CACHE_VERSION}`,
  images: `pwa-images-${CACHE_VERSION}`,
  offline: `pwa-offline-${CACHE_VERSION}`
};

// Configuration par défaut
let config = {
  offlineStrategy: {
    enableOfflineMode: true,
    offlinePages: ['/', '/search', '/bookings', '/profile'],
    offlineAssets: [
      '/static/js/main.js',
      '/static/css/main.css',
      '/icons/icon-192x192.png',
      '/offline.html'
    ],
    fallbackStrategy: 'offline-page'
  },
  updateStrategy: {
    autoUpdate: true,
    promptUser: true,
    forceUpdate: false
  }
};

// URLs critiques à mettre en cache immédiatement
const CRITICAL_URLS = [
  '/',
  '/static/css/critical.css',
  '/static/js/critical.js',
  '/icons/icon-192x192.png',
  '/offline.html'
];

// Patterns d'URLs pour les stratégies de cache
const CACHE_STRATEGIES = {
  // API: Network First avec fallback cache
  api: {
    pattern: /^https?:\/\/.*\/api\//,
    strategy: 'NetworkFirst',
    cacheName: CACHE_NAMES.api,
    maxAge: 5 * 60 * 1000, // 5 minutes
    maxEntries: 100
  },
  
  // Assets statiques: Cache First
  static: {
    pattern: /\.(js|css|woff2|woff|ttf|eot)$/,
    strategy: 'CacheFirst',
    cacheName: CACHE_NAMES.static,
    maxAge: 365 * 24 * 60 * 60 * 1000, // 1 an
    maxEntries: 200
  },
  
  // Images: Cache First avec compression
  images: {
    pattern: /\.(png|jpg|jpeg|gif|webp|avif|svg)$/,
    strategy: 'CacheFirst',
    cacheName: CACHE_NAMES.images,
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30 jours
    maxEntries: 300
  },
  
  // Pages: Stale While Revalidate
  pages: {
    pattern: /^https?:\/\/.*\/(search|booking|profile|retreat)/,
    strategy: 'StaleWhileRevalidate',
    cacheName: CACHE_NAMES.dynamic,
    maxAge: 24 * 60 * 60 * 1000, // 1 jour
    maxEntries: 50
  }
};

/**
 * Installation du Service Worker
 */
self.addEventListener('install', event => {
  console.log('[PWA SW] Installing...');
  
  event.waitUntil(
    Promise.all([
      precacheStaticAssets(),
      self.skipWaiting()
    ])
  );
});

/**
 * Activation du Service Worker
 */
self.addEventListener('activate', event => {
  console.log('[PWA SW] Activating...');
  
  event.waitUntil(
    Promise.all([
      cleanupOldCaches(),
      self.clients.claim(),
      setupBackgroundSync()
    ])
  );
});

/**
 * Interception des requêtes
 */
self.addEventListener('fetch', event => {
  const request = event.request;
  
  // Ignorer les requêtes non-HTTP
  if (!request.url.startsWith('http')) {
    return;
  }

  // Ignorer les requêtes de navigation vers des URLs externes
  if (request.mode === 'navigate' && !request.url.startsWith(self.location.origin)) {
    return;
  }

  event.respondWith(handleRequest(request));
});

/**
 * Gestion des messages
 */
self.addEventListener('message', event => {
  const { type, data } = event.data;
  
  switch (type) {
    case 'CONFIGURE':
      config = { ...config, ...data };
      console.log('[PWA SW] Configuration updated:', config);
      break;
      
    case 'SKIP_WAITING':
      self.skipWaiting();
      break;
      
    case 'CACHE_URLS':
      cacheUrls(data.urls);
      break;
      
    case 'CLEAR_CACHE':
      clearCache(data.cacheType);
      break;
      
    case 'GET_CACHE_STATUS':
      getCacheStatus().then(status => {
        event.ports[0].postMessage(status);
      });
      break;
  }
});

/**
 * Notifications Push
 */
self.addEventListener('push', event => {
  if (!event.data) return;

  const data = event.data.json();
  const options = {
    body: data.body,
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    tag: data.tag || 'default',
    data: data.data || {},
    actions: data.actions || [],
    requireInteraction: data.requireInteraction || false,
    silent: data.silent || false,
    vibrate: data.vibrate || [200, 100, 200]
  };

  event.waitUntil(
    self.registration.showNotification(data.title, options)
  );
});

/**
 * Clic sur notification
 */
self.addEventListener('notificationclick', event => {
  event.notification.close();

  const action = event.action;
  const data = event.notification.data;

  if (action === 'open') {
    event.waitUntil(
      clients.openWindow(data.url || '/')
    );
  } else if (action === 'dismiss') {
    // Ne rien faire, notification fermée
  } else {
    // Clic sur la notification elle-même
    event.waitUntil(
      clients.matchAll({ type: 'window' }).then(clientList => {
        // Chercher une fenêtre ouverte
        for (const client of clientList) {
          if (client.url === data.url && 'focus' in client) {
            return client.focus();
          }
        }
        
        // Ouvrir une nouvelle fenêtre
        if (clients.openWindow) {
          return clients.openWindow(data.url || '/');
        }
      })
    );
  }
});

/**
 * Synchronisation en arrière-plan
 */
self.addEventListener('sync', event => {
  console.log('[PWA SW] Background sync:', event.tag);
  
  switch (event.tag) {
    case 'background-sync':
      event.waitUntil(doBackgroundSync());
      break;
    case 'analytics-sync':
      event.waitUntil(syncAnalytics());
      break;
    case 'offline-actions':
      event.waitUntil(syncOfflineActions());
      break;
  }
});

/**
 * Gestion des requêtes avec stratégies adaptatives
 */
async function handleRequest(request) {
  const url = new URL(request.url);
  
  // Déterminer la stratégie de cache
  const strategy = determineStrategy(request);
  
  try {
    switch (strategy.strategy) {
      case 'CacheFirst':
        return await cacheFirst(request, strategy);
      case 'NetworkFirst':
        return await networkFirst(request, strategy);
      case 'StaleWhileRevalidate':
        return await staleWhileRevalidate(request, strategy);
      case 'NetworkOnly':
        return await fetch(request);
      case 'CacheOnly':
        return await cacheOnly(request, strategy);
      default:
        return await staleWhileRevalidate(request, strategy);
    }
  } catch (error) {
    console.error('[PWA SW] Request failed:', error);
    return await handleOfflineRequest(request);
  }
}

/**
 * Stratégie Cache First
 */
async function cacheFirst(request, strategy) {
  const cache = await caches.open(strategy.cacheName);
  const cachedResponse = await cache.match(request);
  
  if (cachedResponse && await isCacheValid(cachedResponse, strategy.maxAge)) {
    return cachedResponse;
  }
  
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      // Optimiser les images avant mise en cache
      const responseToCache = await optimizeResponse(request, networkResponse.clone());
      await cache.put(request, responseToCache);
      await cleanupCache(strategy.cacheName, strategy.maxEntries);
    }
    
    return networkResponse;
  } catch (error) {
    if (cachedResponse) {
      return cachedResponse;
    }
    throw error;
  }
}

/**
 * Stratégie Network First
 */
async function networkFirst(request, strategy) {
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const cache = await caches.open(strategy.cacheName);
      await cache.put(request, networkResponse.clone());
      await cleanupCache(strategy.cacheName, strategy.maxEntries);
    }
    
    return networkResponse;
  } catch (error) {
    const cache = await caches.open(strategy.cacheName);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    throw error;
  }
}

/**
 * Stratégie Stale While Revalidate
 */
async function staleWhileRevalidate(request, strategy) {
  const cache = await caches.open(strategy.cacheName);
  const cachedResponse = await cache.match(request);
  
  // Démarrer la revalidation en arrière-plan
  const networkPromise = fetch(request).then(response => {
    if (response.ok) {
      cache.put(request, response.clone());
      cleanupCache(strategy.cacheName, strategy.maxEntries);
    }
    return response;
  }).catch(error => {
    console.error('[PWA SW] Background revalidation failed:', error);
  });
  
  // Retourner le cache immédiatement s'il existe
  if (cachedResponse) {
    return cachedResponse;
  }
  
  // Sinon attendre la réponse réseau
  return await networkPromise;
}

/**
 * Stratégie Cache Only
 */
async function cacheOnly(request, strategy) {
  const cache = await caches.open(strategy.cacheName);
  const cachedResponse = await cache.match(request);
  
  if (cachedResponse) {
    return cachedResponse;
  }
  
  throw new Error('No cached response available');
}

/**
 * Déterminer la stratégie de cache
 */
function determineStrategy(request) {
  const url = new URL(request.url);
  
  // Vérifier chaque pattern de stratégie
  for (const [name, strategy] of Object.entries(CACHE_STRATEGIES)) {
    if (strategy.pattern.test(request.url)) {
      return strategy;
    }
  }
  
  // Stratégie par défaut pour les navigations
  if (request.mode === 'navigate') {
    return {
      strategy: 'StaleWhileRevalidate',
      cacheName: CACHE_NAMES.dynamic,
      maxAge: 24 * 60 * 60 * 1000,
      maxEntries: 50
    };
  }
  
  // Stratégie par défaut
  return {
    strategy: 'NetworkFirst',
    cacheName: CACHE_NAMES.dynamic,
    maxAge: 60 * 60 * 1000,
    maxEntries: 100
  };
}

/**
 * Pré-cache des assets statiques
 */
async function precacheStaticAssets() {
  const cache = await caches.open(CACHE_NAMES.static);
  
  try {
    await cache.addAll(CRITICAL_URLS);
    console.log('[PWA SW] Critical assets cached');
    
    // Notifier que l'app est prête pour l'offline
    broadcastMessage('OFFLINE_READY');
  } catch (error) {
    console.error('[PWA SW] Failed to cache critical assets:', error);
  }
}

/**
 * Nettoyage des anciens caches
 */
async function cleanupOldCaches() {
  const cacheNames = await caches.keys();
  const currentCaches = Object.values(CACHE_NAMES);
  
  const deletePromises = cacheNames
    .filter(cacheName => !currentCaches.includes(cacheName))
    .map(cacheName => caches.delete(cacheName));
  
  await Promise.all(deletePromises);
  console.log('[PWA SW] Old caches cleaned up');
}

/**
 * Nettoyage d'un cache spécifique
 */
async function cleanupCache(cacheName, maxEntries = 100) {
  const cache = await caches.open(cacheName);
  const keys = await cache.keys();
  
  if (keys.length > maxEntries) {
    const entriesToDelete = keys.slice(0, keys.length - maxEntries);
    await Promise.all(entriesToDelete.map(key => cache.delete(key)));
  }
}

/**
 * Vérifier la validité du cache
 */
async function isCacheValid(response, maxAge) {
  const dateHeader = response.headers.get('date');
  if (!dateHeader) return false;
  
  const cacheDate = new Date(dateHeader);
  const now = new Date();
  
  return (now.getTime() - cacheDate.getTime()) < maxAge;
}

/**
 * Optimiser la réponse avant mise en cache
 */
async function optimizeResponse(request, response) {
  // Pour les images, on pourrait appliquer une compression
  if (request.url.match(/\.(png|jpg|jpeg)$/i)) {
    // Ici on pourrait utiliser une bibliothèque de compression d'images
    // Pour l'instant, on retourne la réponse originale
    return response;
  }
  
  return response;
}

/**
 * Gérer les requêtes hors ligne
 */
async function handleOfflineRequest(request) {
  // Pour les navigations, servir la page offline
  if (request.mode === 'navigate') {
    const cache = await caches.open(CACHE_NAMES.offline);
    const offlinePage = await cache.match('/offline.html');
    
    if (offlinePage) {
      return offlinePage;
    }
  }
  
  // Pour les API, retourner une réponse JSON d'erreur
  if (request.url.includes('/api/')) {
    return new Response(
      JSON.stringify({
        error: 'Offline',
        message: 'Cette fonctionnalité nécessite une connexion internet'
      }),
      {
        status: 503,
        statusText: 'Service Unavailable',
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
  
  // Réponse générique
  return new Response('Offline', {
    status: 503,
    statusText: 'Service Unavailable'
  });
}

/**
 * Configuration de la synchronisation en arrière-plan
 */
async function setupBackgroundSync() {
  // Enregistrer les tags de synchronisation
  const syncTags = ['background-sync', 'analytics-sync', 'offline-actions'];
  
  for (const tag of syncTags) {
    try {
      await self.registration.sync.register(tag);
    } catch (error) {
      console.error(`[PWA SW] Failed to register sync tag ${tag}:`, error);
    }
  }
}

/**
 * Synchronisation en arrière-plan
 */
async function doBackgroundSync() {
  console.log('[PWA SW] Performing background sync...');
  
  try {
    // Synchroniser les données en attente
    await syncPendingData();
    
    // Mettre à jour le cache
    await updateCache();
    
    console.log('[PWA SW] Background sync completed');
    broadcastMessage('BACKGROUND_SYNC', { success: true });
  } catch (error) {
    console.error('[PWA SW] Background sync failed:', error);
    broadcastMessage('BACKGROUND_SYNC', { success: false, error: error.message });
  }
}

/**
 * Synchroniser les analytics
 */
async function syncAnalytics() {
  // Récupérer les événements analytics en attente depuis IndexedDB
  // et les envoyer au serveur
  console.log('[PWA SW] Syncing analytics...');
}

/**
 * Synchroniser les actions offline
 */
async function syncOfflineActions() {
  // Traiter les actions effectuées hors ligne
  console.log('[PWA SW] Syncing offline actions...');
}

/**
 * Synchroniser les données en attente
 */
async function syncPendingData() {
  // Implémentation de la synchronisation des données
}

/**
 * Mettre à jour le cache
 */
async function updateCache() {
  // Mettre à jour les ressources critiques
  const cache = await caches.open(CACHE_NAMES.static);
  
  for (const url of CRITICAL_URLS) {
    try {
      const response = await fetch(url);
      if (response.ok) {
        await cache.put(url, response);
      }
    } catch (error) {
      console.error(`[PWA SW] Failed to update cache for ${url}:`, error);
    }
  }
}

/**
 * Mettre en cache des URLs spécifiques
 */
async function cacheUrls(urls) {
  const cache = await caches.open(CACHE_NAMES.dynamic);
  
  for (const url of urls) {
    try {
      const response = await fetch(url);
      if (response.ok) {
        await cache.put(url, response);
      }
    } catch (error) {
      console.error(`[PWA SW] Failed to cache ${url}:`, error);
    }
  }
}

/**
 * Effacer un cache spécifique
 */
async function clearCache(cacheType) {
  if (cacheType && CACHE_NAMES[cacheType]) {
    await caches.delete(CACHE_NAMES[cacheType]);
    console.log(`[PWA SW] Cleared ${cacheType} cache`);
  }
}

/**
 * Obtenir le statut des caches
 */
async function getCacheStatus() {
  const status = {};
  
  for (const [type, name] of Object.entries(CACHE_NAMES)) {
    try {
      const cache = await caches.open(name);
      const keys = await cache.keys();
      status[type] = {
        name,
        entries: keys.length,
        exists: true
      };
    } catch (error) {
      status[type] = {
        name,
        entries: 0,
        exists: false,
        error: error.message
      };
    }
  }
  
  return status;
}

/**
 * Diffuser un message à tous les clients
 */
function broadcastMessage(type, data = {}) {
  self.clients.matchAll().then(clients => {
    clients.forEach(client => {
      client.postMessage({ type, data });
    });
  });
}

// Initialisation
console.log('[PWA SW] Service Worker loaded');
