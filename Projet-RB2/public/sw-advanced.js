/**
 * Advanced Service Worker - Sprint 16 Jour 3
 * Cache intelligent avec stratégies adaptatives et optimisation performance
 */

const CACHE_VERSION = 'v2.0.0';
const CACHE_NAMES = {
  static: `static-${CACHE_VERSION}`,
  dynamic: `dynamic-${CACHE_VERSION}`,
  api: `api-${CACHE_VERSION}`,
  images: `images-${CACHE_VERSION}`,
  fonts: `fonts-${CACHE_VERSION}`
};

const CACHE_STRATEGIES = {
  '/api/': 'NetworkFirst',
  '/static/js/': 'CacheFirst',
  '/static/css/': 'CacheFirst',
  '/static/images/': 'CacheFirst',
  '/static/fonts/': 'CacheFirst',
  '/': 'StaleWhileRevalidate'
};

const CACHE_MAX_AGE = {
  '/api/': 5 * 60 * 1000, // 5 minutes
  '/static/js/': 365 * 24 * 60 * 60 * 1000, // 1 an
  '/static/css/': 365 * 24 * 60 * 60 * 1000, // 1 an
  '/static/images/': 30 * 24 * 60 * 60 * 1000, // 30 jours
  '/static/fonts/': 365 * 24 * 60 * 60 * 1000, // 1 an
  '/': 24 * 60 * 60 * 1000 // 1 jour
};

const CACHE_MAX_ENTRIES = {
  static: 100,
  dynamic: 50,
  api: 100,
  images: 200,
  fonts: 20
};

// Configuration par défaut
let config = {
  enablePredictiveCache: true,
  enableImageOptimization: true,
  enableCompressionCache: true,
  enableOfflineMode: true,
  debugMode: false
};

/**
 * Installation du Service Worker
 */
self.addEventListener('install', event => {
  debug('Service Worker installing...');
  
  event.waitUntil(
    Promise.all([
      cacheStaticAssets(),
      self.skipWaiting()
    ])
  );
});

/**
 * Activation du Service Worker
 */
self.addEventListener('activate', event => {
  debug('Service Worker activating...');
  
  event.waitUntil(
    Promise.all([
      cleanupOldCaches(),
      self.clients.claim()
    ])
  );
});

/**
 * Interception des requêtes
 */
self.addEventListener('fetch', event => {
  const request = event.request;
  
  // Ignorer les requêtes non-HTTP
  if (!request.url.startsWith('http')) {
    return;
  }

  // Ignorer les requêtes POST/PUT/DELETE pour les APIs
  if (request.method !== 'GET' && request.url.includes('/api/')) {
    return;
  }

  event.respondWith(handleRequest(request));
});

/**
 * Messages du main thread
 */
self.addEventListener('message', event => {
  const { type, data } = event.data;
  
  switch (type) {
    case 'CONFIGURE_CACHE':
      config = { ...config, ...data.config };
      debug('Cache configuration updated:', config);
      break;
      
    case 'PRELOAD_RESOURCES':
      preloadResources(data.resources);
      break;
      
    case 'CLEAR_CACHE':
      clearSpecificCache(data.cacheType);
      break;
      
    case 'INCREASE_CACHE_AGGRESSIVENESS':
      increaseCacheAggressiveness();
      break;
      
    case 'GET_CACHE_STATUS':
      getCacheStatus().then(status => {
        event.ports[0].postMessage(status);
      });
      break;
  }
});

/**
 * Gestion des requêtes avec stratégies adaptatives
 */
async function handleRequest(request) {
  const url = new URL(request.url);
  const strategy = determineStrategy(url.pathname);
  
  debug(`Handling ${request.url} with strategy: ${strategy}`);
  
  try {
    switch (strategy) {
      case 'CacheFirst':
        return await cacheFirst(request);
      case 'NetworkFirst':
        return await networkFirst(request);
      case 'StaleWhileRevalidate':
        return await staleWhileRevalidate(request);
      case 'NetworkOnly':
        return await fetch(request);
      case 'CacheOnly':
        return await cacheOnly(request);
      default:
        return await staleWhileRevalidate(request);
    }
  } catch (error) {
    debug('Request failed:', error);
    return await handleOfflineRequest(request);
  }
}

/**
 * Stratégie Cache First
 */
async function cacheFirst(request) {
  const cachedResponse = await getCachedResponse(request);
  
  if (cachedResponse) {
    // Vérifier si le cache est encore valide
    if (await isCacheValid(request, cachedResponse)) {
      debug('Cache hit (valid):', request.url);
      return cachedResponse;
    }
  }
  
  // Récupérer depuis le réseau et mettre en cache
  const networkResponse = await fetch(request);
  await cacheResponse(request, networkResponse.clone());
  
  debug('Network response cached:', request.url);
  return networkResponse;
}

/**
 * Stratégie Network First
 */
async function networkFirst(request) {
  try {
    const networkResponse = await fetch(request);
    
    // Mettre en cache si la réponse est valide
    if (networkResponse.ok) {
      await cacheResponse(request, networkResponse.clone());
    }
    
    debug('Network first success:', request.url);
    return networkResponse;
  } catch (error) {
    // Fallback vers le cache
    const cachedResponse = await getCachedResponse(request);
    
    if (cachedResponse) {
      debug('Network failed, serving from cache:', request.url);
      return cachedResponse;
    }
    
    throw error;
  }
}

/**
 * Stratégie Stale While Revalidate
 */
async function staleWhileRevalidate(request) {
  const cachedResponse = await getCachedResponse(request);
  
  // Démarrer la revalidation en arrière-plan
  const networkPromise = fetch(request).then(response => {
    if (response.ok) {
      cacheResponse(request, response.clone());
    }
    return response;
  }).catch(error => {
    debug('Background revalidation failed:', error);
  });
  
  if (cachedResponse) {
    debug('Serving stale content:', request.url);
    return cachedResponse;
  }
  
  // Si pas de cache, attendre la réponse réseau
  debug('No cache, waiting for network:', request.url);
  return await networkPromise;
}

/**
 * Stratégie Cache Only
 */
async function cacheOnly(request) {
  const cachedResponse = await getCachedResponse(request);
  
  if (cachedResponse) {
    return cachedResponse;
  }
  
  throw new Error('No cached response available');
}

/**
 * Déterminer la stratégie de cache
 */
function determineStrategy(pathname) {
  // Vérifier les patterns configurés
  for (const [pattern, strategy] of Object.entries(CACHE_STRATEGIES)) {
    if (pathname.startsWith(pattern)) {
      return strategy;
    }
  }
  
  // Stratégies spéciales basées sur l'extension
  if (pathname.match(/\.(js|css|woff2|woff|ttf)$/)) {
    return 'CacheFirst';
  }
  
  if (pathname.match(/\.(jpg|jpeg|png|gif|webp|avif|svg)$/)) {
    return 'CacheFirst';
  }
  
  if (pathname.startsWith('/api/')) {
    return 'NetworkFirst';
  }
  
  return 'StaleWhileRevalidate';
}

/**
 * Obtenir une réponse en cache
 */
async function getCachedResponse(request) {
  const cacheKey = getCacheKey(request);
  const cacheName = getCacheName(request);
  
  const cache = await caches.open(cacheName);
  return await cache.match(cacheKey);
}

/**
 * Mettre en cache une réponse
 */
async function cacheResponse(request, response) {
  // Ne pas cacher les réponses d'erreur
  if (!response.ok) {
    return;
  }
  
  const cacheName = getCacheName(request);
  const cache = await caches.open(cacheName);
  
  // Optimiser les images avant mise en cache
  if (config.enableImageOptimization && isImageRequest(request)) {
    const optimizedResponse = await optimizeImageResponse(response);
    await cache.put(request, optimizedResponse);
  } else {
    await cache.put(request, response);
  }
  
  // Nettoyer le cache si nécessaire
  await cleanupCache(cacheName);
}

/**
 * Vérifier si le cache est valide
 */
async function isCacheValid(request, cachedResponse) {
  const url = new URL(request.url);
  const maxAge = getMaxAge(url.pathname);
  
  const cacheDate = new Date(cachedResponse.headers.get('date') || 0);
  const now = new Date();
  
  return (now.getTime() - cacheDate.getTime()) < maxAge;
}

/**
 * Obtenir le nom du cache approprié
 */
function getCacheName(request) {
  const url = new URL(request.url);
  
  if (url.pathname.startsWith('/api/')) {
    return CACHE_NAMES.api;
  }
  
  if (url.pathname.startsWith('/static/js/') || url.pathname.startsWith('/static/css/')) {
    return CACHE_NAMES.static;
  }
  
  if (isImageRequest(request)) {
    return CACHE_NAMES.images;
  }
  
  if (url.pathname.match(/\.(woff2|woff|ttf)$/)) {
    return CACHE_NAMES.fonts;
  }
  
  return CACHE_NAMES.dynamic;
}

/**
 * Obtenir la clé de cache
 */
function getCacheKey(request) {
  const url = new URL(request.url);
  
  // Ignorer certains paramètres de query pour améliorer le cache hit
  const ignoredParams = ['utm_source', 'utm_medium', 'utm_campaign', '_t', 'v'];
  
  ignoredParams.forEach(param => {
    url.searchParams.delete(param);
  });
  
  return url.toString();
}

/**
 * Obtenir l'âge maximum du cache
 */
function getMaxAge(pathname) {
  for (const [pattern, maxAge] of Object.entries(CACHE_MAX_AGE)) {
    if (pathname.startsWith(pattern)) {
      return maxAge;
    }
  }
  
  return 24 * 60 * 60 * 1000; // 1 jour par défaut
}

/**
 * Vérifier si c'est une requête d'image
 */
function isImageRequest(request) {
  return request.url.match(/\.(jpg|jpeg|png|gif|webp|avif|svg)$/i) ||
         request.headers.get('accept')?.includes('image/');
}

/**
 * Optimiser la réponse d'image
 */
async function optimizeImageResponse(response) {
  if (!config.enableImageOptimization) {
    return response;
  }
  
  // Pour une implémentation complète, on utiliserait une bibliothèque
  // comme Sharp ou Canvas API pour optimiser les images
  // Ici, on retourne la réponse originale
  return response;
}

/**
 * Mettre en cache les assets statiques
 */
async function cacheStaticAssets() {
  const staticAssets = [
    '/',
    '/static/css/main.css',
    '/static/js/main.js',
    '/static/images/logo.svg',
    '/manifest.json'
  ];
  
  const cache = await caches.open(CACHE_NAMES.static);
  
  try {
    await cache.addAll(staticAssets);
    debug('Static assets cached successfully');
  } catch (error) {
    debug('Failed to cache static assets:', error);
  }
}

/**
 * Nettoyer les anciens caches
 */
async function cleanupOldCaches() {
  const cacheNames = await caches.keys();
  const currentCaches = Object.values(CACHE_NAMES);
  
  const deletePromises = cacheNames
    .filter(cacheName => !currentCaches.includes(cacheName))
    .map(cacheName => caches.delete(cacheName));
  
  await Promise.all(deletePromises);
  debug('Old caches cleaned up');
}

/**
 * Nettoyer un cache spécifique
 */
async function cleanupCache(cacheName) {
  const cache = await caches.open(cacheName);
  const requests = await cache.keys();
  
  const maxEntries = CACHE_MAX_ENTRIES[cacheName.split('-')[0]] || 50;
  
  if (requests.length > maxEntries) {
    // Supprimer les entrées les plus anciennes
    const entriesToDelete = requests.slice(0, requests.length - maxEntries);
    
    await Promise.all(
      entriesToDelete.map(request => cache.delete(request))
    );
    
    debug(`Cleaned up ${entriesToDelete.length} entries from ${cacheName}`);
  }
}

/**
 * Gérer les requêtes hors ligne
 */
async function handleOfflineRequest(request) {
  const url = new URL(request.url);
  
  // Essayer de servir depuis le cache
  const cachedResponse = await getCachedResponse(request);
  if (cachedResponse) {
    return cachedResponse;
  }
  
  // Page hors ligne pour les navigations
  if (request.mode === 'navigate') {
    const offlinePage = await caches.match('/offline.html');
    if (offlinePage) {
      return offlinePage;
    }
  }
  
  // Réponse d'erreur générique
  return new Response('Offline', {
    status: 503,
    statusText: 'Service Unavailable',
    headers: {
      'Content-Type': 'text/plain'
    }
  });
}

/**
 * Preloader des ressources
 */
async function preloadResources(resources) {
  if (!Array.isArray(resources)) {
    return;
  }
  
  const cache = await caches.open(CACHE_NAMES.dynamic);
  
  const preloadPromises = resources.map(async (resource) => {
    try {
      const response = await fetch(resource);
      if (response.ok) {
        await cache.put(resource, response);
        debug('Preloaded:', resource);
      }
    } catch (error) {
      debug('Failed to preload:', resource, error);
    }
  });
  
  await Promise.all(preloadPromises);
}

/**
 * Effacer un cache spécifique
 */
async function clearSpecificCache(cacheType) {
  if (cacheType && CACHE_NAMES[cacheType]) {
    await caches.delete(CACHE_NAMES[cacheType]);
    debug(`Cleared ${cacheType} cache`);
  }
}

/**
 * Augmenter l'agressivité du cache
 */
function increaseCacheAggressiveness() {
  // Augmenter les durées de cache
  Object.keys(CACHE_MAX_AGE).forEach(key => {
    CACHE_MAX_AGE[key] *= 2;
  });
  
  // Augmenter les limites d'entrées
  Object.keys(CACHE_MAX_ENTRIES).forEach(key => {
    CACHE_MAX_ENTRIES[key] *= 1.5;
  });
  
  debug('Cache aggressiveness increased');
}

/**
 * Obtenir le statut du cache
 */
async function getCacheStatus() {
  const cacheNames = await caches.keys();
  const status = {};
  
  for (const cacheName of cacheNames) {
    const cache = await caches.open(cacheName);
    const keys = await cache.keys();
    status[cacheName] = {
      entries: keys.length,
      size: await calculateCacheSize(cache, keys)
    };
  }
  
  return status;
}

/**
 * Calculer la taille du cache
 */
async function calculateCacheSize(cache, keys) {
  let totalSize = 0;
  
  for (const key of keys.slice(0, 10)) { // Échantillon pour éviter la lenteur
    try {
      const response = await cache.match(key);
      if (response && response.headers.get('content-length')) {
        totalSize += parseInt(response.headers.get('content-length'));
      }
    } catch (error) {
      // Ignorer les erreurs
    }
  }
  
  return totalSize;
}

/**
 * Fonction de debug
 */
function debug(message, ...args) {
  if (config.debugMode) {
    console.log(`[SW Advanced] ${message}`, ...args);
  }
}

// Initialisation
debug('Advanced Service Worker loaded');
