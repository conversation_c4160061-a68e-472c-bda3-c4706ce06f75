/**
 * Load Test Suite - Sprint 16 Jour 3
 * Tests de charge complets pour validation performance <200ms
 */

import { performance } from 'perf_hooks';

interface LoadTestConfig {
  baseUrl: string;
  maxUsers: number;
  rampUpDuration: number; // seconds
  testDuration: number; // seconds
  scenarios: LoadTestScenario[];
  thresholds: PerformanceThresholds;
}

interface LoadTestScenario {
  name: string;
  weight: number; // percentage
  steps: LoadTestStep[];
}

interface LoadTestStep {
  name: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  url: string;
  headers?: Record<string, string>;
  body?: any;
  thinkTime?: number; // ms
  validation?: (response: Response) => boolean;
}

interface PerformanceThresholds {
  responseTime: {
    p50: number; // ms
    p95: number; // ms
    p99: number; // ms
  };
  errorRate: number; // percentage
  throughput: number; // requests per second
}

interface LoadTestResult {
  scenario: string;
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  errorRate: number;
  responseTime: {
    min: number;
    max: number;
    avg: number;
    p50: number;
    p95: number;
    p99: number;
  };
  throughput: number;
  errors: Array<{
    url: string;
    error: string;
    count: number;
  }>;
}

interface VirtualUser {
  id: number;
  scenario: LoadTestScenario;
  currentStep: number;
  startTime: number;
  requests: Array<{
    url: string;
    startTime: number;
    endTime: number;
    status: number;
    error?: string;
  }>;
}

export class LoadTestSuite {
  private config: LoadTestConfig;
  private virtualUsers: VirtualUser[] = [];
  private results: Map<string, LoadTestResult> = new Map();
  private isRunning = false;
  private startTime = 0;

  constructor(config: LoadTestConfig) {
    this.config = config;
  }

  /**
   * Exécuter la suite de tests de charge
   */
  public async runLoadTest(): Promise<Map<string, LoadTestResult>> {
    console.log('🚀 Starting Load Test Suite...');
    console.log(`Target: ${this.config.baseUrl}`);
    console.log(`Max Users: ${this.config.maxUsers}`);
    console.log(`Duration: ${this.config.testDuration}s`);

    this.isRunning = true;
    this.startTime = Date.now();

    try {
      // Phase 1: Ramp-up des utilisateurs virtuels
      await this.rampUpUsers();

      // Phase 2: Maintenir la charge
      await this.maintainLoad();

      // Phase 3: Analyser les résultats
      this.analyzeResults();

      // Phase 4: Générer le rapport
      await this.generateReport();

    } catch (error) {
      console.error('❌ Load test failed:', error);
    } finally {
      this.isRunning = false;
    }

    return this.results;
  }

  /**
   * Phase de montée en charge
   */
  private async rampUpUsers(): Promise<void> {
    console.log('📈 Ramping up virtual users...');

    const rampUpInterval = (this.config.rampUpDuration * 1000) / this.config.maxUsers;

    for (let i = 0; i < this.config.maxUsers; i++) {
      if (!this.isRunning) break;

      const scenario = this.selectScenario();
      const user = this.createVirtualUser(i, scenario);
      
      this.virtualUsers.push(user);
      this.startUserScenario(user);

      // Attendre avant de créer le prochain utilisateur
      await this.sleep(rampUpInterval);
    }

    console.log(`✅ ${this.virtualUsers.length} virtual users created`);
  }

  /**
   * Maintenir la charge pendant la durée du test
   */
  private async maintainLoad(): Promise<void> {
    console.log('⚡ Maintaining load...');

    const endTime = this.startTime + (this.config.testDuration * 1000);

    while (Date.now() < endTime && this.isRunning) {
      // Vérifier et redémarrer les utilisateurs qui ont terminé
      await this.manageVirtualUsers();
      
      // Afficher les statistiques en temps réel
      this.displayRealTimeStats();

      await this.sleep(1000); // Check every second
    }

    console.log('🏁 Load test completed');
  }

  /**
   * Sélectionner un scénario basé sur les poids
   */
  private selectScenario(): LoadTestScenario {
    const random = Math.random() * 100;
    let cumulativeWeight = 0;

    for (const scenario of this.config.scenarios) {
      cumulativeWeight += scenario.weight;
      if (random <= cumulativeWeight) {
        return scenario;
      }
    }

    return this.config.scenarios[0]; // Fallback
  }

  /**
   * Créer un utilisateur virtuel
   */
  private createVirtualUser(id: number, scenario: LoadTestScenario): VirtualUser {
    return {
      id,
      scenario,
      currentStep: 0,
      startTime: Date.now(),
      requests: []
    };
  }

  /**
   * Démarrer le scénario d'un utilisateur
   */
  private async startUserScenario(user: VirtualUser): Promise<void> {
    try {
      while (this.isRunning && user.currentStep < user.scenario.steps.length) {
        const step = user.scenario.steps[user.currentStep];
        
        await this.executeStep(user, step);
        
        user.currentStep++;

        // Think time entre les étapes
        if (step.thinkTime && step.thinkTime > 0) {
          await this.sleep(step.thinkTime);
        }
      }

      // Redémarrer le scénario
      if (this.isRunning) {
        user.currentStep = 0;
        setTimeout(() => this.startUserScenario(user), 1000);
      }

    } catch (error) {
      console.error(`User ${user.id} scenario failed:`, error);
    }
  }

  /**
   * Exécuter une étape du scénario
   */
  private async executeStep(user: VirtualUser, step: LoadTestStep): Promise<void> {
    const startTime = performance.now();
    const url = this.config.baseUrl + step.url;

    try {
      const response = await fetch(url, {
        method: step.method,
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': `LoadTest-User-${user.id}`,
          ...step.headers
        },
        body: step.body ? JSON.stringify(step.body) : undefined
      });

      const endTime = performance.now();

      // Enregistrer la requête
      user.requests.push({
        url: step.url,
        startTime,
        endTime,
        status: response.status,
        error: response.ok ? undefined : `HTTP ${response.status}`
      });

      // Validation personnalisée
      if (step.validation && !step.validation(response)) {
        throw new Error('Custom validation failed');
      }

    } catch (error) {
      const endTime = performance.now();

      user.requests.push({
        url: step.url,
        startTime,
        endTime,
        status: 0,
        error: error.message
      });
    }
  }

  /**
   * Gérer les utilisateurs virtuels
   */
  private async manageVirtualUsers(): Promise<void> {
    // Compter les utilisateurs actifs
    const activeUsers = this.virtualUsers.filter(user => 
      Date.now() - user.startTime < 60000 // Actif dans la dernière minute
    );

    // Redémarrer des utilisateurs si nécessaire
    if (activeUsers.length < this.config.maxUsers * 0.8) {
      const usersToRestart = this.config.maxUsers - activeUsers.length;
      
      for (let i = 0; i < usersToRestart; i++) {
        const scenario = this.selectScenario();
        const user = this.createVirtualUser(this.virtualUsers.length + i, scenario);
        
        this.virtualUsers.push(user);
        this.startUserScenario(user);
      }
    }
  }

  /**
   * Afficher les statistiques en temps réel
   */
  private displayRealTimeStats(): void {
    const now = Date.now();
    const elapsed = (now - this.startTime) / 1000;
    
    // Calculer les statistiques des 10 dernières secondes
    const recentRequests = this.getAllRequests().filter(req => 
      now - req.endTime < 10000
    );

    const totalRequests = recentRequests.length;
    const successfulRequests = recentRequests.filter(req => !req.error).length;
    const errorRate = totalRequests > 0 ? ((totalRequests - successfulRequests) / totalRequests) * 100 : 0;
    const avgResponseTime = totalRequests > 0 ? 
      recentRequests.reduce((sum, req) => sum + (req.endTime - req.startTime), 0) / totalRequests : 0;

    console.log(`⏱️  ${elapsed.toFixed(0)}s | Users: ${this.virtualUsers.length} | RPS: ${(totalRequests/10).toFixed(1)} | Avg: ${avgResponseTime.toFixed(0)}ms | Errors: ${errorRate.toFixed(1)}%`);
  }

  /**
   * Analyser les résultats
   */
  private analyzeResults(): void {
    console.log('📊 Analyzing results...');

    for (const scenario of this.config.scenarios) {
      const scenarioRequests = this.getScenarioRequests(scenario.name);
      const result = this.calculateScenarioMetrics(scenario.name, scenarioRequests);
      
      this.results.set(scenario.name, result);
    }
  }

  /**
   * Obtenir toutes les requêtes
   */
  private getAllRequests(): Array<{
    url: string;
    startTime: number;
    endTime: number;
    status: number;
    error?: string;
  }> {
    return this.virtualUsers.flatMap(user => user.requests);
  }

  /**
   * Obtenir les requêtes d'un scénario
   */
  private getScenarioRequests(scenarioName: string): Array<{
    url: string;
    startTime: number;
    endTime: number;
    status: number;
    error?: string;
  }> {
    return this.virtualUsers
      .filter(user => user.scenario.name === scenarioName)
      .flatMap(user => user.requests);
  }

  /**
   * Calculer les métriques d'un scénario
   */
  private calculateScenarioMetrics(scenarioName: string, requests: any[]): LoadTestResult {
    const totalRequests = requests.length;
    const successfulRequests = requests.filter(req => !req.error).length;
    const failedRequests = totalRequests - successfulRequests;
    const errorRate = totalRequests > 0 ? (failedRequests / totalRequests) * 100 : 0;

    // Calculer les temps de réponse
    const responseTimes = requests
      .filter(req => !req.error)
      .map(req => req.endTime - req.startTime)
      .sort((a, b) => a - b);

    const responseTime = {
      min: responseTimes.length > 0 ? Math.min(...responseTimes) : 0,
      max: responseTimes.length > 0 ? Math.max(...responseTimes) : 0,
      avg: responseTimes.length > 0 ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length : 0,
      p50: this.percentile(responseTimes, 50),
      p95: this.percentile(responseTimes, 95),
      p99: this.percentile(responseTimes, 99)
    };

    // Calculer le throughput
    const testDuration = (Date.now() - this.startTime) / 1000;
    const throughput = totalRequests / testDuration;

    // Analyser les erreurs
    const errorMap = new Map<string, number>();
    requests.filter(req => req.error).forEach(req => {
      const key = `${req.url}: ${req.error}`;
      errorMap.set(key, (errorMap.get(key) || 0) + 1);
    });

    const errors = Array.from(errorMap.entries()).map(([error, count]) => ({
      url: error.split(':')[0],
      error: error.split(':').slice(1).join(':').trim(),
      count
    }));

    return {
      scenario: scenarioName,
      totalRequests,
      successfulRequests,
      failedRequests,
      errorRate,
      responseTime,
      throughput,
      errors
    };
  }

  /**
   * Calculer un percentile
   */
  private percentile(values: number[], p: number): number {
    if (values.length === 0) return 0;
    
    const index = Math.ceil((p / 100) * values.length) - 1;
    return values[Math.max(0, Math.min(index, values.length - 1))];
  }

  /**
   * Générer le rapport
   */
  private async generateReport(): Promise<void> {
    console.log('\n📋 LOAD TEST REPORT');
    console.log('='.repeat(50));

    for (const [scenarioName, result] of this.results) {
      console.log(`\n🎯 Scenario: ${scenarioName}`);
      console.log(`   Total Requests: ${result.totalRequests}`);
      console.log(`   Success Rate: ${((result.successfulRequests / result.totalRequests) * 100).toFixed(1)}%`);
      console.log(`   Error Rate: ${result.errorRate.toFixed(1)}%`);
      console.log(`   Throughput: ${result.throughput.toFixed(1)} RPS`);
      
      console.log(`\n   Response Times:`);
      console.log(`     Average: ${result.responseTime.avg.toFixed(0)}ms`);
      console.log(`     P50: ${result.responseTime.p50.toFixed(0)}ms`);
      console.log(`     P95: ${result.responseTime.p95.toFixed(0)}ms`);
      console.log(`     P99: ${result.responseTime.p99.toFixed(0)}ms`);

      // Vérifier les seuils
      this.checkThresholds(result);

      if (result.errors.length > 0) {
        console.log(`\n   Top Errors:`);
        result.errors.slice(0, 5).forEach(error => {
          console.log(`     ${error.error}: ${error.count} times`);
        });
      }
    }

    // Résumé global
    this.generateGlobalSummary();
  }

  /**
   * Vérifier les seuils de performance
   */
  private checkThresholds(result: LoadTestResult): void {
    const thresholds = this.config.thresholds;
    const issues: string[] = [];

    if (result.responseTime.p50 > thresholds.responseTime.p50) {
      issues.push(`P50 response time: ${result.responseTime.p50.toFixed(0)}ms > ${thresholds.responseTime.p50}ms`);
    }

    if (result.responseTime.p95 > thresholds.responseTime.p95) {
      issues.push(`P95 response time: ${result.responseTime.p95.toFixed(0)}ms > ${thresholds.responseTime.p95}ms`);
    }

    if (result.errorRate > thresholds.errorRate) {
      issues.push(`Error rate: ${result.errorRate.toFixed(1)}% > ${thresholds.errorRate}%`);
    }

    if (result.throughput < thresholds.throughput) {
      issues.push(`Throughput: ${result.throughput.toFixed(1)} RPS < ${thresholds.throughput} RPS`);
    }

    if (issues.length > 0) {
      console.log(`\n   ⚠️  Threshold Violations:`);
      issues.forEach(issue => console.log(`     ${issue}`));
    } else {
      console.log(`\n   ✅ All thresholds passed`);
    }
  }

  /**
   * Générer le résumé global
   */
  private generateGlobalSummary(): void {
    const allRequests = this.getAllRequests();
    const totalRequests = allRequests.length;
    const successfulRequests = allRequests.filter(req => !req.error).length;
    const testDuration = (Date.now() - this.startTime) / 1000;

    console.log(`\n🌟 GLOBAL SUMMARY`);
    console.log(`   Test Duration: ${testDuration.toFixed(0)}s`);
    console.log(`   Total Requests: ${totalRequests}`);
    console.log(`   Success Rate: ${((successfulRequests / totalRequests) * 100).toFixed(1)}%`);
    console.log(`   Average Throughput: ${(totalRequests / testDuration).toFixed(1)} RPS`);
    console.log(`   Virtual Users: ${this.config.maxUsers}`);
  }

  /**
   * Utilitaire pour attendre
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Arrêter le test de charge
   */
  public stop(): void {
    this.isRunning = false;
    console.log('🛑 Load test stopped');
  }
}

/**
 * Configuration par défaut pour les tests de performance
 */
export const defaultLoadTestConfig: LoadTestConfig = {
  baseUrl: 'http://localhost:3000',
  maxUsers: 100,
  rampUpDuration: 30, // 30 seconds
  testDuration: 300, // 5 minutes
  scenarios: [
    {
      name: 'Homepage Navigation',
      weight: 40,
      steps: [
        {
          name: 'Load Homepage',
          method: 'GET',
          url: '/',
          thinkTime: 2000
        },
        {
          name: 'Load Search',
          method: 'GET',
          url: '/search',
          thinkTime: 3000
        }
      ]
    },
    {
      name: 'User Journey',
      weight: 35,
      steps: [
        {
          name: 'Load Homepage',
          method: 'GET',
          url: '/',
          thinkTime: 1000
        },
        {
          name: 'Search Retreats',
          method: 'GET',
          url: '/api/retreats?q=yoga',
          thinkTime: 2000
        },
        {
          name: 'View Retreat Details',
          method: 'GET',
          url: '/retreat/123',
          thinkTime: 5000
        },
        {
          name: 'Check Availability',
          method: 'GET',
          url: '/api/retreats/123/availability',
          thinkTime: 1000
        }
      ]
    },
    {
      name: 'API Heavy',
      weight: 25,
      steps: [
        {
          name: 'Get User Profile',
          method: 'GET',
          url: '/api/user/profile',
          headers: { 'Authorization': 'Bearer test-token' },
          thinkTime: 500
        },
        {
          name: 'Get Recommendations',
          method: 'GET',
          url: '/api/recommendations',
          thinkTime: 1000
        },
        {
          name: 'Update Preferences',
          method: 'PUT',
          url: '/api/user/preferences',
          body: { interests: ['yoga', 'meditation'] },
          thinkTime: 2000
        }
      ]
    }
  ],
  thresholds: {
    responseTime: {
      p50: 200, // ms
      p95: 500, // ms
      p99: 1000 // ms
    },
    errorRate: 1, // 1%
    throughput: 50 // RPS
  }
};

export default LoadTestSuite;
