/**
 * Monitoring Business Avancé - Sprint 14
 * Système de surveillance des métriques business en temps réel
 */

interface BusinessMetric {
  name: string;
  value: number;
  timestamp: number;
  category: 'conversion' | 'performance' | 'user' | 'revenue' | 'error';
  tags?: Record<string, string>;
}

interface Alert {
  id: string;
  metric: string;
  threshold: number;
  condition: 'above' | 'below' | 'equal';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  isActive: boolean;
  triggeredAt?: number;
}

interface DashboardConfig {
  refreshInterval: number;
  retentionDays: number;
  alertsEnabled: boolean;
  realTimeEnabled: boolean;
}

export class AdvancedBusinessMonitoring {
  private metrics: BusinessMetric[] = [];
  private alerts: Alert[] = [];
  private config: DashboardConfig;
  private websocket: WebSocket | null = null;
  private intervalId: NodeJS.Timeout | null = null;

  constructor(config: Partial<DashboardConfig> = {}) {
    this.config = {
      refreshInterval: 30000, // 30 secondes
      retentionDays: 30,
      alertsEnabled: true,
      realTimeEnabled: true,
      ...config
    };

    this.initializeDefaultAlerts();
    this.startMonitoring();
  }

  /**
   * Initialiser les alertes par défaut
   */
  private initializeDefaultAlerts() {
    this.alerts = [
      {
        id: 'conversion-rate-low',
        metric: 'conversion_rate',
        threshold: 2.0,
        condition: 'below',
        severity: 'high',
        message: 'Taux de conversion en dessous de 2%',
        isActive: false
      },
      {
        id: 'page-load-time-high',
        metric: 'page_load_time',
        threshold: 3000,
        condition: 'above',
        severity: 'medium',
        message: 'Temps de chargement supérieur à 3 secondes',
        isActive: false
      },
      {
        id: 'error-rate-high',
        metric: 'error_rate',
        threshold: 5.0,
        condition: 'above',
        severity: 'critical',
        message: 'Taux d\'erreur supérieur à 5%',
        isActive: false
      },
      {
        id: 'bounce-rate-high',
        metric: 'bounce_rate',
        threshold: 70.0,
        condition: 'above',
        severity: 'medium',
        message: 'Taux de rebond supérieur à 70%',
        isActive: false
      },
      {
        id: 'revenue-drop',
        metric: 'daily_revenue',
        threshold: 1000,
        condition: 'below',
        severity: 'high',
        message: 'Revenus journaliers en baisse significative',
        isActive: false
      }
    ];
  }

  /**
   * Démarrer le monitoring
   */
  private startMonitoring() {
    if (this.config.realTimeEnabled) {
      this.connectWebSocket();
    }

    this.intervalId = setInterval(() => {
      this.collectMetrics();
      this.checkAlerts();
      this.cleanOldMetrics();
    }, this.config.refreshInterval);
  }

  /**
   * Connecter au WebSocket pour les données temps réel
   */
  private connectWebSocket() {
    try {
      this.websocket = new WebSocket(process.env.REACT_APP_WS_URL || 'ws://localhost:8080/monitoring');
      
      this.websocket.onmessage = (event) => {
        const data = JSON.parse(event.data);
        this.addMetric(data);
      };

      this.websocket.onerror = (error) => {
        console.error('WebSocket error:', error);
      };

      this.websocket.onclose = () => {
        // Reconnexion automatique après 5 secondes
        setTimeout(() => this.connectWebSocket(), 5000);
      };
    } catch (error) {
      console.error('Failed to connect WebSocket:', error);
    }
  }

  /**
   * Collecter les métriques business
   */
  private async collectMetrics() {
    try {
      // Métriques de conversion
      await this.collectConversionMetrics();
      
      // Métriques de performance
      await this.collectPerformanceMetrics();
      
      // Métriques utilisateur
      await this.collectUserMetrics();
      
      // Métriques de revenus
      await this.collectRevenueMetrics();
      
      // Métriques d'erreurs
      await this.collectErrorMetrics();
    } catch (error) {
      console.error('Error collecting metrics:', error);
    }
  }

  /**
   * Collecter les métriques de conversion
   */
  private async collectConversionMetrics() {
    const response = await fetch('/api/analytics/conversion');
    const data = await response.json();

    this.addMetric({
      name: 'conversion_rate',
      value: data.conversionRate,
      timestamp: Date.now(),
      category: 'conversion',
      tags: { period: 'daily' }
    });

    this.addMetric({
      name: 'booking_completion_rate',
      value: data.bookingCompletionRate,
      timestamp: Date.now(),
      category: 'conversion',
      tags: { funnel: 'booking' }
    });
  }

  /**
   * Collecter les métriques de performance
   */
  private async collectPerformanceMetrics() {
    // Core Web Vitals
    const vitals = await this.getCoreWebVitals();
    
    Object.entries(vitals).forEach(([key, value]) => {
      this.addMetric({
        name: key,
        value: value as number,
        timestamp: Date.now(),
        category: 'performance',
        tags: { type: 'core_web_vitals' }
      });
    });

    // Temps de réponse API
    const apiResponse = await fetch('/api/health/performance');
    const apiData = await apiResponse.json();

    this.addMetric({
      name: 'api_response_time',
      value: apiData.averageResponseTime,
      timestamp: Date.now(),
      category: 'performance',
      tags: { endpoint: 'average' }
    });
  }

  /**
   * Collecter les métriques utilisateur
   */
  private async collectUserMetrics() {
    const response = await fetch('/api/analytics/users');
    const data = await response.json();

    this.addMetric({
      name: 'active_users',
      value: data.activeUsers,
      timestamp: Date.now(),
      category: 'user',
      tags: { period: 'current' }
    });

    this.addMetric({
      name: 'bounce_rate',
      value: data.bounceRate,
      timestamp: Date.now(),
      category: 'user',
      tags: { period: 'daily' }
    });

    this.addMetric({
      name: 'session_duration',
      value: data.averageSessionDuration,
      timestamp: Date.now(),
      category: 'user',
      tags: { type: 'average' }
    });
  }

  /**
   * Collecter les métriques de revenus
   */
  private async collectRevenueMetrics() {
    const response = await fetch('/api/analytics/revenue');
    const data = await response.json();

    this.addMetric({
      name: 'daily_revenue',
      value: data.dailyRevenue,
      timestamp: Date.now(),
      category: 'revenue',
      tags: { period: 'daily' }
    });

    this.addMetric({
      name: 'average_order_value',
      value: data.averageOrderValue,
      timestamp: Date.now(),
      category: 'revenue',
      tags: { type: 'aov' }
    });
  }

  /**
   * Collecter les métriques d'erreurs
   */
  private async collectErrorMetrics() {
    const response = await fetch('/api/analytics/errors');
    const data = await response.json();

    this.addMetric({
      name: 'error_rate',
      value: data.errorRate,
      timestamp: Date.now(),
      category: 'error',
      tags: { type: 'application' }
    });

    this.addMetric({
      name: 'failed_payments',
      value: data.failedPayments,
      timestamp: Date.now(),
      category: 'error',
      tags: { type: 'payment' }
    });
  }

  /**
   * Obtenir les Core Web Vitals
   */
  private async getCoreWebVitals(): Promise<Record<string, number>> {
    return new Promise((resolve) => {
      const vitals: Record<string, number> = {};

      // First Contentful Paint
      new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
        if (fcpEntry) {
          vitals.first_contentful_paint = fcpEntry.startTime;
        }
      }).observe({ entryTypes: ['paint'] });

      // Largest Contentful Paint
      new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        if (lastEntry) {
          vitals.largest_contentful_paint = lastEntry.startTime;
        }
      }).observe({ entryTypes: ['largest-contentful-paint'] });

      // Cumulative Layout Shift
      let clsValue = 0;
      new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value;
          }
        }
        vitals.cumulative_layout_shift = clsValue;
      }).observe({ entryTypes: ['layout-shift'] });

      setTimeout(() => resolve(vitals), 1000);
    });
  }

  /**
   * Ajouter une métrique
   */
  public addMetric(metric: BusinessMetric) {
    this.metrics.push(metric);
    
    // Limiter le nombre de métriques en mémoire
    if (this.metrics.length > 10000) {
      this.metrics = this.metrics.slice(-5000);
    }
  }

  /**
   * Vérifier les alertes
   */
  private checkAlerts() {
    if (!this.config.alertsEnabled) return;

    this.alerts.forEach(alert => {
      const recentMetrics = this.getRecentMetrics(alert.metric, 5); // 5 dernières minutes
      if (recentMetrics.length === 0) return;

      const latestValue = recentMetrics[recentMetrics.length - 1].value;
      const shouldTrigger = this.shouldTriggerAlert(alert, latestValue);

      if (shouldTrigger && !alert.isActive) {
        this.triggerAlert(alert, latestValue);
      } else if (!shouldTrigger && alert.isActive) {
        this.resolveAlert(alert);
      }
    });
  }

  /**
   * Vérifier si une alerte doit être déclenchée
   */
  private shouldTriggerAlert(alert: Alert, value: number): boolean {
    switch (alert.condition) {
      case 'above':
        return value > alert.threshold;
      case 'below':
        return value < alert.threshold;
      case 'equal':
        return value === alert.threshold;
      default:
        return false;
    }
  }

  /**
   * Déclencher une alerte
   */
  private triggerAlert(alert: Alert, value: number) {
    alert.isActive = true;
    alert.triggeredAt = Date.now();

    console.warn(`🚨 ALERTE ${alert.severity.toUpperCase()}: ${alert.message} (Valeur: ${value})`);

    // Envoyer notification
    this.sendNotification(alert, value);
  }

  /**
   * Résoudre une alerte
   */
  private resolveAlert(alert: Alert) {
    alert.isActive = false;
    alert.triggeredAt = undefined;

    console.info(`✅ Alerte résolue: ${alert.message}`);
  }

  /**
   * Envoyer une notification
   */
  private async sendNotification(alert: Alert, value: number) {
    try {
      await fetch('/api/notifications/alert', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          alert: alert.id,
          message: alert.message,
          value,
          severity: alert.severity,
          timestamp: Date.now()
        })
      });
    } catch (error) {
      console.error('Failed to send notification:', error);
    }
  }

  /**
   * Obtenir les métriques récentes
   */
  public getRecentMetrics(metricName: string, minutes: number): BusinessMetric[] {
    const cutoff = Date.now() - (minutes * 60 * 1000);
    return this.metrics.filter(m => 
      m.name === metricName && m.timestamp > cutoff
    );
  }

  /**
   * Obtenir les métriques par catégorie
   */
  public getMetricsByCategory(category: BusinessMetric['category']): BusinessMetric[] {
    return this.metrics.filter(m => m.category === category);
  }

  /**
   * Obtenir le dashboard des métriques
   */
  public getDashboardData() {
    const now = Date.now();
    const oneHourAgo = now - (60 * 60 * 1000);

    return {
      summary: {
        totalMetrics: this.metrics.length,
        activeAlerts: this.alerts.filter(a => a.isActive).length,
        lastUpdate: now
      },
      recentMetrics: this.metrics.filter(m => m.timestamp > oneHourAgo),
      alerts: this.alerts,
      categories: {
        conversion: this.getMetricsByCategory('conversion').slice(-10),
        performance: this.getMetricsByCategory('performance').slice(-10),
        user: this.getMetricsByCategory('user').slice(-10),
        revenue: this.getMetricsByCategory('revenue').slice(-10),
        error: this.getMetricsByCategory('error').slice(-10)
      }
    };
  }

  /**
   * Nettoyer les anciennes métriques
   */
  private cleanOldMetrics() {
    const cutoff = Date.now() - (this.config.retentionDays * 24 * 60 * 60 * 1000);
    this.metrics = this.metrics.filter(m => m.timestamp > cutoff);
  }

  /**
   * Arrêter le monitoring
   */
  public stop() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
    
    if (this.websocket) {
      this.websocket.close();
    }
  }

  /**
   * Exporter les métriques
   */
  public exportMetrics(format: 'json' | 'csv' = 'json') {
    if (format === 'csv') {
      const headers = ['name', 'value', 'timestamp', 'category', 'tags'];
      const rows = this.metrics.map(m => [
        m.name,
        m.value,
        m.timestamp,
        m.category,
        JSON.stringify(m.tags || {})
      ]);
      
      return [headers, ...rows].map(row => row.join(',')).join('\n');
    }
    
    return JSON.stringify(this.metrics, null, 2);
  }
}
