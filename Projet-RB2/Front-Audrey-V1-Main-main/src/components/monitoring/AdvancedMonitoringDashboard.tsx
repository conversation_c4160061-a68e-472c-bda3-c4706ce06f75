/**
 * Dashboard de Monitoring Avancé - Sprint 14
 * Interface de surveillance des métriques business en temps réel
 */

import React, { useState, useEffect } from 'react';
import { AdvancedBusinessMonitoring } from '../../monitoring/AdvancedBusinessMonitoring';
import { 
  Chart as ChartJS, 
  CategoryScale, 
  LinearScale, 
  PointElement, 
  LineElement, 
  Title, 
  Tooltip, 
  Legend,
  ArcElement 
} from 'chart.js';
import { Line, Doughnut } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface DashboardProps {
  refreshInterval?: number;
}

export const AdvancedMonitoringDashboard: React.FC<DashboardProps> = ({ 
  refreshInterval = 30000 
}) => {
  const [monitoring] = useState(() => new AdvancedBusinessMonitoring());
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('conversion');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const updateDashboard = () => {
      const data = monitoring.getDashboardData();
      setDashboardData(data);
      setIsLoading(false);
    };

    // Mise à jour initiale
    updateDashboard();

    // Mise à jour périodique
    const interval = setInterval(updateDashboard, refreshInterval);

    return () => {
      clearInterval(interval);
      monitoring.stop();
    };
  }, [monitoring, refreshInterval]);

  if (isLoading || !dashboardData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const { summary, recentMetrics, alerts, categories } = dashboardData;

  // Préparer les données pour les graphiques
  const getChartData = (categoryData: any[]) => {
    const labels = categoryData.map(m => new Date(m.timestamp).toLocaleTimeString());
    const data = categoryData.map(m => m.value);

    return {
      labels,
      datasets: [{
        label: selectedCategory,
        data,
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4
      }]
    };
  };

  const alertsData = {
    labels: ['Actives', 'Résolues'],
    datasets: [{
      data: [
        alerts.filter((a: any) => a.isActive).length,
        alerts.filter((a: any) => !a.isActive).length
      ],
      backgroundColor: ['#ef4444', '#10b981'],
      borderWidth: 0
    }]
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Dashboard de Monitoring Avancé
          </h1>
          <p className="text-gray-600">
            Surveillance en temps réel des métriques business critiques
          </p>
        </div>

        {/* Résumé */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Métriques Totales</p>
                <p className="text-2xl font-bold text-gray-900">{summary.totalMetrics}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-red-100 rounded-lg">
                <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Alertes Actives</p>
                <p className="text-2xl font-bold text-gray-900">{summary.activeAlerts}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Statut</p>
                <p className="text-2xl font-bold text-green-600">Opérationnel</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Dernière MAJ</p>
                <p className="text-sm font-bold text-gray-900">
                  {new Date(summary.lastUpdate).toLocaleTimeString()}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Graphiques et Alertes */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
          {/* Graphique des métriques */}
          <div className="lg:col-span-2 bg-white rounded-lg shadow p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-gray-900">Métriques en Temps Réel</h2>
              <select 
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-1 text-sm"
              >
                <option value="conversion">Conversion</option>
                <option value="performance">Performance</option>
                <option value="user">Utilisateurs</option>
                <option value="revenue">Revenus</option>
                <option value="error">Erreurs</option>
              </select>
            </div>
            
            {categories[selectedCategory] && categories[selectedCategory].length > 0 ? (
              <Line 
                data={getChartData(categories[selectedCategory])}
                options={{
                  responsive: true,
                  plugins: {
                    legend: { position: 'top' as const },
                    title: { display: false }
                  },
                  scales: {
                    y: { beginAtZero: true }
                  }
                }}
              />
            ) : (
              <div className="flex items-center justify-center h-64 text-gray-500">
                Aucune donnée disponible pour cette catégorie
              </div>
            )}
          </div>

          {/* Répartition des alertes */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4">État des Alertes</h2>
            <div className="h-64">
              <Doughnut 
                data={alertsData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: { position: 'bottom' as const }
                  }
                }}
              />
            </div>
          </div>
        </div>

        {/* Liste des alertes */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-xl font-bold text-gray-900">Alertes Récentes</h2>
          </div>
          <div className="divide-y divide-gray-200">
            {alerts.length > 0 ? (
              alerts.slice(0, 10).map((alert: any) => (
                <div key={alert.id} className="px-6 py-4 flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className={`px-2 py-1 rounded-full text-xs font-medium border ${getSeverityColor(alert.severity)}`}>
                      {alert.severity.toUpperCase()}
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{alert.message}</p>
                      <p className="text-sm text-gray-500">Métrique: {alert.metric}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      alert.isActive 
                        ? 'bg-red-100 text-red-800' 
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {alert.isActive ? 'Active' : 'Résolue'}
                    </span>
                    {alert.triggeredAt && (
                      <span className="text-xs text-gray-500">
                        {new Date(alert.triggeredAt).toLocaleString()}
                      </span>
                    )}
                  </div>
                </div>
              ))
            ) : (
              <div className="px-6 py-8 text-center text-gray-500">
                Aucune alerte configurée
              </div>
            )}
          </div>
        </div>

        {/* Actions rapides */}
        <div className="mt-8 flex justify-end space-x-4">
          <button 
            onClick={() => {
              const data = monitoring.exportMetrics('json');
              const blob = new Blob([data], { type: 'application/json' });
              const url = URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = `metrics-${Date.now()}.json`;
              a.click();
            }}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Exporter JSON
          </button>
          <button 
            onClick={() => {
              const data = monitoring.exportMetrics('csv');
              const blob = new Blob([data], { type: 'text/csv' });
              const url = URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = `metrics-${Date.now()}.csv`;
              a.click();
            }}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
          >
            Exporter CSV
          </button>
        </div>
      </div>
    </div>
  );
};
