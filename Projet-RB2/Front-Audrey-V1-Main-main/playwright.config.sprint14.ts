import { defineConfig, devices } from '@playwright/test';

/**
 * Configuration Playwright pour Sprint 14 - Tests E2E Complets
 * Optimisée pour les parcours critiques et la performance
 * @see https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
  testDir: './tests/e2e',

  /* Configuration parallèle optimisée pour Sprint 14 */
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 3 : 1,
  workers: process.env.CI ? 4 : undefined,
  timeout: 60000,

  /* Reporter optimisé pour CI/CD et monitoring */
  reporter: [
    ['html', {
      outputFolder: 'reports/playwright',
      open: 'never'
    }],
    ['json', {
      outputFile: 'reports/playwright/results.json'
    }],
    ['junit', {
      outputFile: 'reports/playwright/results.xml'
    }],
    ['blob', {
      outputDir: 'reports/playwright/blob'
    }],
    ...(process.env.CI ? [['github']] : [['list']])
  ],

  /* Configuration globale optimisée */
  use: {
    baseURL: process.env.BASE_URL || 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    actionTimeout: 15000,
    navigationTimeout: 45000,

    /* Headers de sécurité pour les tests */
    extraHTTPHeaders: {
      'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
      'User-Agent': 'Playwright-Sprint14-E2E-Tests'
    },

    /* Configuration des cookies et stockage */
    storageState: undefined,
    ignoreHTTPSErrors: true
  },

  /* Projets pour différents navigateurs et appareils */
  projects: [
    /* Tests Desktop */
    {
      name: 'chromium-desktop',
      use: {
        ...devices['Desktop Chrome'],
        viewport: { width: 1920, height: 1080 },
        channel: 'chrome'
      },
    },
    {
      name: 'firefox-desktop',
      use: {
        ...devices['Desktop Firefox'],
        viewport: { width: 1920, height: 1080 }
      },
    },
    {
      name: 'webkit-desktop',
      use: {
        ...devices['Desktop Safari'],
        viewport: { width: 1920, height: 1080 }
      },
    },

    /* Tests Mobile */
    {
      name: 'mobile-chrome',
      use: {
        ...devices['Pixel 5'],
      },
    },
    {
      name: 'mobile-safari',
      use: {
        ...devices['iPhone 12'],
      },
    }
  ],

  /* Serveur de développement avec monitoring */
  webServer: process.env.CI ? undefined : {
    command: 'npm run dev',
    port: 3000,
    reuseExistingServer: !process.env.CI,
    timeout: 120000,
    env: {
      NODE_ENV: 'test',
      PLAYWRIGHT_TEST: 'true'
    }
  },

  /* Configuration des dossiers */
  outputDir: 'test-results/',

  /* Expect configuration */
  expect: {
    timeout: 10000,
    toHaveScreenshot: {
      mode: 'strict',
      threshold: 0.2
    },
    toMatchSnapshot: {
      threshold: 0.2
    }
  }
});
