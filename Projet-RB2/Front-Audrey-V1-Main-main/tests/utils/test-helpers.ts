/**
 * Utilitaires de Test E2E - Sprint 14
 * Helpers et fonctions communes pour les tests
 */

import { Page, expect } from '@playwright/test';

/**
 * Authentification rapide pour les tests
 */
export async function loginUser(page: Page, email = '<EMAIL>', password = 'UserPassword123!') {
  await page.goto('/login');
  await page.fill('[data-testid="email-input"]', email);
  await page.fill('[data-testid="password-input"]', password);
  await page.click('[data-testid="submit-login"]');
  await expect(page).toHaveURL(/.*\/dashboard/);
}

/**
 * Attendre qu'une condition soit remplie
 */
export async function waitForCondition(
  page: Page,
  condition: () => Promise<boolean>,
  timeout = 10000,
  interval = 100
): Promise<void> {
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeout) {
    if (await condition()) {
      return;
    }
    await page.waitForTimeout(interval);
  }
  
  throw new Error(`Condition not met within ${timeout}ms`);
}

/**
 * Vérifier les métriques de performance
 */
export async function checkPerformanceMetrics(page: Page) {
  const metrics = await page.evaluate(() => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    return {
      domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
      loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
      firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime || 0,
      firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0
    };
  });
  
  return metrics;
}

/**
 * Prendre une capture d'écran avec timestamp
 */
export async function takeTimestampedScreenshot(page: Page, name: string) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  await page.screenshot({ 
    path: `screenshots/${name}-${timestamp}.png`,
    fullPage: true 
  });
}

/**
 * Vérifier l'accessibilité d'un élément
 */
export async function checkElementAccessibility(page: Page, selector: string) {
  const element = page.locator(selector);
  
  // Vérifier que l'élément est visible
  await expect(element).toBeVisible();
  
  // Vérifier les attributs d'accessibilité
  const ariaLabel = await element.getAttribute('aria-label');
  const ariaLabelledby = await element.getAttribute('aria-labelledby');
  const role = await element.getAttribute('role');
  const tabindex = await element.getAttribute('tabindex');
  
  return {
    hasAriaLabel: !!ariaLabel,
    hasAriaLabelledby: !!ariaLabelledby,
    hasRole: !!role,
    isFocusable: tabindex !== '-1'
  };
}

/**
 * Simuler une connexion lente
 */
export async function simulateSlowConnection(page: Page) {
  const client = await page.context().newCDPSession(page);
  await client.send('Network.emulateNetworkConditions', {
    offline: false,
    downloadThroughput: 50 * 1024, // 50 KB/s
    uploadThroughput: 20 * 1024,   // 20 KB/s
    latency: 500 // 500ms
  });
}

/**
 * Restaurer la connexion normale
 */
export async function restoreNormalConnection(page: Page) {
  const client = await page.context().newCDPSession(page);
  await client.send('Network.emulateNetworkConditions', {
    offline: false,
    downloadThroughput: -1,
    uploadThroughput: -1,
    latency: 0
  });
}

/**
 * Vérifier les erreurs de console
 */
export async function checkConsoleErrors(page: Page): Promise<string[]> {
  const errors: string[] = [];
  
  page.on('console', msg => {
    if (msg.type() === 'error') {
      errors.push(msg.text());
    }
  });
  
  return errors;
}

/**
 * Attendre le chargement complet des images
 */
export async function waitForImagesLoaded(page: Page) {
  await page.evaluate(() => {
    return Promise.all(
      Array.from(document.images)
        .filter(img => !img.complete)
        .map(img => new Promise(resolve => {
          img.onload = img.onerror = resolve;
        }))
    );
  });
}

/**
 * Générer des données de test aléatoires
 */
export function generateTestData() {
  const timestamp = Date.now();
  return {
    email: `test${timestamp}@retreatandbe.com`,
    firstName: `Test${timestamp}`,
    lastName: `User${timestamp}`,
    phone: `+33${Math.floor(Math.random() * 1000000000)}`,
    address: `${Math.floor(Math.random() * 999)} Test Street`,
    city: 'Paris',
    postalCode: `${Math.floor(Math.random() * 99999)}`.padStart(5, '0')
  };
}

/**
 * Vérifier la responsivité sur différentes tailles d'écran
 */
export async function checkResponsiveness(page: Page, url: string) {
  const viewports = [
    { width: 375, height: 667, name: 'mobile' },
    { width: 768, height: 1024, name: 'tablet' },
    { width: 1920, height: 1080, name: 'desktop' }
  ];
  
  const results = [];
  
  for (const viewport of viewports) {
    await page.setViewportSize(viewport);
    await page.goto(url);
    await page.waitForLoadState('networkidle');
    
    const metrics = await checkPerformanceMetrics(page);
    results.push({
      viewport: viewport.name,
      ...metrics
    });
  }
  
  return results;
}

/**
 * Intercepter et analyser les requêtes réseau
 */
export async function analyzeNetworkRequests(page: Page) {
  const requests: any[] = [];
  
  page.on('request', request => {
    requests.push({
      url: request.url(),
      method: request.method(),
      resourceType: request.resourceType(),
      timestamp: Date.now()
    });
  });
  
  page.on('response', response => {
    const request = requests.find(req => req.url === response.url());
    if (request) {
      request.status = response.status();
      request.responseTime = Date.now() - request.timestamp;
      request.size = response.headers()['content-length'] || 0;
    }
  });
  
  return requests;
}

/**
 * Vérifier la sécurité des formulaires
 */
export async function checkFormSecurity(page: Page, formSelector: string) {
  const form = page.locator(formSelector);
  
  // Vérifier HTTPS
  const url = page.url();
  expect(url).toMatch(/^https:/);
  
  // Vérifier les attributs de sécurité
  const method = await form.getAttribute('method');
  const action = await form.getAttribute('action');
  
  // Vérifier les champs de mot de passe
  const passwordFields = await form.locator('input[type="password"]').all();
  for (const field of passwordFields) {
    const autocomplete = await field.getAttribute('autocomplete');
    expect(autocomplete).toBeTruthy();
  }
  
  return {
    isHttps: url.startsWith('https:'),
    method,
    action,
    hasPasswordFields: passwordFields.length > 0
  };
}

/**
 * Tester la navigation au clavier
 */
export async function testKeyboardNavigation(page: Page) {
  const focusableElements = await page.locator(
    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
  ).all();
  
  const results = [];
  
  for (let i = 0; i < Math.min(focusableElements.length, 10); i++) {
    await page.keyboard.press('Tab');
    const focused = await page.locator(':focus').first();
    const isVisible = await focused.isVisible();
    
    results.push({
      index: i,
      isVisible,
      tagName: await focused.evaluate(el => el.tagName),
      hasOutline: await focused.evaluate(el => {
        const style = window.getComputedStyle(el);
        return style.outline !== 'none' && style.outline !== '';
      })
    });
  }
  
  return results;
}

/**
 * Vérifier les Core Web Vitals
 */
export async function measureCoreWebVitals(page: Page) {
  return await page.evaluate(() => {
    return new Promise((resolve) => {
      const vitals: any = {};
      
      // First Contentful Paint
      new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
        if (fcpEntry) {
          vitals.fcp = fcpEntry.startTime;
        }
      }).observe({ entryTypes: ['paint'] });
      
      // Largest Contentful Paint
      new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        vitals.lcp = lastEntry.startTime;
      }).observe({ entryTypes: ['largest-contentful-paint'] });
      
      // Cumulative Layout Shift
      let clsValue = 0;
      new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value;
          }
        }
        vitals.cls = clsValue;
      }).observe({ entryTypes: ['layout-shift'] });
      
      // Résoudre après 3 secondes
      setTimeout(() => resolve(vitals), 3000);
    });
  });
}

/**
 * Nettoyer les données de test
 */
export async function cleanupTestData(page: Page, testEmail: string) {
  // Cette fonction devrait appeler une API de nettoyage
  // ou supprimer les données de test créées
  console.log(`Nettoyage des données pour: ${testEmail}`);
}
