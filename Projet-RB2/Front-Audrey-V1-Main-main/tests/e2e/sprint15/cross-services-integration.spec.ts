/**
 * Tests E2E - Intégration Cross-Services Sprint 15
 * Validation de la navigation et cohérence entre microservices
 */

import { test, expect } from '@playwright/test';
import { loginUser } from '../../utils/test-helpers';

test.describe('Intégration Cross-Services Sprint 15', () => {
  test.beforeEach(async ({ page }) => {
    // Connexion utilisateur pour accéder aux services
    await loginUser(page);
  });

  test('Navigation entre microservices via header unifié', async ({ page }) => {
    // Vérifier la présence du header unifié
    await expect(page.locator('[data-testid="unified-header"]')).toBeVisible();
    
    // Vérifier les services disponibles
    const services = ['Accueil', 'Assistant IA', 'Finances', 'Social'];
    for (const service of services) {
      await expect(page.locator(`[data-testid="service-${service.toLowerCase().replace(' ', '-')}"]`)).toBeVisible();
    }

    // Navigation vers Assistant IA
    await page.click('[data-testid="service-assistant-ia"]');
    await expect(page).toHaveURL(/.*\/ai/);
    await expect(page.locator('[data-testid="chat-window"]')).toBeVisible();

    // Vérifier la cohérence du design
    await expect(page.locator('.unified-header')).toHaveCSS('background-color', 'rgb(255, 255, 255)');
    
    // Navigation vers Finances
    await page.click('[data-testid="service-finances"]');
    await expect(page).toHaveURL(/.*\/financial/);
    await expect(page.locator('[data-testid="financial-dashboard"]')).toBeVisible();

    // Navigation vers Social
    await page.click('[data-testid="service-social"]');
    await expect(page).toHaveURL(/.*\/social/);
    await expect(page.locator('[data-testid="social-feed"]')).toBeVisible();

    // Retour à l'accueil
    await page.click('[data-testid="service-accueil"]');
    await expect(page).toHaveURL(/.*\//);
  });

  test('Persistance de l\'état utilisateur entre services', async ({ page }) => {
    // Vérifier les informations utilisateur dans le header
    await expect(page.locator('[data-testid="user-avatar"]')).toBeVisible();
    await expect(page.locator('[data-testid="user-name"]')).toContainText('Test User');

    // Navigation vers Assistant IA
    await page.click('[data-testid="service-assistant-ia"]');
    
    // Vérifier que l'utilisateur est toujours connecté
    await expect(page.locator('[data-testid="user-avatar"]')).toBeVisible();
    await expect(page.locator('[data-testid="user-name"]')).toContainText('Test User');

    // Navigation vers Finances
    await page.click('[data-testid="service-finances"]');
    
    // Vérifier la persistance
    await expect(page.locator('[data-testid="user-avatar"]')).toBeVisible();
    await expect(page.locator('[data-testid="user-name"]')).toContainText('Test User');
  });

  test('Cohérence visuelle du Design System', async ({ page }) => {
    // Vérifier les tokens de couleur
    const primaryButton = page.locator('[data-testid="primary-button"]').first();
    await expect(primaryButton).toHaveCSS('background-color', 'rgb(14, 165, 233)'); // primary-500

    // Vérifier la typographie
    const heading = page.locator('h1').first();
    await expect(heading).toHaveCSS('font-family', /Inter/);

    // Navigation vers Assistant IA
    await page.click('[data-testid="service-assistant-ia"]');
    
    // Vérifier la cohérence des couleurs
    const aiButton = page.locator('[data-testid="send-message-button"]');
    await expect(aiButton).toHaveCSS('background-color', 'rgb(14, 165, 233)');

    // Navigation vers Finances
    await page.click('[data-testid="service-finances"]');
    
    // Vérifier la cohérence
    const financialButton = page.locator('[data-testid="financial-action-button"]').first();
    await expect(financialButton).toHaveCSS('background-color', 'rgb(14, 165, 233)');
  });

  test('Breadcrumbs contextuels', async ({ page }) => {
    // Navigation profonde dans l'Assistant IA
    await page.click('[data-testid="service-assistant-ia"]');
    await page.click('[data-testid="chat-settings"]');
    
    // Vérifier les breadcrumbs
    await expect(page.locator('[data-testid="breadcrumb"]')).toBeVisible();
    await expect(page.locator('[data-testid="breadcrumb"]')).toContainText('Accueil');
    await expect(page.locator('[data-testid="breadcrumb"]')).toContainText('Assistant IA');
    await expect(page.locator('[data-testid="breadcrumb"]')).toContainText('Paramètres');

    // Navigation via breadcrumb
    await page.click('[data-testid="breadcrumb-assistant-ia"]');
    await expect(page).toHaveURL(/.*\/ai$/);
  });

  test('Notifications cross-services', async ({ page }) => {
    // Vérifier l'icône de notifications
    await expect(page.locator('[data-testid="notifications-bell"]')).toBeVisible();
    
    // Cliquer sur les notifications
    await page.click('[data-testid="notifications-bell"]');
    await expect(page.locator('[data-testid="notifications-dropdown"]')).toBeVisible();

    // Navigation vers Assistant IA
    await page.click('[data-testid="service-assistant-ia"]');
    
    // Vérifier que les notifications sont toujours accessibles
    await expect(page.locator('[data-testid="notifications-bell"]')).toBeVisible();
    
    // Simuler une nouvelle notification depuis l'IA
    await page.evaluate(() => {
      window.dispatchEvent(new CustomEvent('new-notification', {
        detail: { message: 'Nouveau message IA', type: 'info' }
      }));
    });

    // Vérifier le badge de notification
    await expect(page.locator('[data-testid="notification-badge"]')).toBeVisible();
  });

  test('Responsive design cross-services', async ({ page }) => {
    // Test sur mobile
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Vérifier le menu mobile
    await expect(page.locator('[data-testid="mobile-menu-button"]')).toBeVisible();
    
    // Ouvrir le menu mobile
    await page.click('[data-testid="mobile-menu-button"]');
    await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible();

    // Navigation mobile vers Assistant IA
    await page.click('[data-testid="mobile-service-assistant-ia"]');
    await expect(page).toHaveURL(/.*\/ai/);
    
    // Vérifier l'adaptation mobile du chat
    await expect(page.locator('[data-testid="chat-window"]')).toBeVisible();
    await expect(page.locator('[data-testid="chat-input"]')).toBeVisible();

    // Test sur tablette
    await page.setViewportSize({ width: 768, height: 1024 });
    
    // Vérifier l'adaptation tablette
    await expect(page.locator('[data-testid="unified-header"]')).toBeVisible();
    await expect(page.locator('[data-testid="service-navigation"]')).toBeVisible();
  });

  test('Performance de navigation entre services', async ({ page }) => {
    // Mesurer le temps de navigation
    const startTime = Date.now();
    
    await page.click('[data-testid="service-assistant-ia"]');
    await page.waitForLoadState('networkidle');
    
    const navigationTime = Date.now() - startTime;
    
    // La navigation doit prendre moins de 500ms
    expect(navigationTime).toBeLessThan(500);

    // Vérifier l'absence de rechargement complet
    const performanceEntries = await page.evaluate(() => {
      return performance.getEntriesByType('navigation').length;
    });
    
    // Doit être 1 (chargement initial seulement)
    expect(performanceEntries).toBe(1);
  });

  test('Gestion des erreurs cross-services', async ({ page }) => {
    // Simuler une erreur réseau
    await page.route('**/api/ai/**', route => route.abort());
    
    // Navigation vers Assistant IA
    await page.click('[data-testid="service-assistant-ia"]');
    
    // Vérifier l'affichage d'erreur
    await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="error-message"]')).toContainText('Service temporairement indisponible');

    // Vérifier que la navigation reste fonctionnelle
    await page.click('[data-testid="service-accueil"]');
    await expect(page).toHaveURL(/.*\/$/);
  });

  test('Sécurité et authentification cross-services', async ({ page }) => {
    // Vérifier les headers de sécurité
    const response = await page.goto('/ai');
    const headers = response?.headers();
    
    expect(headers?.['x-frame-options']).toBe('DENY');
    expect(headers?.['x-content-type-options']).toBe('nosniff');

    // Vérifier la persistance du token JWT
    const token = await page.evaluate(() => localStorage.getItem('auth-token'));
    expect(token).toBeTruthy();

    // Navigation vers service sécurisé
    await page.click('[data-testid="service-finances"]');
    
    // Vérifier que le token est toujours présent
    const tokenAfterNavigation = await page.evaluate(() => localStorage.getItem('auth-token'));
    expect(tokenAfterNavigation).toBe(token);
  });

  test('Accessibilité cross-services', async ({ page }) => {
    // Vérifier la navigation au clavier
    await page.keyboard.press('Tab');
    await expect(page.locator(':focus')).toBeVisible();

    // Navigation clavier vers Assistant IA
    await page.keyboard.press('Enter');
    
    // Vérifier le focus management
    await expect(page.locator('[data-testid="chat-input"]')).toBeFocused();

    // Vérifier les attributs ARIA
    await expect(page.locator('[data-testid="unified-header"]')).toHaveAttribute('role', 'banner');
    await expect(page.locator('[data-testid="service-navigation"]')).toHaveAttribute('role', 'navigation');
  });

  test('Intégration des données cross-services', async ({ page }) => {
    // Créer une conversation dans l'Assistant IA
    await page.click('[data-testid="service-assistant-ia"]');
    await page.fill('[data-testid="chat-input"]', 'Bonjour, je cherche une retraite');
    await page.click('[data-testid="send-message-button"]');
    
    // Attendre la réponse
    await expect(page.locator('[data-testid="ai-message"]').last()).toBeVisible();

    // Navigation vers les finances
    await page.click('[data-testid="service-finances"]');
    
    // Vérifier que les données utilisateur sont cohérentes
    await expect(page.locator('[data-testid="user-balance"]')).toBeVisible();
    
    // Navigation vers social
    await page.click('[data-testid="service-social"]');
    
    // Vérifier la cohérence du profil
    await expect(page.locator('[data-testid="user-profile"]')).toBeVisible();
    await expect(page.locator('[data-testid="user-name"]')).toContainText('Test User');
  });

  test('Monitoring et métriques cross-services', async ({ page }) => {
    // Intercepter les appels de métriques
    const metricsRequests: string[] = [];
    
    page.on('request', request => {
      if (request.url().includes('/metrics') || request.url().includes('/analytics')) {
        metricsRequests.push(request.url());
      }
    });

    // Navigation entre services
    await page.click('[data-testid="service-assistant-ia"]');
    await page.click('[data-testid="service-finances"]');
    await page.click('[data-testid="service-social"]');

    // Vérifier que les métriques sont collectées
    expect(metricsRequests.length).toBeGreaterThan(0);
    
    // Vérifier les métriques de performance
    const performanceMetrics = await page.evaluate(() => {
      return {
        navigation: performance.getEntriesByType('navigation')[0],
        paint: performance.getEntriesByType('paint')
      };
    });
    
    expect(performanceMetrics.navigation).toBeTruthy();
    expect(performanceMetrics.paint.length).toBeGreaterThan(0);
  });
});
