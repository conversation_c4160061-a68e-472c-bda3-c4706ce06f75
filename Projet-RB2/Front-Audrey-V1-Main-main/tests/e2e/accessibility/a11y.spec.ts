/**
 * Tests E2E - Accessibilité (A11Y)
 * Sprint 14 - Tests E2E Complets
 */

import { test, expect } from '@playwright/test';
import AxeBuilder from '@axe-core/playwright';

test.describe('Tests d\'Accessibilité', () => {
  test('Accessibilité de la page d\'accueil', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
      .analyze();
    
    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('Accessibilité des formulaires d\'authentification', async ({ page }) => {
    await page.goto('/login');
    
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa'])
      .analyze();
    
    expect(accessibilityScanResults.violations).toEqual([]);
    
    // Vérifier les labels des champs
    await expect(page.locator('label[for="email"]')).toBeVisible();
    await expect(page.locator('label[for="password"]')).toBeVisible();
    
    // Vérifier les attributs ARIA
    await expect(page.locator('[data-testid="email-input"]')).toHaveAttribute('aria-required', 'true');
    await expect(page.locator('[data-testid="password-input"]')).toHaveAttribute('aria-required', 'true');
  });

  test('Navigation au clavier', async ({ page }) => {
    await page.goto('/');
    
    // Tester la navigation avec Tab
    await page.keyboard.press('Tab');
    const firstFocusable = await page.locator(':focus').first();
    await expect(firstFocusable).toBeVisible();
    
    // Continuer la navigation
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    
    // Vérifier que le focus est visible
    const focusedElement = await page.locator(':focus').first();
    await expect(focusedElement).toBeVisible();
    
    // Tester la navigation inverse avec Shift+Tab
    await page.keyboard.press('Shift+Tab');
    const previousElement = await page.locator(':focus').first();
    await expect(previousElement).toBeVisible();
  });

  test('Contraste des couleurs', async ({ page }) => {
    await page.goto('/');
    
    const contrastResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
      .analyze();
    
    const contrastViolations = contrastResults.violations.filter(
      violation => violation.id === 'color-contrast'
    );
    
    expect(contrastViolations).toEqual([]);
  });

  test('Textes alternatifs des images', async ({ page }) => {
    await page.goto('/');
    
    const images = await page.locator('img').all();
    
    for (const img of images) {
      const alt = await img.getAttribute('alt');
      const ariaLabel = await img.getAttribute('aria-label');
      const role = await img.getAttribute('role');
      
      // Chaque image doit avoir un texte alternatif ou être décorative
      if (role !== 'presentation' && !alt && !ariaLabel) {
        const src = await img.getAttribute('src');
        throw new Error(`Image sans texte alternatif: ${src}`);
      }
    }
  });

  test('Structure des titres (headings)', async ({ page }) => {
    await page.goto('/');
    
    const headings = await page.locator('h1, h2, h3, h4, h5, h6').all();
    
    // Vérifier qu'il y a au moins un H1
    const h1Count = await page.locator('h1').count();
    expect(h1Count).toBeGreaterThanOrEqual(1);
    
    // Vérifier la hiérarchie des titres
    let previousLevel = 0;
    for (const heading of headings) {
      const tagName = await heading.evaluate(el => el.tagName.toLowerCase());
      const currentLevel = parseInt(tagName.charAt(1));
      
      if (previousLevel > 0) {
        // Le niveau ne doit pas sauter plus d'un niveau
        expect(currentLevel - previousLevel).toBeLessThanOrEqual(1);
      }
      
      previousLevel = currentLevel;
    }
  });

  test('Accessibilité des boutons et liens', async ({ page }) => {
    await page.goto('/');
    
    // Vérifier que tous les boutons ont un texte accessible
    const buttons = await page.locator('button').all();
    for (const button of buttons) {
      const text = await button.textContent();
      const ariaLabel = await button.getAttribute('aria-label');
      const ariaLabelledby = await button.getAttribute('aria-labelledby');
      
      expect(text || ariaLabel || ariaLabelledby).toBeTruthy();
    }
    
    // Vérifier que tous les liens ont un texte accessible
    const links = await page.locator('a').all();
    for (const link of links) {
      const text = await link.textContent();
      const ariaLabel = await link.getAttribute('aria-label');
      
      expect(text || ariaLabel).toBeTruthy();
    }
  });

  test('Accessibilité des formulaires', async ({ page }) => {
    await page.goto('/register');
    
    // Vérifier que tous les champs ont des labels
    const inputs = await page.locator('input[type="text"], input[type="email"], input[type="password"]').all();
    
    for (const input of inputs) {
      const id = await input.getAttribute('id');
      const ariaLabel = await input.getAttribute('aria-label');
      const ariaLabelledby = await input.getAttribute('aria-labelledby');
      
      if (id) {
        const label = await page.locator(`label[for="${id}"]`).count();
        expect(label > 0 || ariaLabel || ariaLabelledby).toBeTruthy();
      } else {
        expect(ariaLabel || ariaLabelledby).toBeTruthy();
      }
    }
  });

  test('Accessibilité des messages d\'erreur', async ({ page }) => {
    await page.goto('/login');
    
    // Soumettre le formulaire vide pour déclencher les erreurs
    await page.click('[data-testid="submit-login"]');
    
    // Vérifier que les erreurs sont annoncées
    const errorMessages = await page.locator('[role="alert"], [aria-live="polite"], [aria-live="assertive"]').all();
    expect(errorMessages.length).toBeGreaterThan(0);
    
    // Vérifier que les champs en erreur sont correctement marqués
    const invalidInputs = await page.locator('[aria-invalid="true"]').all();
    expect(invalidInputs.length).toBeGreaterThan(0);
  });

  test('Accessibilité des modales et overlays', async ({ page }) => {
    await page.goto('/');
    
    // Ouvrir une modale (si elle existe)
    const modalTrigger = page.locator('[data-testid="open-modal"]');
    if (await modalTrigger.count() > 0) {
      await modalTrigger.click();
      
      // Vérifier que la modale a le bon rôle
      await expect(page.locator('[role="dialog"]')).toBeVisible();
      
      // Vérifier que le focus est piégé dans la modale
      await page.keyboard.press('Tab');
      const focusedElement = await page.locator(':focus').first();
      const modalContainer = page.locator('[role="dialog"]');
      
      // Le focus doit être dans la modale
      expect(await modalContainer.locator(':focus').count()).toBeGreaterThan(0);
      
      // Fermer avec Escape
      await page.keyboard.press('Escape');
      await expect(page.locator('[role="dialog"]')).not.toBeVisible();
    }
  });

  test('Accessibilité des listes et navigation', async ({ page }) => {
    await page.goto('/');
    
    // Vérifier les listes de navigation
    const navLists = await page.locator('nav ul, nav ol').all();
    
    for (const list of navLists) {
      // Vérifier que les listes ont le bon rôle
      const role = await list.getAttribute('role');
      expect(role === 'list' || role === null).toBeTruthy();
      
      // Vérifier que les éléments de liste sont corrects
      const listItems = await list.locator('li').all();
      expect(listItems.length).toBeGreaterThan(0);
    }
  });

  test('Accessibilité responsive', async ({ page }) => {
    // Tester sur mobile
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/');
    
    const mobileResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa'])
      .analyze();
    
    expect(mobileResults.violations).toEqual([]);
    
    // Tester sur tablette
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.reload();
    
    const tabletResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa'])
      .analyze();
    
    expect(tabletResults.violations).toEqual([]);
  });

  test('Accessibilité des éléments interactifs', async ({ page }) => {
    await page.goto('/search');
    
    // Vérifier les éléments de formulaire complexes
    const selects = await page.locator('select').all();
    for (const select of selects) {
      const ariaLabel = await select.getAttribute('aria-label');
      const id = await select.getAttribute('id');
      
      if (id) {
        const label = await page.locator(`label[for="${id}"]`).count();
        expect(label > 0 || ariaLabel).toBeTruthy();
      }
    }
    
    // Vérifier les boutons radio et checkboxes
    const radioButtons = await page.locator('input[type="radio"]').all();
    for (const radio of radioButtons) {
      const name = await radio.getAttribute('name');
      expect(name).toBeTruthy();
    }
  });

  test('Accessibilité du contenu dynamique', async ({ page }) => {
    await page.goto('/search');
    
    // Effectuer une recherche pour générer du contenu dynamique
    await page.fill('[data-testid="location-search"]', 'Bali');
    await page.click('[data-testid="search-button"]');
    
    // Attendre que les résultats se chargent
    await page.waitForSelector('[data-testid="search-results"]');
    
    // Vérifier l'accessibilité du contenu généré
    const dynamicResults = await new AxeBuilder({ page })
      .include('[data-testid="search-results"]')
      .analyze();
    
    expect(dynamicResults.violations).toEqual([]);
    
    // Vérifier que les changements sont annoncés
    const liveRegions = await page.locator('[aria-live], [role="status"], [role="alert"]').count();
    expect(liveRegions).toBeGreaterThan(0);
  });

  test('Accessibilité des tableaux de données', async ({ page }) => {
    // Aller à une page avec des tableaux (ex: dashboard)
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'UserPassword123!');
    await page.click('[data-testid="submit-login"]');
    
    await page.goto('/dashboard/bookings');
    
    const tables = await page.locator('table').all();
    
    for (const table of tables) {
      // Vérifier les en-têtes de colonnes
      const headers = await table.locator('th').all();
      if (headers.length > 0) {
        for (const header of headers) {
          const scope = await header.getAttribute('scope');
          expect(scope === 'col' || scope === 'row' || scope === null).toBeTruthy();
        }
      }
      
      // Vérifier la caption si nécessaire
      const caption = await table.locator('caption').count();
      const ariaLabel = await table.getAttribute('aria-label');
      const ariaLabelledby = await table.getAttribute('aria-labelledby');
      
      // Au moins une méthode de description doit être présente
      expect(caption > 0 || ariaLabel || ariaLabelledby).toBeTruthy();
    }
  });
});
