/**
 * Tests E2E - Parcours de Réservation Critique
 * Sprint 14 - Tests E2E Complets
 */

import { test, expect } from '@playwright/test';

test.describe('Parcours de Réservation Critique', () => {
  test.beforeEach(async ({ page }) => {
    // Se connecter avant chaque test
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'UserPassword123!');
    await page.click('[data-testid="submit-login"]');
    await expect(page).toHaveURL(/.*\/dashboard/);
  });

  test('Recherche et sélection d\'une retraite', async ({ page }) => {
    // Aller à la page de recherche
    await page.goto('/search');
    
    // Utiliser les filtres de recherche
    await page.fill('[data-testid="location-search"]', 'Bali');
    await page.selectOption('[data-testid="retreat-type"]', 'yoga');
    await page.fill('[data-testid="date-from"]', '2025-07-01');
    await page.fill('[data-testid="date-to"]', '2025-07-07');
    await page.selectOption('[data-testid="price-range"]', '500-1000');

    // Lancer la recherche
    await page.click('[data-testid="search-button"]');
    await page.waitForLoadState('networkidle');

    // Vérifier les résultats
    await expect(page.locator('[data-testid="search-results"]')).toBeVisible();
    await expect(page.locator('[data-testid="retreat-card"]').first()).toBeVisible();

    // Sélectionner une retraite
    await page.click('[data-testid="retreat-card"]').first();
    await expect(page).toHaveURL(/.*\/retreat\/\d+/);
  });

  test('Consultation des détails d\'une retraite', async ({ page }) => {
    // Aller directement à une page de retraite
    await page.goto('/retreat/123');
    await page.waitForLoadState('networkidle');

    // Vérifier les éléments de la page
    await expect(page.locator('[data-testid="retreat-title"]')).toBeVisible();
    await expect(page.locator('[data-testid="retreat-description"]')).toBeVisible();
    await expect(page.locator('[data-testid="retreat-price"]')).toBeVisible();
    await expect(page.locator('[data-testid="retreat-dates"]')).toBeVisible();
    await expect(page.locator('[data-testid="retreat-location"]')).toBeVisible();
    await expect(page.locator('[data-testid="retreat-gallery"]')).toBeVisible();

    // Vérifier les avis et évaluations
    await expect(page.locator('[data-testid="reviews-section"]')).toBeVisible();
    await expect(page.locator('[data-testid="rating-stars"]')).toBeVisible();

    // Vérifier les informations du professeur
    await expect(page.locator('[data-testid="instructor-info"]')).toBeVisible();
  });

  test('Processus de réservation complet', async ({ page }) => {
    // Aller à une page de retraite
    await page.goto('/retreat/123');
    
    // Cliquer sur "Réserver maintenant"
    await page.click('[data-testid="book-now-button"]');
    await expect(page).toHaveURL(/.*\/booking\/123/);

    // Sélectionner les options
    await page.selectOption('[data-testid="room-type"]', 'private');
    await page.selectOption('[data-testid="meal-plan"]', 'vegetarian');
    
    // Ajouter des services supplémentaires
    await page.check('[data-testid="airport-transfer"]');
    await page.check('[data-testid="spa-package"]');

    // Vérifier le récapitulatif
    await expect(page.locator('[data-testid="booking-summary"]')).toBeVisible();
    await expect(page.locator('[data-testid="total-price"]')).toBeVisible();

    // Continuer vers le paiement
    await page.click('[data-testid="proceed-to-payment"]');
    await expect(page).toHaveURL(/.*\/payment\/123/);
  });

  test('Processus de paiement sécurisé', async ({ page }) => {
    // Aller directement à la page de paiement (après réservation)
    await page.goto('/payment/123');
    
    // Vérifier les informations de sécurité
    await expect(page.locator('[data-testid="ssl-indicator"]')).toBeVisible();
    await expect(page.locator('[data-testid="secure-payment-badge"]')).toBeVisible();

    // Remplir les informations de paiement
    await page.fill('[data-testid="card-number"]', '****************');
    await page.fill('[data-testid="card-expiry"]', '12/26');
    await page.fill('[data-testid="card-cvc"]', '123');
    await page.fill('[data-testid="cardholder-name"]', 'Jean Dupont');

    // Remplir l'adresse de facturation
    await page.fill('[data-testid="billing-address"]', '123 Rue de la Paix');
    await page.fill('[data-testid="billing-city"]', 'Paris');
    await page.fill('[data-testid="billing-postal"]', '75001');
    await page.selectOption('[data-testid="billing-country"]', 'FR');

    // Accepter les conditions
    await page.check('[data-testid="payment-terms"]');

    // Effectuer le paiement
    await page.click('[data-testid="complete-payment"]');
    
    // Vérifier la confirmation
    await expect(page).toHaveURL(/.*\/booking-confirmation/);
    await expect(page.locator('[data-testid="booking-success"]')).toBeVisible();
    await expect(page.locator('[data-testid="booking-reference"]')).toBeVisible();
  });

  test('Gestion du panier de réservation', async ({ page }) => {
    // Ajouter plusieurs retraites au panier
    await page.goto('/retreat/123');
    await page.click('[data-testid="add-to-cart"]');
    
    await page.goto('/retreat/456');
    await page.click('[data-testid="add-to-cart"]');

    // Aller au panier
    await page.click('[data-testid="cart-icon"]');
    await expect(page).toHaveURL(/.*\/cart/);

    // Vérifier les éléments du panier
    await expect(page.locator('[data-testid="cart-item"]')).toHaveCount(2);
    await expect(page.locator('[data-testid="cart-total"]')).toBeVisible();

    // Modifier les quantités
    await page.click('[data-testid="remove-item"]').first();
    await expect(page.locator('[data-testid="cart-item"]')).toHaveCount(1);

    // Procéder au checkout
    await page.click('[data-testid="checkout-button"]');
    await expect(page).toHaveURL(/.*\/checkout/);
  });

  test('Annulation et remboursement', async ({ page }) => {
    // Aller aux réservations existantes
    await page.goto('/dashboard/bookings');
    
    // Sélectionner une réservation annulable
    await page.click('[data-testid="booking-item"]').first();
    await expect(page).toHaveURL(/.*\/booking\/\d+/);

    // Vérifier les détails de la réservation
    await expect(page.locator('[data-testid="booking-details"]')).toBeVisible();
    await expect(page.locator('[data-testid="cancellation-policy"]')).toBeVisible();

    // Initier l'annulation
    await page.click('[data-testid="cancel-booking"]');
    
    // Confirmer l'annulation
    await page.fill('[data-testid="cancellation-reason"]', 'Changement de plans personnels');
    await page.click('[data-testid="confirm-cancellation"]');

    // Vérifier la confirmation d'annulation
    await expect(page.locator('[data-testid="cancellation-success"]')).toBeVisible();
    await expect(page.locator('[data-testid="refund-info"]')).toBeVisible();
  });

  test('Notifications et rappels de réservation', async ({ page }) => {
    // Aller au dashboard
    await page.goto('/dashboard');
    
    // Vérifier les notifications
    await expect(page.locator('[data-testid="notifications-panel"]')).toBeVisible();
    
    // Cliquer sur une notification de réservation
    await page.click('[data-testid="booking-notification"]').first();
    
    // Vérifier la redirection vers les détails
    await expect(page).toHaveURL(/.*\/booking\/\d+/);
    
    // Vérifier les rappels
    await expect(page.locator('[data-testid="booking-reminders"]')).toBeVisible();
  });

  test('Évaluation et avis après la retraite', async ({ page }) => {
    // Aller à une réservation terminée
    await page.goto('/booking/completed/123');
    
    // Vérifier le bouton d'évaluation
    await expect(page.locator('[data-testid="leave-review-button"]')).toBeVisible();
    
    // Cliquer pour laisser un avis
    await page.click('[data-testid="leave-review-button"]');
    
    // Remplir l'évaluation
    await page.click('[data-testid="star-rating"] .star:nth-child(5)'); // 5 étoiles
    await page.fill('[data-testid="review-title"]', 'Expérience exceptionnelle');
    await page.fill('[data-testid="review-content"]', 'Cette retraite a dépassé toutes mes attentes...');
    
    // Soumettre l'avis
    await page.click('[data-testid="submit-review"]');
    
    // Vérifier la confirmation
    await expect(page.locator('[data-testid="review-success"]')).toBeVisible();
  });

  test('Gestion des erreurs de réservation', async ({ page }) => {
    // Tenter de réserver une retraite complète
    await page.goto('/retreat/full-retreat');
    
    // Vérifier l'affichage "Complet"
    await expect(page.locator('[data-testid="fully-booked"]')).toBeVisible();
    await expect(page.locator('[data-testid="book-now-button"]')).toBeDisabled();
    
    // Tester la liste d'attente
    await page.click('[data-testid="join-waitlist"]');
    await page.fill('[data-testid="waitlist-email"]', '<EMAIL>');
    await page.click('[data-testid="submit-waitlist"]');
    
    await expect(page.locator('[data-testid="waitlist-success"]')).toBeVisible();
  });

  test('Réservation de groupe', async ({ page }) => {
    // Aller à une retraite avec option groupe
    await page.goto('/retreat/group-retreat');
    
    // Sélectionner l'option groupe
    await page.click('[data-testid="group-booking-tab"]');
    
    // Remplir les détails du groupe
    await page.fill('[data-testid="group-size"]', '8');
    await page.fill('[data-testid="group-leader"]', 'Jean Dupont');
    await page.fill('[data-testid="group-email"]', '<EMAIL>');
    
    // Demander un devis
    await page.click('[data-testid="request-group-quote"]');
    
    // Vérifier la confirmation
    await expect(page.locator('[data-testid="quote-request-success"]')).toBeVisible();
  });
});
