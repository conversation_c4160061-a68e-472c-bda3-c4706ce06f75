/**
 * Tests E2E - Performance et Core Web Vitals
 * Sprint 14 - Tests E2E Complets
 */

import { test, expect } from '@playwright/test';

test.describe('Tests de Performance Critique', () => {
  test('Temps de chargement de la page d\'accueil', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    
    // Vérifier que la page se charge en moins de 3 secondes
    expect(loadTime).toBeLessThan(3000);
    
    // Vérifier les Core Web Vitals
    const metrics = await page.evaluate(() => {
      return new Promise((resolve) => {
        new PerformanceObserver((list) => {
          const entries = list.getEntries();
          resolve(entries);
        }).observe({ entryTypes: ['navigation', 'paint'] });
      });
    });
    
    console.log('Métriques de performance:', metrics);
  });

  test('First Contentful Paint (FCP)', async ({ page }) => {
    await page.goto('/');
    
    const fcp = await page.evaluate(() => {
      return new Promise((resolve) => {
        new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
          resolve(fcpEntry ? fcpEntry.startTime : null);
        }).observe({ entryTypes: ['paint'] });
      });
    });
    
    // FCP doit être inférieur à 1.8 secondes
    expect(fcp).toBeLessThan(1800);
  });

  test('Largest Contentful Paint (LCP)', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    const lcp = await page.evaluate(() => {
      return new Promise((resolve) => {
        new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          resolve(lastEntry ? lastEntry.startTime : null);
        }).observe({ entryTypes: ['largest-contentful-paint'] });
        
        // Timeout après 5 secondes
        setTimeout(() => resolve(null), 5000);
      });
    });
    
    // LCP doit être inférieur à 2.5 secondes
    if (lcp) {
      expect(lcp).toBeLessThan(2500);
    }
  });

  test('Cumulative Layout Shift (CLS)', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Attendre que la page soit complètement chargée
    await page.waitForTimeout(2000);
    
    const cls = await page.evaluate(() => {
      return new Promise((resolve) => {
        let clsValue = 0;
        new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
            }
          }
          resolve(clsValue);
        }).observe({ entryTypes: ['layout-shift'] });
        
        // Timeout après 3 secondes
        setTimeout(() => resolve(clsValue), 3000);
      });
    });
    
    // CLS doit être inférieur à 0.1
    expect(cls).toBeLessThan(0.1);
  });

  test('First Input Delay (FID) simulation', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    const startTime = Date.now();
    
    // Simuler une interaction utilisateur
    await page.click('[data-testid="search-button"]');
    
    const responseTime = Date.now() - startTime;
    
    // Le temps de réponse doit être inférieur à 100ms
    expect(responseTime).toBeLessThan(100);
  });

  test('Performance de la recherche', async ({ page }) => {
    await page.goto('/search');
    
    const startTime = Date.now();
    
    // Effectuer une recherche
    await page.fill('[data-testid="location-search"]', 'Bali');
    await page.click('[data-testid="search-button"]');
    await page.waitForSelector('[data-testid="search-results"]');
    
    const searchTime = Date.now() - startTime;
    
    // La recherche doit prendre moins de 2 secondes
    expect(searchTime).toBeLessThan(2000);
  });

  test('Performance du chargement des images', async ({ page }) => {
    await page.goto('/');
    
    // Vérifier que les images sont optimisées
    const images = await page.locator('img').all();
    
    for (const img of images) {
      const src = await img.getAttribute('src');
      const loading = await img.getAttribute('loading');
      
      // Vérifier le lazy loading
      if (src && !src.includes('data:')) {
        expect(loading).toBe('lazy');
      }
    }
  });

  test('Performance de navigation entre pages', async ({ page }) => {
    await page.goto('/');
    
    const startTime = Date.now();
    
    // Naviguer vers une autre page
    await page.click('[data-testid="about-link"]');
    await page.waitForLoadState('networkidle');
    
    const navigationTime = Date.now() - startTime;
    
    // La navigation doit prendre moins de 1.5 secondes
    expect(navigationTime).toBeLessThan(1500);
  });

  test('Performance du formulaire de réservation', async ({ page }) => {
    // Se connecter d'abord
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'UserPassword123!');
    await page.click('[data-testid="submit-login"]');
    
    await page.goto('/retreat/123');
    
    const startTime = Date.now();
    
    // Cliquer sur réserver
    await page.click('[data-testid="book-now-button"]');
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    
    // Le formulaire doit se charger en moins de 1 seconde
    expect(loadTime).toBeLessThan(1000);
  });

  test('Performance de l\'API de recherche', async ({ page }) => {
    await page.goto('/search');
    
    // Intercepter les requêtes API
    const apiResponses: number[] = [];
    
    page.on('response', response => {
      if (response.url().includes('/api/search')) {
        const responseTime = response.timing().responseEnd - response.timing().requestStart;
        apiResponses.push(responseTime);
      }
    });
    
    // Effectuer plusieurs recherches
    await page.fill('[data-testid="location-search"]', 'Bali');
    await page.click('[data-testid="search-button"]');
    await page.waitForResponse(response => response.url().includes('/api/search'));
    
    // Vérifier que l'API répond rapidement
    expect(apiResponses[0]).toBeLessThan(500); // Moins de 500ms
  });

  test('Performance du cache et ressources statiques', async ({ page }) => {
    // Premier chargement
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Recharger la page
    const startTime = Date.now();
    await page.reload();
    await page.waitForLoadState('networkidle');
    const reloadTime = Date.now() - startTime;
    
    // Le rechargement doit être plus rapide grâce au cache
    expect(reloadTime).toBeLessThan(1000);
  });

  test('Performance mobile', async ({ page, browserName }) => {
    // Simuler un appareil mobile
    await page.setViewportSize({ width: 375, height: 667 });
    
    const startTime = Date.now();
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    const loadTime = Date.now() - startTime;
    
    // Sur mobile, accepter un temps de chargement légèrement plus long
    expect(loadTime).toBeLessThan(4000);
  });

  test('Performance de défilement et animations', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Mesurer la fluidité du défilement
    const scrollPerformance = await page.evaluate(() => {
      return new Promise((resolve) => {
        let frameCount = 0;
        const startTime = performance.now();
        
        function countFrames() {
          frameCount++;
          if (performance.now() - startTime < 1000) {
            requestAnimationFrame(countFrames);
          } else {
            resolve(frameCount);
          }
        }
        
        // Démarrer le défilement
        window.scrollTo(0, 1000);
        requestAnimationFrame(countFrames);
      });
    });
    
    // Vérifier un taux de rafraîchissement acceptable (>30 FPS)
    expect(scrollPerformance).toBeGreaterThan(30);
  });

  test('Analyse des ressources réseau', async ({ page }) => {
    const resourceSizes: { [key: string]: number } = {};
    
    page.on('response', async response => {
      const url = response.url();
      const headers = response.headers();
      const contentLength = headers['content-length'];
      
      if (contentLength) {
        resourceSizes[url] = parseInt(contentLength);
      }
    });
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Vérifier que les ressources ne sont pas trop lourdes
    for (const [url, size] of Object.entries(resourceSizes)) {
      if (url.includes('.js')) {
        expect(size).toBeLessThan(500000); // JS < 500KB
      }
      if (url.includes('.css')) {
        expect(size).toBeLessThan(100000); // CSS < 100KB
      }
      if (url.includes('.jpg') || url.includes('.png')) {
        expect(size).toBeLessThan(200000); // Images < 200KB
      }
    }
  });

  test('Performance de la recherche en temps réel', async ({ page }) => {
    await page.goto('/search');
    
    const searchInput = page.locator('[data-testid="location-search"]');
    
    // Mesurer le temps de réponse de la recherche en temps réel
    const startTime = Date.now();
    
    await searchInput.fill('B');
    await page.waitForSelector('[data-testid="search-suggestions"]');
    
    const responseTime = Date.now() - startTime;
    
    // Les suggestions doivent apparaître rapidement
    expect(responseTime).toBeLessThan(300);
  });
});
