/**
 * Tests E2E - Parcours d'Authentification Critique
 * Sprint 14 - Tests E2E Complets
 */

import { test, expect } from '@playwright/test';

test.describe('Parcours d\'Authentification Critique', () => {
  test.beforeEach(async ({ page }) => {
    // Navigation vers la page d'accueil
    await page.goto('/');
    await page.waitForLoadState('networkidle');
  });

  test('Inscription utilisateur complète', async ({ page }) => {
    // Cliquer sur le bouton d'inscription
    await page.click('[data-testid="signup-button"]');
    await expect(page).toHaveURL(/.*\/register/);

    // Remplir le formulaire d'inscription
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'SecurePassword123!');
    await page.fill('[data-testid="confirm-password-input"]', 'SecurePassword123!');
    await page.fill('[data-testid="firstname-input"]', 'Jean');
    await page.fill('[data-testid="lastname-input"]', 'Dupont');

    // Accepter les conditions
    await page.check('[data-testid="terms-checkbox"]');
    await page.check('[data-testid="privacy-checkbox"]');

    // Soumettre le formulaire
    await page.click('[data-testid="submit-registration"]');

    // Vérifier la redirection vers la vérification email
    await expect(page).toHaveURL(/.*\/verify-email/);
    await expect(page.locator('[data-testid="verification-message"]')).toBeVisible();
  });

  test('Connexion utilisateur existant', async ({ page }) => {
    // Cliquer sur le bouton de connexion
    await page.click('[data-testid="login-button"]');
    await expect(page).toHaveURL(/.*\/login/);

    // Remplir les identifiants
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'UserPassword123!');

    // Se connecter
    await page.click('[data-testid="submit-login"]');

    // Vérifier la redirection vers le dashboard
    await expect(page).toHaveURL(/.*\/dashboard/);
    await expect(page.locator('[data-testid="user-welcome"]')).toBeVisible();
  });

  test('Authentification à deux facteurs (2FA)', async ({ page }) => {
    // Se connecter avec un compte ayant 2FA activé
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'User2FA123!');
    await page.click('[data-testid="submit-login"]');

    // Vérifier l'affichage de la page 2FA
    await expect(page).toHaveURL(/.*\/two-factor/);
    await expect(page.locator('[data-testid="2fa-form"]')).toBeVisible();

    // Saisir le code 2FA (simulé)
    await page.fill('[data-testid="2fa-code-input"]', '123456');
    await page.click('[data-testid="submit-2fa"]');

    // Vérifier la connexion réussie
    await expect(page).toHaveURL(/.*\/dashboard/);
  });

  test('Récupération de mot de passe', async ({ page }) => {
    // Aller à la page de connexion
    await page.goto('/login');
    
    // Cliquer sur "Mot de passe oublié"
    await page.click('[data-testid="forgot-password-link"]');
    await expect(page).toHaveURL(/.*\/forgot-password/);

    // Saisir l'email
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.click('[data-testid="submit-forgot-password"]');

    // Vérifier le message de confirmation
    await expect(page.locator('[data-testid="reset-email-sent"]')).toBeVisible();
  });

  test('Déconnexion utilisateur', async ({ page }) => {
    // Se connecter d'abord
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'UserPassword123!');
    await page.click('[data-testid="submit-login"]');
    
    await expect(page).toHaveURL(/.*\/dashboard/);

    // Se déconnecter
    await page.click('[data-testid="user-menu"]');
    await page.click('[data-testid="logout-button"]');

    // Vérifier la redirection vers l'accueil
    await expect(page).toHaveURL('/');
    await expect(page.locator('[data-testid="login-button"]')).toBeVisible();
  });

  test('Gestion des erreurs d\'authentification', async ({ page }) => {
    // Tentative de connexion avec des identifiants incorrects
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'WrongPassword');
    await page.click('[data-testid="submit-login"]');

    // Vérifier l'affichage de l'erreur
    await expect(page.locator('[data-testid="login-error"]')).toBeVisible();
    await expect(page.locator('[data-testid="login-error"]')).toContainText('Identifiants incorrects');
  });

  test('Protection des routes authentifiées', async ({ page }) => {
    // Tentative d'accès direct au dashboard sans être connecté
    await page.goto('/dashboard');

    // Vérifier la redirection vers la page de connexion
    await expect(page).toHaveURL(/.*\/login/);
    await expect(page.locator('[data-testid="login-required-message"]')).toBeVisible();
  });

  test('Persistance de session', async ({ page, context }) => {
    // Se connecter
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'UserPassword123!');
    await page.click('[data-testid="submit-login"]');
    
    await expect(page).toHaveURL(/.*\/dashboard/);

    // Ouvrir un nouvel onglet
    const newPage = await context.newPage();
    await newPage.goto('/dashboard');

    // Vérifier que l'utilisateur est toujours connecté
    await expect(newPage).toHaveURL(/.*\/dashboard/);
    await expect(newPage.locator('[data-testid="user-welcome"]')).toBeVisible();
  });

  test('Validation des champs de formulaire', async ({ page }) => {
    // Aller à la page d'inscription
    await page.goto('/register');

    // Soumettre le formulaire vide
    await page.click('[data-testid="submit-registration"]');

    // Vérifier les messages d'erreur de validation
    await expect(page.locator('[data-testid="email-error"]')).toBeVisible();
    await expect(page.locator('[data-testid="password-error"]')).toBeVisible();
    await expect(page.locator('[data-testid="firstname-error"]')).toBeVisible();
    await expect(page.locator('[data-testid="lastname-error"]')).toBeVisible();

    // Tester la validation de l'email
    await page.fill('[data-testid="email-input"]', 'invalid-email');
    await page.blur('[data-testid="email-input"]');
    await expect(page.locator('[data-testid="email-error"]')).toContainText('Email invalide');

    // Tester la validation du mot de passe
    await page.fill('[data-testid="password-input"]', '123');
    await page.blur('[data-testid="password-input"]');
    await expect(page.locator('[data-testid="password-error"]')).toContainText('Mot de passe trop faible');
  });

  test('Accessibilité du formulaire d\'authentification', async ({ page }) => {
    await page.goto('/login');

    // Vérifier la navigation au clavier
    await page.keyboard.press('Tab');
    await expect(page.locator('[data-testid="email-input"]')).toBeFocused();
    
    await page.keyboard.press('Tab');
    await expect(page.locator('[data-testid="password-input"]')).toBeFocused();
    
    await page.keyboard.press('Tab');
    await expect(page.locator('[data-testid="submit-login"]')).toBeFocused();

    // Vérifier les labels et attributs d'accessibilité
    await expect(page.locator('label[for="email"]')).toBeVisible();
    await expect(page.locator('label[for="password"]')).toBeVisible();
    await expect(page.locator('[data-testid="email-input"]')).toHaveAttribute('aria-required', 'true');
    await expect(page.locator('[data-testid="password-input"]')).toHaveAttribute('aria-required', 'true');
  });
});
