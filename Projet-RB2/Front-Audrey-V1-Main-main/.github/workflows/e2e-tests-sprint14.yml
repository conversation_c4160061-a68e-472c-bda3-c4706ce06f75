name: E2E Tests Sprint 14 - Tests Complets

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # Exécuter les tests tous les jours à 6h00 UTC
    - cron: '0 6 * * *'

env:
  NODE_VERSION: '18'
  PLAYWRIGHT_BROWSERS_PATH: ${{ github.workspace }}/ms-playwright

jobs:
  # Job de préparation
  setup:
    runs-on: ubuntu-latest
    outputs:
      cache-key: ${{ steps.cache-key.outputs.key }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Generate cache key
        id: cache-key
        run: echo "key=node-modules-${{ hashFiles('**/package-lock.json') }}" >> $GITHUB_OUTPUT

      - name: Cache dependencies
        uses: actions/cache@v3
        with:
          path: |
            node_modules
            ~/.cache/ms-playwright
          key: ${{ steps.cache-key.outputs.key }}
          restore-keys: |
            node-modules-

      - name: Install dependencies
        run: npm ci

      - name: Install Playwright browsers
        run: npx playwright install --with-deps

  # Tests Playwright E2E
  playwright-tests:
    needs: setup
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        project: [chromium-desktop, firefox-desktop, webkit-desktop, mobile-chrome, mobile-safari]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Restore dependencies cache
        uses: actions/cache@v3
        with:
          path: |
            node_modules
            ~/.cache/ms-playwright
          key: ${{ needs.setup.outputs.cache-key }}

      - name: Install dependencies (if cache miss)
        run: npm ci

      - name: Install Playwright browsers (if cache miss)
        run: npx playwright install --with-deps

      - name: Start application
        run: |
          npm run build
          npm run preview &
          sleep 10
        env:
          NODE_ENV: test
          PLAYWRIGHT_TEST: true

      - name: Wait for application to be ready
        run: |
          timeout 60 bash -c 'until curl -f http://localhost:3000; do sleep 2; done'

      - name: Run Playwright tests
        run: npx playwright test --project=${{ matrix.project }}
        env:
          BASE_URL: http://localhost:3000

      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: playwright-results-${{ matrix.project }}
          path: |
            test-results/
            reports/playwright/
          retention-days: 7

  # Tests Cypress E2E
  cypress-tests:
    needs: setup
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        browser: [chrome, firefox, edge]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Restore dependencies cache
        uses: actions/cache@v3
        with:
          path: |
            node_modules
            ~/.cache/Cypress
          key: ${{ needs.setup.outputs.cache-key }}

      - name: Install dependencies (if cache miss)
        run: npm ci

      - name: Cypress run
        uses: cypress-io/github-action@v6
        with:
          build: npm run build
          start: npm run preview
          wait-on: 'http://localhost:3000'
          wait-on-timeout: 120
          browser: ${{ matrix.browser }}
          record: true
          parallel: true
        env:
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Upload Cypress screenshots
        uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: cypress-screenshots-${{ matrix.browser }}
          path: cypress/screenshots
          retention-days: 7

      - name: Upload Cypress videos
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: cypress-videos-${{ matrix.browser }}
          path: cypress/videos
          retention-days: 7

  # Tests d'accessibilité
  accessibility-tests:
    needs: setup
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Restore dependencies cache
        uses: actions/cache@v3
        with:
          path: |
            node_modules
            ~/.cache/ms-playwright
          key: ${{ needs.setup.outputs.cache-key }}

      - name: Install dependencies (if cache miss)
        run: npm ci

      - name: Install Playwright browsers (if cache miss)
        run: npx playwright install --with-deps

      - name: Start application
        run: |
          npm run build
          npm run preview &
          sleep 10

      - name: Run accessibility tests
        run: npx playwright test --project=accessibility
        env:
          BASE_URL: http://localhost:3000

      - name: Upload accessibility results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: accessibility-results
          path: |
            test-results/
            reports/playwright/
          retention-days: 7

  # Tests de performance
  performance-tests:
    needs: setup
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Restore dependencies cache
        uses: actions/cache@v3
        with:
          path: |
            node_modules
            ~/.cache/ms-playwright
          key: ${{ needs.setup.outputs.cache-key }}

      - name: Install dependencies (if cache miss)
        run: npm ci

      - name: Install Playwright browsers (if cache miss)
        run: npx playwright install --with-deps

      - name: Start application
        run: |
          npm run build
          npm run preview &
          sleep 10

      - name: Run performance tests
        run: npx playwright test --project=performance
        env:
          BASE_URL: http://localhost:3000

      - name: Run Lighthouse CI
        run: |
          npm install -g @lhci/cli@0.12.x
          lhci autorun
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

      - name: Upload performance results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: performance-results
          path: |
            test-results/
            reports/playwright/
            .lighthouseci/
          retention-days: 7

  # Génération du rapport consolidé
  generate-report:
    needs: [playwright-tests, cypress-tests, accessibility-tests, performance-tests]
    runs-on: ubuntu-latest
    if: always()
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download all artifacts
        uses: actions/download-artifact@v3
        with:
          path: test-artifacts

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install report dependencies
        run: |
          npm install -g allure-commandline
          npm install --save-dev mochawesome-merge mochawesome-report-generator

      - name: Generate consolidated report
        run: |
          mkdir -p reports/consolidated
          
          # Merger les rapports Playwright
          find test-artifacts -name "*.json" -path "*/playwright-results-*" -exec cp {} reports/consolidated/ \;
          
          # Générer le rapport HTML
          echo "# 📊 Rapport de Tests E2E Sprint 14" > reports/consolidated/README.md
          echo "" >> reports/consolidated/README.md
          echo "**Date**: $(date)" >> reports/consolidated/README.md
          echo "**Commit**: ${{ github.sha }}" >> reports/consolidated/README.md
          echo "" >> reports/consolidated/README.md
          
          # Compter les résultats
          TOTAL_TESTS=$(find test-artifacts -name "*.json" | xargs grep -l "\"title\"" | wc -l)
          echo "**Total des tests**: $TOTAL_TESTS" >> reports/consolidated/README.md

      - name: Upload consolidated report
        uses: actions/upload-artifact@v3
        with:
          name: consolidated-test-report
          path: reports/consolidated/
          retention-days: 30

      - name: Comment PR with results
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            const path = require('path');
            
            let comment = '## 🧪 Résultats des Tests E2E Sprint 14\n\n';
            comment += `**Commit**: ${context.sha.substring(0, 7)}\n`;
            comment += `**Workflow**: [${context.runNumber}](${context.payload.repository.html_url}/actions/runs/${context.runId})\n\n`;
            
            // Ajouter les statuts des jobs
            comment += '### Statuts des Tests\n\n';
            comment += '| Type de Test | Statut |\n';
            comment += '|--------------|--------|\n';
            comment += '| Playwright | ${{ needs.playwright-tests.result == "success" && "✅ Succès" || "❌ Échec" }} |\n';
            comment += '| Cypress | ${{ needs.cypress-tests.result == "success" && "✅ Succès" || "❌ Échec" }} |\n';
            comment += '| Accessibilité | ${{ needs.accessibility-tests.result == "success" && "✅ Succès" || "❌ Échec" }} |\n';
            comment += '| Performance | ${{ needs.performance-tests.result == "success" && "✅ Succès" || "❌ Échec" }} |\n\n';
            
            comment += '### Artefacts\n\n';
            comment += '- [Rapport consolidé](${context.payload.repository.html_url}/actions/runs/${context.runId})\n';
            comment += '- [Captures d\'écran](${context.payload.repository.html_url}/actions/runs/${context.runId})\n';
            comment += '- [Vidéos des tests](${context.payload.repository.html_url}/actions/runs/${context.runId})\n';
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });

  # Notification des résultats
  notify-results:
    needs: [generate-report]
    runs-on: ubuntu-latest
    if: always() && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')
    steps:
      - name: Notify Slack on failure
        if: failure()
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          channel: '#dev-alerts'
          text: |
            🚨 Tests E2E Sprint 14 échoués sur ${{ github.ref_name }}
            Commit: ${{ github.sha }}
            Auteur: ${{ github.actor }}
            Voir: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: Notify Slack on success
        if: success()
        uses: 8398a7/action-slack@v3
        with:
          status: success
          channel: '#dev-updates'
          text: |
            ✅ Tests E2E Sprint 14 réussis sur ${{ github.ref_name }}
            Commit: ${{ github.sha }}
            Auteur: ${{ github.actor }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
