#!/bin/bash

# Deploy Production Infrastructure - Sprint 18
# Script de déploiement automatisé pour infrastructure Kubernetes production

set -e

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
K8S_DIR="$PROJECT_ROOT/k8s/production"
MONITORING_DIR="$PROJECT_ROOT/k8s/monitoring"
SCRIPTS_DIR="$PROJECT_ROOT/scripts"

# Variables d'environnement
CLUSTER_NAME="${CLUSTER_NAME:-retreat-and-be-production}"
NAMESPACE_PROD="${NAMESPACE_PROD:-production}"
NAMESPACE_MONITORING="${NAMESPACE_MONITORING:-monitoring}"
DOMAIN="${DOMAIN:-retreatandbe.com}"
REGISTRY="${REGISTRY:-retreatandbe.azurecr.io}"

echo -e "${BLUE}🏭 Déploiement Infrastructure Production - Sprint 18${NC}"
echo "=============================================================="

# Fonction de logging
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "${PURPLE}🚀 $1${NC}"
}

# Fonction de vérification des prérequis
check_prerequisites() {
    log_step "Vérification des prérequis..."
    
    # Vérifier kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl n'est pas installé"
        exit 1
    fi
    
    # Vérifier helm
    if ! command -v helm &> /dev/null; then
        log_error "helm n'est pas installé"
        exit 1
    fi
    
    # Vérifier la connexion au cluster
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Impossible de se connecter au cluster Kubernetes"
        exit 1
    fi
    
    # Vérifier les répertoires
    if [ ! -d "$K8S_DIR" ]; then
        log_error "Répertoire k8s/production non trouvé: $K8S_DIR"
        exit 1
    fi
    
    log_success "Prérequis vérifiés"
}

# Fonction de création des namespaces
create_namespaces() {
    log_step "Création des namespaces..."
    
    # Namespace production
    kubectl create namespace $NAMESPACE_PROD --dry-run=client -o yaml | kubectl apply -f -
    kubectl label namespace $NAMESPACE_PROD environment=production --overwrite
    kubectl label namespace $NAMESPACE_PROD project=retreat-and-be --overwrite
    
    # Namespace monitoring
    kubectl create namespace $NAMESPACE_MONITORING --dry-run=client -o yaml | kubectl apply -f -
    kubectl label namespace $NAMESPACE_MONITORING environment=production --overwrite
    kubectl label namespace $NAMESPACE_MONITORING purpose=observability --overwrite
    
    # Namespace ingress-nginx
    kubectl create namespace ingress-nginx --dry-run=client -o yaml | kubectl apply -f -
    kubectl label namespace ingress-nginx environment=production --overwrite
    kubectl label namespace ingress-nginx purpose=ingress --overwrite
    
    # Namespace cert-manager
    kubectl create namespace cert-manager --dry-run=client -o yaml | kubectl apply -f -
    kubectl label namespace cert-manager environment=production --overwrite
    kubectl label namespace cert-manager purpose=certificates --overwrite
    
    log_success "Namespaces créés"
}

# Fonction d'installation de cert-manager
install_cert_manager() {
    log_step "Installation de cert-manager..."
    
    # Ajouter le repository Helm
    helm repo add jetstack https://charts.jetstack.io
    helm repo update
    
    # Installer cert-manager
    helm upgrade --install cert-manager jetstack/cert-manager \
        --namespace cert-manager \
        --version v1.13.0 \
        --set installCRDs=true \
        --set global.leaderElection.namespace=cert-manager \
        --wait
    
    # Attendre que cert-manager soit prêt
    kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=cert-manager -n cert-manager --timeout=300s
    
    log_success "cert-manager installé"
}

# Fonction d'installation de NGINX Ingress
install_nginx_ingress() {
    log_step "Installation de NGINX Ingress Controller..."
    
    # Ajouter le repository Helm
    helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx
    helm repo update
    
    # Installer NGINX Ingress
    helm upgrade --install ingress-nginx ingress-nginx/ingress-nginx \
        --namespace ingress-nginx \
        --set controller.replicaCount=3 \
        --set controller.nodeSelector."kubernetes\.io/arch"=amd64 \
        --set controller.admissionWebhooks.enabled=true \
        --set controller.metrics.enabled=true \
        --set controller.metrics.serviceMonitor.enabled=true \
        --set controller.podAnnotations."prometheus\.io/scrape"=true \
        --set controller.podAnnotations."prometheus\.io/port"=10254 \
        --set controller.service.type=LoadBalancer \
        --set controller.service.annotations."service\.beta\.kubernetes\.io/aws-load-balancer-type"=nlb \
        --set controller.service.annotations."service\.beta\.kubernetes\.io/aws-load-balancer-cross-zone-load-balancing-enabled"=true \
        --wait
    
    # Attendre que l'ingress controller soit prêt
    kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=ingress-nginx -n ingress-nginx --timeout=300s
    
    log_success "NGINX Ingress Controller installé"
}

# Fonction de déploiement des storage classes
deploy_storage_classes() {
    log_step "Déploiement des storage classes..."
    
    cat <<EOF | kubectl apply -f -
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: fast-ssd
  annotations:
    storageclass.kubernetes.io/is-default-class: "true"
provisioner: kubernetes.io/aws-ebs
parameters:
  type: gp3
  iops: "3000"
  throughput: "125"
  encrypted: "true"
allowVolumeExpansion: true
volumeBindingMode: WaitForFirstConsumer
---
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: standard-hdd
provisioner: kubernetes.io/aws-ebs
parameters:
  type: gp2
  encrypted: "true"
allowVolumeExpansion: true
volumeBindingMode: WaitForFirstConsumer
EOF
    
    log_success "Storage classes déployées"
}

# Fonction de création des secrets
create_secrets() {
    log_step "Création des secrets..."
    
    # Secret pour le registry Docker
    if [ ! -z "$DOCKER_REGISTRY_USERNAME" ] && [ ! -z "$DOCKER_REGISTRY_PASSWORD" ]; then
        kubectl create secret docker-registry acr-secret \
            --docker-server=$REGISTRY \
            --docker-username=$DOCKER_REGISTRY_USERNAME \
            --docker-password=$DOCKER_REGISTRY_PASSWORD \
            --namespace=$NAMESPACE_PROD \
            --dry-run=client -o yaml | kubectl apply -f -
    fi
    
    # Secret pour la base de données
    if [ ! -z "$DATABASE_URL" ]; then
        kubectl create secret generic database-credentials \
            --from-literal=url="$DATABASE_URL" \
            --namespace=$NAMESPACE_PROD \
            --dry-run=client -o yaml | kubectl apply -f -
    fi
    
    # Secret pour JWT
    if [ ! -z "$JWT_SECRET" ]; then
        kubectl create secret generic jwt-secret \
            --from-literal=secret="$JWT_SECRET" \
            --namespace=$NAMESPACE_PROD \
            --dry-run=client -o yaml | kubectl apply -f -
    fi
    
    # Secret pour Redis
    if [ ! -z "$REDIS_URL" ]; then
        kubectl create secret generic redis-credentials \
            --from-literal=url="$REDIS_URL" \
            --namespace=$NAMESPACE_PROD \
            --dry-run=client -o yaml | kubectl apply -f -
    fi
    
    log_success "Secrets créés"
}

# Fonction de déploiement du cluster issuer
deploy_cluster_issuer() {
    log_step "Déploiement du cluster issuer..."
    
    cat <<EOF | kubectl apply -f -
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: devops@$DOMAIN
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx
EOF
    
    log_success "Cluster issuer déployé"
}

# Fonction de déploiement des applications
deploy_applications() {
    log_step "Déploiement des applications..."
    
    # Déployer les manifests de production
    if [ -f "$K8S_DIR/retreat-and-be-deployment.yaml" ]; then
        kubectl apply -f "$K8S_DIR/retreat-and-be-deployment.yaml"
        log_success "Application backend déployée"
    fi
    
    # Déployer l'ingress
    if [ -f "$K8S_DIR/ingress.yaml" ]; then
        kubectl apply -f "$K8S_DIR/ingress.yaml"
        log_success "Ingress déployé"
    fi
    
    # Déployer le monitoring
    if [ -f "$K8S_DIR/monitoring-stack.yaml" ]; then
        kubectl apply -f "$K8S_DIR/monitoring-stack.yaml"
        log_success "Stack de monitoring déployée"
    fi
    
    # Déployer l'auto-scaling
    if [ -f "$K8S_DIR/hpa.yaml" ]; then
        kubectl apply -f "$K8S_DIR/hpa.yaml"
        log_success "Auto-scaling déployé"
    fi
}

# Fonction de validation du déploiement
validate_deployment() {
    log_step "Validation du déploiement..."
    
    # Attendre que les pods soient prêts
    log_info "Attente que les pods soient prêts..."
    kubectl wait --for=condition=ready pod -l app=retreat-and-be-backend -n $NAMESPACE_PROD --timeout=300s
    
    # Vérifier les services
    log_info "Vérification des services..."
    kubectl get services -n $NAMESPACE_PROD
    kubectl get services -n $NAMESPACE_MONITORING
    
    # Vérifier les ingress
    log_info "Vérification des ingress..."
    kubectl get ingress -n $NAMESPACE_PROD
    
    # Vérifier les certificats
    log_info "Vérification des certificats SSL..."
    kubectl get certificates -n $NAMESPACE_PROD
    
    # Test de santé
    log_info "Test de santé des applications..."
    if kubectl get pods -l app=retreat-and-be-backend -n $NAMESPACE_PROD | grep -q "Running"; then
        log_success "Application backend en cours d'exécution"
    else
        log_warning "Application backend non disponible"
    fi
    
    log_success "Validation terminée"
}

# Fonction de monitoring post-déploiement
setup_monitoring() {
    log_step "Configuration du monitoring..."
    
    # Installer Prometheus Operator si pas déjà fait
    helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
    helm repo update
    
    helm upgrade --install kube-prometheus-stack prometheus-community/kube-prometheus-stack \
        --namespace $NAMESPACE_MONITORING \
        --set prometheus.prometheusSpec.storageSpec.volumeClaimTemplate.spec.storageClassName=fast-ssd \
        --set prometheus.prometheusSpec.storageSpec.volumeClaimTemplate.spec.resources.requests.storage=50Gi \
        --set grafana.persistence.enabled=true \
        --set grafana.persistence.storageClassName=fast-ssd \
        --set grafana.persistence.size=10Gi \
        --set alertmanager.alertmanagerSpec.storage.volumeClaimTemplate.spec.storageClassName=fast-ssd \
        --set alertmanager.alertmanagerSpec.storage.volumeClaimTemplate.spec.resources.requests.storage=10Gi \
        --wait
    
    log_success "Monitoring configuré"
}

# Fonction d'affichage des informations finales
display_final_info() {
    echo ""
    echo -e "${GREEN}🎉 DÉPLOIEMENT INFRASTRUCTURE PRODUCTION RÉUSSI !${NC}"
    echo "=============================================================="
    echo -e "${GREEN}✅ Cluster Kubernetes configuré${NC}"
    echo -e "${GREEN}✅ NGINX Ingress Controller déployé${NC}"
    echo -e "${GREEN}✅ Cert-Manager configuré${NC}"
    echo -e "${GREEN}✅ Applications déployées${NC}"
    echo -e "${GREEN}✅ Monitoring configuré${NC}"
    echo -e "${GREEN}✅ Auto-scaling activé${NC}"
    echo ""
    echo -e "${BLUE}📊 Informations de connexion:${NC}"
    echo -e "${BLUE}🌐 Site web: https://$DOMAIN${NC}"
    echo -e "${BLUE}🔧 API: https://api.$DOMAIN${NC}"
    echo -e "${BLUE}📈 Monitoring: https://monitoring.$DOMAIN${NC}"
    echo -e "${BLUE}📊 Grafana: https://grafana.$DOMAIN${NC}"
    echo ""
    echo -e "${BLUE}🔍 Commandes utiles:${NC}"
    echo "kubectl get pods -n $NAMESPACE_PROD"
    echo "kubectl get services -n $NAMESPACE_PROD"
    echo "kubectl get ingress -n $NAMESPACE_PROD"
    echo "kubectl logs -f deployment/retreat-and-be-backend -n $NAMESPACE_PROD"
}

# Fonction principale
main() {
    echo -e "${BLUE}🚀 Début du déploiement infrastructure production${NC}"
    
    # Étapes de déploiement
    check_prerequisites
    create_namespaces
    deploy_storage_classes
    install_cert_manager
    install_nginx_ingress
    create_secrets
    deploy_cluster_issuer
    deploy_applications
    setup_monitoring
    validate_deployment
    
    # Affichage des informations finales
    display_final_info
}

# Gestion des erreurs
trap 'log_error "Erreur lors du déploiement à la ligne $LINENO"' ERR

# Exécution
main "$@"
