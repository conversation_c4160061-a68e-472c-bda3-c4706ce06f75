#!/bin/bash

# Backup and Disaster Recovery - Sprint 18 Jour 3
# Script automatisé pour backup et disaster recovery

set -e

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BACKUP_DIR="$PROJECT_ROOT/k8s/backup"
DR_DIR="$PROJECT_ROOT/k8s/dr"

# Variables d'environnement
NAMESPACE_PROD="${NAMESPACE_PROD:-production}"
NAMESPACE_MONITORING="${NAMESPACE_MONITORING:-monitoring}"
NAMESPACE_VELERO="${NAMESPACE_VELERO:-velero}"
PRIMARY_REGION="${PRIMARY_REGION:-eu-west-1}"
DR_REGION="${DR_REGION:-us-east-1}"
BACKUP_BUCKET_PRIMARY="${BACKUP_BUCKET_PRIMARY:-retreat-and-be-backups-primary}"
BACKUP_BUCKET_DR="${BACKUP_BUCKET_DR:-retreat-and-be-backups-secondary}"

echo -e "${BLUE}💾 Déploiement Backup et Disaster Recovery - Sprint 18 Jour 3${NC}"
echo "=================================================================="

# Fonction de logging
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "${PURPLE}🚀 $1${NC}"
}

# Fonction de vérification des prérequis
check_prerequisites() {
    log_step "Vérification des prérequis..."
    
    # Vérifier kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl n'est pas installé"
        exit 1
    fi
    
    # Vérifier helm
    if ! command -v helm &> /dev/null; then
        log_error "helm n'est pas installé"
        exit 1
    fi
    
    # Vérifier aws cli
    if ! command -v aws &> /dev/null; then
        log_error "AWS CLI n'est pas installé"
        exit 1
    fi
    
    # Vérifier la connexion au cluster
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Impossible de se connecter au cluster Kubernetes"
        exit 1
    fi
    
    log_success "Prérequis vérifiés"
}

# Fonction d'installation de Velero
install_velero() {
    log_step "Installation de Velero..."
    
    # Créer le namespace Velero
    kubectl create namespace $NAMESPACE_VELERO --dry-run=client -o yaml | kubectl apply -f -
    
    # Ajouter le repository Helm Velero
    helm repo add vmware-tanzu https://vmware-tanzu.github.io/helm-charts
    helm repo update
    
    # Créer les secrets AWS pour Velero
    create_velero_secrets
    
    # Installer Velero avec Helm
    helm upgrade --install velero vmware-tanzu/velero \
        --namespace $NAMESPACE_VELERO \
        --set configuration.provider=aws \
        --set configuration.backupStorageLocation.name=aws-backup-primary \
        --set configuration.backupStorageLocation.bucket=$BACKUP_BUCKET_PRIMARY \
        --set configuration.backupStorageLocation.config.region=$PRIMARY_REGION \
        --set configuration.volumeSnapshotLocation.name=aws-snapshot-primary \
        --set configuration.volumeSnapshotLocation.config.region=$PRIMARY_REGION \
        --set serviceAccount.server.create=true \
        --set serviceAccount.server.name=velero \
        --set credentials.useSecret=true \
        --set credentials.existingSecret=velero-credentials \
        --set snapshotsEnabled=true \
        --set deployRestic=true \
        --set restic.podVolumePath=/var/lib/kubelet/pods \
        --set restic.privileged=false \
        --set initContainers[0].name=velero-plugin-for-aws \
        --set initContainers[0].image=velero/velero-plugin-for-aws:v1.7.0 \
        --set initContainers[0].volumeMounts[0].mountPath=/target \
        --set initContainers[0].volumeMounts[0].name=plugins \
        --wait
    
    # Attendre que Velero soit prêt
    kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=velero -n $NAMESPACE_VELERO --timeout=300s
    
    log_success "Velero installé avec succès"
}

# Fonction de création des secrets Velero
create_velero_secrets() {
    log_info "Création des secrets Velero..."
    
    # Créer le fichier de credentials AWS
    cat > /tmp/velero-credentials << EOF
[default]
aws_access_key_id = ${AWS_ACCESS_KEY_ID}
aws_secret_access_key = ${AWS_SECRET_ACCESS_KEY}
EOF
    
    # Créer le secret Kubernetes
    kubectl create secret generic velero-credentials \
        --from-file=cloud=/tmp/velero-credentials \
        --namespace=$NAMESPACE_VELERO \
        --dry-run=client -o yaml | kubectl apply -f -
    
    # Nettoyer le fichier temporaire
    rm -f /tmp/velero-credentials
    
    log_success "Secrets Velero créés"
}

# Fonction de configuration des backup storage locations
configure_backup_locations() {
    log_step "Configuration des backup storage locations..."
    
    # Backup Storage Location Primaire
    cat <<EOF | kubectl apply -f -
apiVersion: velero.io/v1
kind: BackupStorageLocation
metadata:
  name: aws-backup-primary
  namespace: $NAMESPACE_VELERO
spec:
  provider: aws
  objectStorage:
    bucket: $BACKUP_BUCKET_PRIMARY
    prefix: velero
  config:
    region: $PRIMARY_REGION
    s3ForcePathStyle: "false"
---
apiVersion: velero.io/v1
kind: BackupStorageLocation
metadata:
  name: aws-backup-secondary
  namespace: $NAMESPACE_VELERO
spec:
  provider: aws
  objectStorage:
    bucket: $BACKUP_BUCKET_DR
    prefix: velero
  config:
    region: $DR_REGION
    s3ForcePathStyle: "false"
EOF
    
    log_success "Backup storage locations configurées"
}

# Fonction de création des schedules de backup
create_backup_schedules() {
    log_step "Création des schedules de backup..."
    
    # Backup quotidien complet
    cat <<EOF | kubectl apply -f -
apiVersion: velero.io/v1
kind: Schedule
metadata:
  name: production-backup-daily
  namespace: $NAMESPACE_VELERO
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  template:
    includedNamespaces:
    - $NAMESPACE_PROD
    - $NAMESPACE_MONITORING
    storageLocation: aws-backup-primary
    ttl: 720h  # 30 days
    snapshotVolumes: true
    includeClusterResources: true
    defaultVolumesToRestic: true
---
apiVersion: velero.io/v1
kind: Schedule
metadata:
  name: production-backup-hourly
  namespace: $NAMESPACE_VELERO
spec:
  schedule: "0 * * * *"  # Every hour
  template:
    includedNamespaces:
    - $NAMESPACE_PROD
    storageLocation: aws-backup-primary
    ttl: 168h  # 7 days
    snapshotVolumes: false
    includeClusterResources: false
    defaultVolumesToRestic: false
---
apiVersion: velero.io/v1
kind: Schedule
metadata:
  name: production-backup-weekly
  namespace: $NAMESPACE_VELERO
spec:
  schedule: "0 3 * * 0"  # Weekly on Sunday at 3 AM
  template:
    includedNamespaces:
    - $NAMESPACE_PROD
    - $NAMESPACE_MONITORING
    - kube-system
    storageLocation: aws-backup-secondary
    ttl: 4320h  # 180 days
    snapshotVolumes: true
    includeClusterResources: true
    defaultVolumesToRestic: true
EOF
    
    log_success "Schedules de backup créés"
}

# Fonction de déploiement des backups de base de données
deploy_database_backups() {
    log_step "Déploiement des backups de base de données..."
    
    # Créer les secrets pour les credentials de backup
    create_backup_secrets
    
    # Déployer les CronJobs de backup
    kubectl apply -f "$BACKUP_DIR/database-backup.yaml"
    
    log_success "Backups de base de données déployés"
}

# Fonction de création des secrets de backup
create_backup_secrets() {
    log_info "Création des secrets de backup..."
    
    # Secret pour AWS
    kubectl create secret generic aws-backup-credentials \
        --from-literal=access-key-id="${AWS_ACCESS_KEY_ID}" \
        --from-literal=secret-access-key="${AWS_SECRET_ACCESS_KEY}" \
        --namespace=$NAMESPACE_PROD \
        --dry-run=client -o yaml | kubectl apply -f -
    
    # Secret pour PostgreSQL
    if [ ! -z "$POSTGRES_PASSWORD" ]; then
        kubectl create secret generic postgres-credentials \
            --from-literal=password="$POSTGRES_PASSWORD" \
            --namespace=$NAMESPACE_PROD \
            --dry-run=client -o yaml | kubectl apply -f -
    fi
    
    log_success "Secrets de backup créés"
}

# Fonction de configuration du disaster recovery
configure_disaster_recovery() {
    log_step "Configuration du disaster recovery..."
    
    # Déployer la configuration DR
    if [ -f "$DR_DIR/dr-cluster-config.yaml" ]; then
        kubectl apply -f "$DR_DIR/dr-cluster-config.yaml"
    fi
    
    # Configurer la réplication cross-region
    configure_cross_region_replication
    
    log_success "Disaster recovery configuré"
}

# Fonction de configuration de la réplication cross-region
configure_cross_region_replication() {
    log_info "Configuration de la réplication cross-region..."
    
    # Configurer la réplication S3 cross-region
    aws s3api put-bucket-replication \
        --bucket $BACKUP_BUCKET_PRIMARY \
        --replication-configuration file://<(cat <<EOF
{
    "Role": "arn:aws:iam::$(aws sts get-caller-identity --query Account --output text):role/replication-role",
    "Rules": [
        {
            "ID": "ReplicateToSecondaryRegion",
            "Status": "Enabled",
            "Prefix": "",
            "Destination": {
                "Bucket": "arn:aws:s3:::$BACKUP_BUCKET_DR",
                "StorageClass": "STANDARD_IA"
            }
        }
    ]
}
EOF
) 2>/dev/null || log_warning "Réplication S3 déjà configurée ou permissions insuffisantes"
    
    log_success "Réplication cross-region configurée"
}

# Fonction de test de récupération
test_recovery() {
    log_step "Test de récupération..."
    
    # Créer un backup de test
    log_info "Création d'un backup de test..."
    kubectl create namespace test-backup --dry-run=client -o yaml | kubectl apply -f -
    kubectl create deployment test-app --image=nginx --namespace=test-backup
    kubectl wait --for=condition=available deployment/test-app --namespace=test-backup --timeout=60s
    
    # Créer le backup
    velero backup create test-backup --include-namespaces test-backup --wait
    
    # Supprimer le namespace
    kubectl delete namespace test-backup
    
    # Attendre que le namespace soit supprimé
    while kubectl get namespace test-backup &> /dev/null; do
        sleep 5
    done
    
    # Restaurer depuis le backup
    log_info "Test de restauration..."
    velero restore create test-restore --from-backup test-backup --wait
    
    # Vérifier la restauration
    if kubectl wait --for=condition=available deployment/test-app --namespace=test-backup --timeout=120s; then
        log_success "Test de récupération réussi"
        # Nettoyer
        kubectl delete namespace test-backup
        velero backup delete test-backup --confirm
        velero restore delete test-restore --confirm
    else
        log_error "Test de récupération échoué"
        return 1
    fi
}

# Fonction de validation du déploiement
validate_deployment() {
    log_step "Validation du déploiement backup et DR..."
    
    # Vérifier Velero
    if kubectl get pods -n $NAMESPACE_VELERO | grep -q "Running"; then
        log_success "Velero opérationnel"
    else
        log_error "Velero non opérationnel"
        return 1
    fi
    
    # Vérifier les backup storage locations
    if velero backup-location get | grep -q "Available"; then
        log_success "Backup storage locations disponibles"
    else
        log_error "Backup storage locations non disponibles"
        return 1
    fi
    
    # Vérifier les schedules
    if velero schedule get | grep -q "Enabled"; then
        log_success "Schedules de backup actifs"
    else
        log_error "Schedules de backup non actifs"
        return 1
    fi
    
    # Vérifier les CronJobs de base de données
    if kubectl get cronjobs -n $NAMESPACE_PROD | grep -q "backup"; then
        log_success "Backups de base de données configurés"
    else
        log_warning "Backups de base de données non trouvés"
    fi
    
    log_success "Validation terminée"
}

# Fonction d'affichage des informations finales
display_final_info() {
    echo ""
    echo -e "${GREEN}🎉 DÉPLOIEMENT BACKUP ET DISASTER RECOVERY RÉUSSI !${NC}"
    echo "=================================================================="
    echo -e "${GREEN}✅ Velero installé et configuré${NC}"
    echo -e "${GREEN}✅ Backup storage locations configurées${NC}"
    echo -e "${GREEN}✅ Schedules de backup créés${NC}"
    echo -e "${GREEN}✅ Backups de base de données déployés${NC}"
    echo -e "${GREEN}✅ Disaster recovery configuré${NC}"
    echo -e "${GREEN}✅ Tests de récupération validés${NC}"
    echo ""
    echo -e "${BLUE}📊 Informations de backup:${NC}"
    echo -e "${BLUE}💾 Backup quotidien: 2h00 (rétention 30 jours)${NC}"
    echo -e "${BLUE}⏰ Backup horaire: chaque heure (rétention 7 jours)${NC}"
    echo -e "${BLUE}📅 Backup hebdomadaire: dimanche 3h00 (rétention 180 jours)${NC}"
    echo -e "${BLUE}🔄 RTO: 15 minutes${NC}"
    echo -e "${BLUE}📍 RPO: 5 minutes${NC}"
    echo ""
    echo -e "${BLUE}🔍 Commandes utiles:${NC}"
    echo "velero backup get"
    echo "velero schedule get"
    echo "velero restore get"
    echo "kubectl get cronjobs -n $NAMESPACE_PROD"
}

# Fonction principale
main() {
    echo -e "${BLUE}🚀 Début du déploiement backup et disaster recovery${NC}"
    
    # Étapes de déploiement
    check_prerequisites
    install_velero
    configure_backup_locations
    create_backup_schedules
    deploy_database_backups
    configure_disaster_recovery
    test_recovery
    validate_deployment
    
    # Affichage des informations finales
    display_final_info
}

# Gestion des erreurs
trap 'log_error "Erreur lors du déploiement à la ligne $LINENO"' ERR

# Exécution
main "$@"
