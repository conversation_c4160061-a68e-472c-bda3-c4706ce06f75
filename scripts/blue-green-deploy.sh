#!/bin/bash

# Blue-Green Deployment Script - Sprint 18 Jour 5
# Script automatisé pour déploiement zero-downtime

set -e

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration par défaut
NAMESPACE="production"
APP_NAME="retreat-and-be"
IMAGE=""
TIMEOUT=600
HEALTH_CHECK_TIMEOUT=300
HEALTH_CHECK_RETRIES=10
SMOKE_TEST_TIMEOUT=120
DRY_RUN=false
VERBOSE=false

echo -e "${BLUE}🔵 Blue-Green Deployment - Sprint 18 Jour 5${NC}"
echo "=============================================================="

# Fonction de logging
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "${PURPLE}🚀 $1${NC}"
}

log_verbose() {
    if [ "$VERBOSE" = true ]; then
        echo -e "${BLUE}🔍 $1${NC}"
    fi
}

# Fonction d'aide
show_help() {
    cat << EOF
Usage: $0 [OPTIONS]

Blue-Green Deployment Script for Zero-Downtime Deployments

OPTIONS:
    --image IMAGE           Container image to deploy (required)
    --namespace NAMESPACE   Kubernetes namespace (default: production)
    --app-name APP_NAME     Application name (default: retreat-and-be)
    --timeout SECONDS       Deployment timeout (default: 600)
    --health-timeout SECONDS Health check timeout (default: 300)
    --retries NUMBER        Health check retries (default: 10)
    --smoke-timeout SECONDS Smoke test timeout (default: 120)
    --dry-run              Perform dry run without actual deployment
    --verbose              Enable verbose logging
    --help                 Show this help message

EXAMPLES:
    $0 --image myregistry/myapp:v1.2.3
    $0 --image myregistry/myapp:latest --namespace staging --verbose
    $0 --image myregistry/myapp:v1.2.3 --dry-run

ENVIRONMENT VARIABLES:
    KUBECONFIG             Path to kubeconfig file
    HELM_CHART_PATH        Path to Helm chart (default: ./helm/retreat-and-be)
EOF
}

# Parse des arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --image)
            IMAGE="$2"
            shift 2
            ;;
        --namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        --app-name)
            APP_NAME="$2"
            shift 2
            ;;
        --timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        --health-timeout)
            HEALTH_CHECK_TIMEOUT="$2"
            shift 2
            ;;
        --retries)
            HEALTH_CHECK_RETRIES="$2"
            shift 2
            ;;
        --smoke-timeout)
            SMOKE_TEST_TIMEOUT="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validation des paramètres
if [ -z "$IMAGE" ]; then
    log_error "Error: --image is required"
    show_help
    exit 1
fi

# Variables dérivées
HELM_CHART_PATH="${HELM_CHART_PATH:-./helm/retreat-and-be}"
IMAGE_REPO=$(echo $IMAGE | cut -d: -f1)
IMAGE_TAG=$(echo $IMAGE | cut -d: -f2)

log_info "Configuration:"
log_info "  Image: $IMAGE"
log_info "  Namespace: $NAMESPACE"
log_info "  App Name: $APP_NAME"
log_info "  Timeout: ${TIMEOUT}s"
log_info "  Dry Run: $DRY_RUN"

# Fonction de vérification des prérequis
check_prerequisites() {
    log_step "Vérification des prérequis..."
    
    # Vérifier kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl n'est pas installé"
        exit 1
    fi
    
    # Vérifier helm
    if ! command -v helm &> /dev/null; then
        log_error "helm n'est pas installé"
        exit 1
    fi
    
    # Vérifier la connexion au cluster
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Impossible de se connecter au cluster Kubernetes"
        exit 1
    fi
    
    # Vérifier le namespace
    if ! kubectl get namespace $NAMESPACE &> /dev/null; then
        log_error "Namespace '$NAMESPACE' n'existe pas"
        exit 1
    fi
    
    # Vérifier le chart Helm
    if [ ! -d "$HELM_CHART_PATH" ]; then
        log_error "Chart Helm non trouvé: $HELM_CHART_PATH"
        exit 1
    fi
    
    log_success "Prérequis vérifiés"
}

# Fonction de détermination de l'environnement actuel
get_current_environment() {
    log_step "Détermination de l'environnement actuel..."
    
    # Essayer de récupérer la version active depuis le service
    local current_active=$(kubectl get service $APP_NAME -n $NAMESPACE -o jsonpath='{.spec.selector.version}' 2>/dev/null || echo "")
    
    if [ -z "$current_active" ]; then
        # Si pas de service existant, vérifier les déploiements
        if kubectl get deployment $APP_NAME-blue -n $NAMESPACE &> /dev/null; then
            current_active="blue"
        elif kubectl get deployment $APP_NAME-green -n $NAMESPACE &> /dev/null; then
            current_active="green"
        else
            current_active="blue"  # Premier déploiement
        fi
    fi
    
    if [ "$current_active" = "blue" ]; then
        NEW_VERSION="green"
    else
        NEW_VERSION="blue"
    fi
    
    log_info "Environnement actuel: $current_active"
    log_info "Nouveau déploiement: $NEW_VERSION"
    
    echo "$current_active"
}

# Fonction de déploiement de la nouvelle version
deploy_new_version() {
    local current_active=$1
    
    log_step "Déploiement de la nouvelle version ($NEW_VERSION)..."
    
    if [ "$DRY_RUN" = true ]; then
        log_info "DRY RUN: Simulation du déploiement"
        return 0
    fi
    
    # Préparer les valeurs Helm
    local helm_values="--set image.repository=$IMAGE_REPO"
    helm_values="$helm_values --set image.tag=$IMAGE_TAG"
    helm_values="$helm_values --set version=$NEW_VERSION"
    helm_values="$helm_values --set environment=production"
    helm_values="$helm_values --set fullnameOverride=$APP_NAME-$NEW_VERSION"
    
    log_verbose "Commande Helm: helm upgrade --install $APP_NAME-$NEW_VERSION $HELM_CHART_PATH $helm_values"
    
    # Déployer avec Helm
    if ! helm upgrade --install $APP_NAME-$NEW_VERSION $HELM_CHART_PATH \
        --namespace $NAMESPACE \
        $helm_values \
        --wait --timeout=${TIMEOUT}s; then
        log_error "Échec du déploiement Helm"
        return 1
    fi
    
    log_success "Nouvelle version déployée avec succès"
}

# Fonction de vérification de santé
run_health_checks() {
    log_step "Vérification de santé de la nouvelle version..."
    
    if [ "$DRY_RUN" = true ]; then
        log_info "DRY RUN: Simulation des health checks"
        return 0
    fi
    
    local health_check_url="http://$APP_NAME-$NEW_VERSION.$NAMESPACE.svc.cluster.local/health"
    local retry_count=0
    
    log_verbose "URL de health check: $health_check_url"
    
    while [ $retry_count -lt $HEALTH_CHECK_RETRIES ]; do
        log_info "Health check tentative $((retry_count + 1))/$HEALTH_CHECK_RETRIES..."
        
        # Utiliser un pod temporaire pour le health check
        if kubectl run health-check-$NEW_VERSION-$retry_count \
            --rm -i --restart=Never \
            --image=curlimages/curl \
            --timeout=${HEALTH_CHECK_TIMEOUT}s \
            -- curl -f --max-time 10 $health_check_url &> /dev/null; then
            log_success "Health check réussi"
            return 0
        fi
        
        retry_count=$((retry_count + 1))
        if [ $retry_count -lt $HEALTH_CHECK_RETRIES ]; then
            log_warning "Health check échoué, nouvelle tentative dans 10s..."
            sleep 10
        fi
    done
    
    log_error "Health check échoué après $HEALTH_CHECK_RETRIES tentatives"
    return 1
}

# Fonction de tests de fumée
run_smoke_tests() {
    log_step "Exécution des tests de fumée..."
    
    if [ "$DRY_RUN" = true ]; then
        log_info "DRY RUN: Simulation des smoke tests"
        return 0
    fi
    
    local base_url="http://$APP_NAME-$NEW_VERSION.$NAMESPACE.svc.cluster.local"
    local test_endpoints=("/health" "/api/health" "/api/version")
    
    for endpoint in "${test_endpoints[@]}"; do
        log_verbose "Test endpoint: $base_url$endpoint"
        
        if ! kubectl run smoke-test-$(date +%s) \
            --rm -i --restart=Never \
            --image=curlimages/curl \
            --timeout=${SMOKE_TEST_TIMEOUT}s \
            -- curl -f --max-time 5 "$base_url$endpoint" &> /dev/null; then
            log_error "Smoke test échoué pour $endpoint"
            return 1
        fi
    done
    
    log_success "Tous les smoke tests ont réussi"
}

# Fonction de basculement du trafic
switch_traffic() {
    log_step "Basculement du trafic vers $NEW_VERSION..."
    
    if [ "$DRY_RUN" = true ]; then
        log_info "DRY RUN: Simulation du basculement de trafic"
        return 0
    fi
    
    # Mettre à jour le service principal pour pointer vers la nouvelle version
    if ! kubectl patch service $APP_NAME -n $NAMESPACE \
        -p '{"spec":{"selector":{"version":"'$NEW_VERSION'"}}}'; then
        log_error "Échec du basculement de trafic"
        return 1
    fi
    
    log_success "Trafic basculé vers $NEW_VERSION"
    
    # Attendre un peu pour la propagation
    sleep 5
}

# Fonction de vérification du basculement
verify_traffic_switch() {
    log_step "Vérification du basculement de trafic..."
    
    if [ "$DRY_RUN" = true ]; then
        log_info "DRY RUN: Simulation de la vérification"
        return 0
    fi
    
    local verification_url="http://$APP_NAME.$NAMESPACE.svc.cluster.local/health"
    
    for i in {1..5}; do
        log_verbose "Vérification $i/5..."
        
        if ! kubectl run traffic-verification-$i \
            --rm -i --restart=Never \
            --image=curlimages/curl \
            --timeout=30s \
            -- curl -f --max-time 5 $verification_url &> /dev/null; then
            log_error "Vérification du trafic échouée"
            return 1
        fi
    done
    
    log_success "Basculement de trafic vérifié avec succès"
}

# Fonction de nettoyage de l'ancienne version
cleanup_old_version() {
    local current_active=$1
    
    log_step "Nettoyage de l'ancienne version ($current_active)..."
    
    if [ "$DRY_RUN" = true ]; then
        log_info "DRY RUN: Simulation du nettoyage"
        return 0
    fi
    
    # Attendre un peu avant le nettoyage pour s'assurer que tout fonctionne
    log_info "Attente de 30s avant nettoyage..."
    sleep 30
    
    # Supprimer l'ancien déploiement
    if helm list -n $NAMESPACE | grep -q "$APP_NAME-$current_active"; then
        if helm uninstall $APP_NAME-$current_active -n $NAMESPACE; then
            log_success "Ancienne version supprimée"
        else
            log_warning "Échec de la suppression de l'ancienne version (non critique)"
        fi
    else
        log_info "Aucune ancienne version à nettoyer"
    fi
}

# Fonction de rollback
rollback() {
    local current_active=$1
    
    log_error "Rollback vers $current_active..."
    
    if [ "$DRY_RUN" = true ]; then
        log_info "DRY RUN: Simulation du rollback"
        return 0
    fi
    
    # Remettre le service sur l'ancienne version
    kubectl patch service $APP_NAME -n $NAMESPACE \
        -p '{"spec":{"selector":{"version":"'$current_active'"}}}' || true
    
    # Supprimer la nouvelle version défaillante
    helm uninstall $APP_NAME-$NEW_VERSION -n $NAMESPACE || true
    
    log_error "Rollback effectué"
}

# Fonction principale
main() {
    local current_active
    
    log_step "Début du déploiement Blue-Green"
    
    # Vérifications préliminaires
    check_prerequisites
    
    # Déterminer l'environnement actuel
    current_active=$(get_current_environment)
    
    # Déployer la nouvelle version
    if ! deploy_new_version "$current_active"; then
        log_error "Échec du déploiement"
        exit 1
    fi
    
    # Vérifications de santé
    if ! run_health_checks; then
        log_error "Health checks échoués, rollback..."
        rollback "$current_active"
        exit 1
    fi
    
    # Tests de fumée
    if ! run_smoke_tests; then
        log_error "Smoke tests échoués, rollback..."
        rollback "$current_active"
        exit 1
    fi
    
    # Basculement du trafic
    if ! switch_traffic; then
        log_error "Échec du basculement, rollback..."
        rollback "$current_active"
        exit 1
    fi
    
    # Vérification du basculement
    if ! verify_traffic_switch; then
        log_error "Vérification échouée, rollback..."
        rollback "$current_active"
        exit 1
    fi
    
    # Nettoyage
    cleanup_old_version "$current_active"
    
    echo ""
    log_success "🎉 Déploiement Blue-Green terminé avec succès !"
    log_success "✅ Nouvelle version: $NEW_VERSION"
    log_success "✅ Image: $IMAGE"
    log_success "✅ Namespace: $NAMESPACE"
    log_success "✅ Zero downtime maintenu"
}

# Gestion des erreurs
trap 'log_error "Erreur inattendue à la ligne $LINENO"' ERR

# Exécution
main "$@"
